/**
 * 性能报告生成器
 * 收集和分析性能数据，生成优化建议
 */

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

interface PerformanceReport {
  summary: {
    totalMetrics: number;
    averagePageLoad: number;
    averageApiCall: number;
    cacheHitRate: number;
    performanceScore: number;
  };
  metrics: Record<string, {
    count: number;
    avg: number;
    min: number;
    max: number;
    p95: number;
  }>;
  recommendations: string[];
  issues: Array<{
    type: 'warning' | 'error';
    message: string;
    metric: string;
    value: number;
  }>;
}

class PerformanceReporter {
  private metrics: PerformanceMetric[] = [];
  private maxMetrics = 1000; // 最大保存的指标数量

  /**
   * 添加性能指标
   */
  addMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);
    
    // 限制内存使用
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }
  }

  /**
   * 生成性能报告
   */
  generateReport(): PerformanceReport {
    const metricGroups = this.groupMetricsByName();
    const summary = this.calculateSummary(metricGroups);
    const recommendations = this.generateRecommendations(summary, metricGroups);
    const issues = this.detectIssues(metricGroups);

    return {
      summary,
      metrics: metricGroups,
      recommendations,
      issues,
    };
  }

  /**
   * 按名称分组指标
   */
  private groupMetricsByName(): Record<string, {
    count: number;
    avg: number;
    min: number;
    max: number;
    p95: number;
  }> {
    const groups: Record<string, number[]> = {};

    // 分组收集数据
    this.metrics.forEach(metric => {
      if (!groups[metric.name]) {
        groups[metric.name] = [];
      }
      groups[metric.name].push(metric.value);
    });

    // 计算统计信息
    const result: Record<string, any> = {};
    Object.entries(groups).forEach(([name, values]) => {
      values.sort((a, b) => a - b);
      const sum = values.reduce((a, b) => a + b, 0);
      
      result[name] = {
        count: values.length,
        avg: sum / values.length,
        min: values[0],
        max: values[values.length - 1],
        p95: values[Math.floor(values.length * 0.95)] || values[values.length - 1],
      };
    });

    return result;
  }

  /**
   * 计算总体摘要
   */
  private calculateSummary(metricGroups: Record<string, any>): PerformanceReport['summary'] {
    const pageLoadMetrics = Object.entries(metricGroups)
      .filter(([name]) => name.includes('page_load'))
      .map(([, data]) => data.avg);

    const apiMetrics = Object.entries(metricGroups)
      .filter(([name]) => name.startsWith('api_'))
      .map(([, data]) => data.avg);

    const averagePageLoad = pageLoadMetrics.length > 0 
      ? pageLoadMetrics.reduce((a, b) => a + b, 0) / pageLoadMetrics.length 
      : 0;

    const averageApiCall = apiMetrics.length > 0 
      ? apiMetrics.reduce((a, b) => a + b, 0) / apiMetrics.length 
      : 0;

    // 计算性能分数 (0-100)
    const performanceScore = this.calculatePerformanceScore(averagePageLoad, averageApiCall);

    return {
      totalMetrics: this.metrics.length,
      averagePageLoad,
      averageApiCall,
      cacheHitRate: 0, // 需要从缓存系统获取
      performanceScore,
    };
  }

  /**
   * 计算性能分数
   */
  private calculatePerformanceScore(pageLoad: number, apiCall: number): number {
    let score = 100;

    // 页面加载时间评分
    if (pageLoad > 2000) score -= 30;
    else if (pageLoad > 1000) score -= 15;
    else if (pageLoad > 500) score -= 5;

    // API调用时间评分
    if (apiCall > 1000) score -= 25;
    else if (apiCall > 500) score -= 10;
    else if (apiCall > 200) score -= 5;

    return Math.max(0, score);
  }

  /**
   * 生成优化建议
   */
  private generateRecommendations(
    summary: PerformanceReport['summary'], 
    metricGroups: Record<string, any>
  ): string[] {
    const recommendations: string[] = [];

    // 页面加载时间建议
    if (summary.averagePageLoad > 1000) {
      recommendations.push('页面加载时间超过1秒，建议优化关键渲染路径');
      recommendations.push('考虑实施代码分割和懒加载');
    }

    // API调用建议
    if (summary.averageApiCall > 500) {
      recommendations.push('API调用时间较长，建议优化后端响应时间');
      recommendations.push('考虑实施更积极的缓存策略');
    }

    // 检查是否有频繁的API调用
    const apiCallCounts = Object.entries(metricGroups)
      .filter(([name]) => name.startsWith('api_'))
      .map(([name, data]) => ({ name, count: data.count }))
      .sort((a, b) => b.count - a.count);

    if (apiCallCounts.length > 0 && apiCallCounts[0].count > 10) {
      recommendations.push(`${apiCallCounts[0].name} 调用频繁，建议增加缓存`);
    }

    // 性能分数建议
    if (summary.performanceScore < 70) {
      recommendations.push('整体性能分数较低，建议进行全面性能优化');
    }

    return recommendations;
  }

  /**
   * 检测性能问题
   */
  private detectIssues(metricGroups: Record<string, any>): PerformanceReport['issues'] {
    const issues: PerformanceReport['issues'] = [];

    Object.entries(metricGroups).forEach(([name, data]) => {
      // 检测异常慢的操作
      if (name.includes('page_load') && data.avg > 2000) {
        issues.push({
          type: 'error',
          message: '页面加载时间过长',
          metric: name,
          value: data.avg,
        });
      }

      if (name.startsWith('api_') && data.avg > 1000) {
        issues.push({
          type: 'error',
          message: 'API调用时间过长',
          metric: name,
          value: data.avg,
        });
      }

      // 检测性能波动
      if (data.max > data.avg * 3) {
        issues.push({
          type: 'warning',
          message: '性能波动较大',
          metric: name,
          value: data.max - data.avg,
        });
      }
    });

    return issues;
  }

  /**
   * 导出报告为JSON
   */
  exportReport(): string {
    const report = this.generateReport();
    return JSON.stringify(report, null, 2);
  }

  /**
   * 清空指标数据
   */
  clearMetrics(): void {
    this.metrics = [];
  }

  /**
   * 获取最近的指标
   */
  getRecentMetrics(minutes: number = 5): PerformanceMetric[] {
    const cutoff = Date.now() - (minutes * 60 * 1000);
    return this.metrics.filter(metric => metric.timestamp > cutoff);
  }
}

// 创建全局性能报告器实例
export const globalPerformanceReporter = new PerformanceReporter();

export default PerformanceReporter;
