"use client";

interface TimeFilterProps {
  value: string;
  onChange: (value: string) => void;
}

const TIME_FILTER_OPTIONS = [
  { value: "all", label: "全部时间" },
  { value: "week", label: "最近一周" },
  { value: "half_month", label: "最近半月" },
  { value: "month", label: "最近一月" },
  { value: "half_year", label: "最近半年" },
  { value: "year", label: "最近一年" },
];

export default function TimeFilter({ value, onChange }: TimeFilterProps) {
  return (
    <div className="flex items-center flex-nowrap mr-1">
      <label
        htmlFor="time-filter-select"
        className="mr-1 text-sm font-medium whitespace-nowrap text-gray-700 dark:text-white"
      >
        更新时间:
      </label>
      <select
        id="time-filter-select"
        aria-label="选择时间范围"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="bg-white dark:bg-[#2c2c34] border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-white rounded-md px-1 py-1.5 text-sm min-w-[100px]"
      >
        {TIME_FILTER_OPTIONS.map((option) => (
          <option
            key={option.value}
            value={option.value}
            className="text-gray-700 dark:text-white"
          >
            {option.label}
          </option>
        ))}
      </select>
    </div>
  );
}
