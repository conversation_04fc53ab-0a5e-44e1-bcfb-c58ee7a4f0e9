import { tokenManager } from "../utils/tokenManager";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

// API请求配置接口
export interface ApiRequestConfig {
  method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  headers?: Record<string, string>;
  body?: string;
  skipTokenRefresh?: boolean; // 跳过自动Token刷新（用于刷新Token的请求）
}

// API响应接口
export interface ApiResponse<T = any> {
  status: string;
  data?: T;
  message?: string;
  error?: string;
}

// 获取认证token
function getAuthToken(): string | null {
  if (typeof window === "undefined") return null;
  return localStorage.getItem("auth_token");
}

// 通用API请求函数
export async function apiRequest<T = any>(
  endpoint: string,
  config: ApiRequestConfig = {}
): Promise<T> {
  const {
    method = "GET",
    headers = {},
    body,
    skipTokenRefresh = false,
  } = config;

  // 如果不跳过Token刷新，先检查并刷新Token
  if (!skipTokenRefresh && typeof window !== "undefined") {
    try {
      await tokenManager.autoRefreshToken();
    } catch (error) {
      console.warn("Token刷新失败，继续使用当前Token:", error);
    }
  }

  // 设置默认headers
  const defaultHeaders: Record<string, string> = {
    "Content-Type": "application/json",
    Accept: "application/json",
  };

  // 添加认证token
  const token = getAuthToken();
  if (token) {
    defaultHeaders["Authorization"] = `Bearer ${token}`;
  }

  // 合并headers
  const finalHeaders = { ...defaultHeaders, ...headers };

  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method,
      headers: finalHeaders,
      body,
    });

    // 检查响应状态
    if (!response.ok) {
      // 如果是401错误且不是刷新Token的请求，尝试刷新Token后重试
      if (
        response.status === 401 &&
        !skipTokenRefresh &&
        !endpoint.includes("/auth/refresh")
      ) {
        console.log("🔄 收到401错误，尝试刷新Token后重试...");

        try {
          const refreshSuccess = await tokenManager.autoRefreshToken();
          if (refreshSuccess) {
            // 重新获取Token并重试请求
            const newToken = getAuthToken();
            if (newToken) {
              finalHeaders["Authorization"] = `Bearer ${newToken}`;

              const retryResponse = await fetch(`${API_BASE_URL}${endpoint}`, {
                method,
                headers: finalHeaders,
                body,
              });

              if (retryResponse.ok) {
                const retryData = await retryResponse.json();
                return retryData;
              }
            }
          }
        } catch (refreshError) {
          console.error("Token刷新重试失败:", refreshError);
        }
      }

      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message || `HTTP ${response.status}: ${response.statusText}`
      );
    }

    // 解析响应数据
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("API请求失败:", error);
    throw error;
  }
}

// 便捷方法
export const api = {
  get: <T = any>(endpoint: string, headers?: Record<string, string>) =>
    apiRequest<T>(endpoint, { method: "GET", headers }),

  post: <T = any>(
    endpoint: string,
    data?: any,
    headers?: Record<string, string>
  ) =>
    apiRequest<T>(endpoint, {
      method: "POST",
      body: data ? JSON.stringify(data) : undefined,
      headers,
    }),

  put: <T = any>(
    endpoint: string,
    data?: any,
    headers?: Record<string, string>
  ) =>
    apiRequest<T>(endpoint, {
      method: "PUT",
      body: data ? JSON.stringify(data) : undefined,
      headers,
    }),

  delete: <T = any>(endpoint: string, headers?: Record<string, string>) =>
    apiRequest<T>(endpoint, { method: "DELETE", headers }),

  patch: <T = any>(
    endpoint: string,
    data?: any,
    headers?: Record<string, string>
  ) =>
    apiRequest<T>(endpoint, {
      method: "PATCH",
      body: data ? JSON.stringify(data) : undefined,
      headers,
    }),
};
