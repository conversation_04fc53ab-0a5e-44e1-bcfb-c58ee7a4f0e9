'use client';

import { create } from 'zustand';

interface LoadingState {
    isLoading: boolean;
    currentProcessingId: string | null;
    setLoading: (loading: boolean, resourceId?: string) => void;
}

export const useLoadingStore = create<LoadingState>((set) => ({
    isLoading: false,
    currentProcessingId: null,
    setLoading: (loading, resourceId) => set({
        isLoading: loading,
        currentProcessingId: loading ? resourceId || null : null
    }),
})); 