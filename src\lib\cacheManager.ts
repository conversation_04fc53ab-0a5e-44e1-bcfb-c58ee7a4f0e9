/**
 * 高级缓存管理器
 * 提供多层缓存策略，包括内存缓存、会话存储和本地存储
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
  accessCount: number;
  lastAccessed: number;
}

interface CacheConfig {
  maxMemorySize: number; // 内存缓存最大条目数
  maxStorageSize: number; // 存储缓存最大条目数
  defaultTTL: number; // 默认缓存时间（毫秒）
  enablePersistence: boolean; // 是否启用持久化存储
  enableCompression: boolean; // 是否启用压缩（大数据）
}

type CacheLevel = "memory" | "session" | "local";

class CacheManager {
  private memoryCache = new Map<string, CacheEntry<any>>();
  private config: CacheConfig;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxMemorySize: 100,
      maxStorageSize: 50,
      defaultTTL: 5 * 60 * 1000, // 5分钟
      enablePersistence: true,
      enableCompression: false,
      ...config,
    };
  }

  /**
   * 获取缓存数据
   */
  get<T>(
    key: string,
    levels: CacheLevel[] = ["memory", "session", "local"]
  ): T | null {
    // 按优先级检查各级缓存
    for (const level of levels) {
      const entry = this.getFromLevel<T>(key, level);
      if (entry) {
        // 更新访问统计
        entry.accessCount++;
        entry.lastAccessed = Date.now();

        // 如果是从存储中获取的，提升到内存缓存
        if (
          level !== "memory" &&
          this.memoryCache.size < this.config.maxMemorySize
        ) {
          this.memoryCache.set(key, entry);
        }

        return entry.data;
      }
    }

    return null;
  }

  /**
   * 设置缓存数据
   */
  set<T>(
    key: string,
    data: T,
    ttl: number = this.config.defaultTTL,
    levels: CacheLevel[] = ["memory", "session"]
  ): void {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      expiresAt: Date.now() + ttl,
      accessCount: 1,
      lastAccessed: Date.now(),
    };

    // 设置到指定的缓存级别
    levels.forEach((level) => {
      this.setToLevel(key, entry, level);
    });

    // 清理过期缓存
    this.cleanup();
  }

  /**
   * 从指定级别获取缓存
   */
  private getFromLevel<T>(
    key: string,
    level: CacheLevel
  ): CacheEntry<T> | null {
    try {
      switch (level) {
        case "memory":
          const memEntry = this.memoryCache.get(key);
          if (memEntry && Date.now() < memEntry.expiresAt) {
            return memEntry;
          }
          if (memEntry) {
            this.memoryCache.delete(key);
          }
          return null;

        case "session":
          if (typeof window === "undefined") return null;
          const sessionData = sessionStorage.getItem(`cache_${key}`);
          if (sessionData) {
            const entry = JSON.parse(sessionData);
            if (Date.now() < entry.expiresAt) {
              return entry;
            }
            sessionStorage.removeItem(`cache_${key}`);
          }
          return null;

        case "local":
          if (typeof window === "undefined" || !this.config.enablePersistence)
            return null;
          const localData = localStorage.getItem(`cache_${key}`);
          if (localData) {
            const entry = JSON.parse(localData);
            if (Date.now() < entry.expiresAt) {
              return entry;
            }
            localStorage.removeItem(`cache_${key}`);
          }
          return null;

        default:
          return null;
      }
    } catch {
      return null;
    }
  }

  /**
   * 设置到指定级别
   */
  private setToLevel<T>(
    key: string,
    entry: CacheEntry<T>,
    level: CacheLevel
  ): void {
    try {
      switch (level) {
        case "memory":
          // 内存缓存大小限制
          if (this.memoryCache.size >= this.config.maxMemorySize) {
            this.evictLeastUsed();
          }
          this.memoryCache.set(key, entry);
          break;

        case "session":
          if (typeof window !== "undefined") {
            sessionStorage.setItem(`cache_${key}`, JSON.stringify(entry));
          }
          break;

        case "local":
          if (typeof window !== "undefined" && this.config.enablePersistence) {
            // 检查存储空间
            this.cleanupStorage("local");
            localStorage.setItem(`cache_${key}`, JSON.stringify(entry));
          }
          break;
      }
    } catch {
      // 忽略缓存设置失败
    }
  }

  /**
   * 清理过期缓存
   */
  private cleanup(): void {
    const now = Date.now();

    // 清理内存缓存
    for (const [key, entry] of this.memoryCache.entries()) {
      if (now > entry.expiresAt) {
        this.memoryCache.delete(key);
      }
    }

    // 清理存储缓存（定期执行）
    if (Math.random() < 0.1) {
      // 10%概率执行
      this.cleanupStorage("session");
      this.cleanupStorage("local");
    }
  }

  /**
   * 清理存储缓存
   */
  private cleanupStorage(type: "session" | "local"): void {
    if (typeof window === "undefined") return;

    const storage = type === "session" ? sessionStorage : localStorage;
    const now = Date.now();
    const keysToRemove: string[] = [];

    // 收集过期的键
    for (let i = 0; i < storage.length; i++) {
      const key = storage.key(i);
      if (key?.startsWith("cache_")) {
        try {
          const data = storage.getItem(key);
          if (data) {
            const entry = JSON.parse(data);
            if (now > entry.expiresAt) {
              keysToRemove.push(key);
            }
          }
        } catch {
          keysToRemove.push(key);
        }
      }
    }

    // 删除过期的键
    keysToRemove.forEach((key) => storage.removeItem(key));
  }

  /**
   * 驱逐最少使用的缓存项
   */
  private evictLeastUsed(): void {
    let leastUsedKey = "";
    let leastUsedScore = Infinity;

    for (const [key, entry] of this.memoryCache.entries()) {
      // 计算使用分数（访问次数 / 时间差）
      const timeDiff = Date.now() - entry.timestamp;
      const score = entry.accessCount / (timeDiff + 1);

      if (score < leastUsedScore) {
        leastUsedScore = score;
        leastUsedKey = key;
      }
    }

    if (leastUsedKey) {
      this.memoryCache.delete(leastUsedKey);
    }
  }

  /**
   * 删除缓存
   */
  remove(
    key: string,
    levels: CacheLevel[] = ["memory", "session", "local"]
  ): void {
    levels.forEach((level) => {
      switch (level) {
        case "memory":
          this.memoryCache.delete(key);
          break;
        case "session":
          if (typeof window !== "undefined") {
            sessionStorage.removeItem(`cache_${key}`);
          }
          break;
        case "local":
          if (typeof window !== "undefined") {
            localStorage.removeItem(`cache_${key}`);
          }
          break;
      }
    });
  }

  /**
   * 清空所有缓存
   */
  clear(levels: CacheLevel[] = ["memory", "session", "local"]): void {
    levels.forEach((level) => {
      switch (level) {
        case "memory":
          this.memoryCache.clear();
          break;
        case "session":
          if (typeof window !== "undefined") {
            const keysToRemove: string[] = [];
            for (let i = 0; i < sessionStorage.length; i++) {
              const key = sessionStorage.key(i);
              if (key?.startsWith("cache_")) {
                keysToRemove.push(key);
              }
            }
            keysToRemove.forEach((key) => sessionStorage.removeItem(key));
          }
          break;
        case "local":
          if (typeof window !== "undefined") {
            const keysToRemove: string[] = [];
            for (let i = 0; i < localStorage.length; i++) {
              const key = localStorage.key(i);
              if (key?.startsWith("cache_")) {
                keysToRemove.push(key);
              }
            }
            keysToRemove.forEach((key) => localStorage.removeItem(key));
          }
          break;
      }
    });
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    return {
      memorySize: this.memoryCache.size,
      memoryMaxSize: this.config.maxMemorySize,
      memoryUsage:
        ((this.memoryCache.size / this.config.maxMemorySize) * 100).toFixed(2) +
        "%",
    };
  }
}

// 创建全局缓存管理器实例
export const globalCacheManager = new CacheManager({
  maxMemorySize: 100,
  maxStorageSize: 50,
  defaultTTL: 5 * 60 * 1000,
  enablePersistence: true,
  enableCompression: false,
});

export default CacheManager;
