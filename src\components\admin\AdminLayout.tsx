"use client";

import { ReactNode } from "react";
import AdminNavigation from "./AdminNavigation";
import AdminSidebar from "./AdminSidebar";
import { AdminProvider, useAdminSidebarContext } from "@/contexts/AdminContext";
import { useMounted } from "@/hooks/use-mounted";
import { Bars3Icon } from "@heroicons/react/24/outline";

interface AdminLayoutProps {
  children: ReactNode;
}

/**
 * 管理后台内部布局组件
 * 使用AdminContext中的状态
 */
function AdminLayoutContent({ children }: AdminLayoutProps) {
  const sidebarState = useAdminSidebarContext();

  return (
    <div className="min-h-screen bg-background">
      {/* 管理后台专用导航栏 */}
      <AdminNavigation />

      {/* 主体布局容器 - 为导航栏留出空间，修复移动端高度问题 */}
      <div className="pt-12 md:pt-14 h-screen overflow-hidden">
        <div className="flex h-[calc(100vh-3rem)] md:h-[calc(100vh-3.5rem)]">
          {/* 左侧侧边栏 */}
          <AdminSidebar />

          {/* 右侧主内容区域 - 修复移动端滚动问题 */}
          <div
            className={`flex-1 flex flex-col transition-all duration-300 relative z-10 overflow-hidden ${
              sidebarState.isOpen && !sidebarState.isMobile
                ? sidebarState.isCollapsed
                  ? "ml-16"
                  : "ml-[30px]"
                : "ml-0"
            }`}
          >
            {/* 移动端菜单按钮 */}
            {sidebarState.isMobile && (
              <div className="flex-shrink-0 flex items-center justify-between p-3 md:p-4 border-b border-border-color bg-card-background relative z-20">
                <div className="flex items-center">
                  <button
                    type="button"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      console.log("📱 AdminLayout: 移动端菜单按钮被点击", {
                        currentIsOpen: sidebarState.isOpen,
                        screenWidth: window.innerWidth,
                      });
                      sidebarState.toggleSidebar();
                    }}
                    className="admin-sidebar-toggle p-2 rounded-lg hover:bg-hover-background transition-colors mr-3"
                    aria-label="打开菜单"
                  >
                    <Bars3Icon className="h-5 w-5 text-foreground" />
                  </button>
                  <div className="text-lg md:text-xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-purple-600">
                    管理后台
                  </div>
                </div>
              </div>
            )}

            {/* 主内容区域 - 修复移动端滚动和内容显示问题 */}
            <main className="flex-1 overflow-auto relative z-10 bg-background admin-main-content">
              <div className="pr-4 py-2 md:pr-6 md:py-4 lg:pr-8 lg:py-6 min-h-full">
                {children}
              </div>
            </main>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * 管理后台布局组件
 * 提供AdminProvider包装
 */
export default function AdminLayout({ children }: AdminLayoutProps) {
  const mounted = useMounted();

  if (!mounted) {
    return null;
  }

  return (
    <AdminProvider>
      <AdminLayoutContent>{children}</AdminLayoutContent>
    </AdminProvider>
  );
}
