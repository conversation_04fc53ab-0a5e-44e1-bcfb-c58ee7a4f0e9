'use client';

import { ChartBarIcon, PlusCircleIcon } from '@heroicons/react/24/outline';

interface ResourceStatsProps {
    totalResources: number;
    yesterday?: number;
}

export default function ResourceStats({ totalResources, yesterday = 0 }: ResourceStatsProps) {
    return (
        <div className="bg-white dark:bg-[#242428] rounded-lg shadow-sm p-3 border border-gray-100 dark:border-[#35363a]">
            <div className="grid grid-cols-2 gap-3">
                <div className="flex items-center p-2 rounded-lg bg-blue-50 dark:bg-blue-900/20">
                    <ChartBarIcon className="h-6 w-6 text-blue-500 dark:text-blue-400 mr-2" />
                    <div>
                        <p className="text-xs font-medium text-[#5c5c66] dark:text-[#b0b3b8]">资源总数</p>
                        <p className="text-lg font-bold text-[#18181c] dark:text-[#f5f6fa]">{totalResources.toLocaleString()}</p>
                    </div>
                </div>

                <div className="flex items-center p-2 rounded-lg bg-green-50 dark:bg-green-900/20">
                    <PlusCircleIcon className="h-6 w-6 text-green-500 dark:text-green-400 mr-2" />
                    <div>
                        <p className="text-xs font-medium text-[#5c5c66] dark:text-[#b0b3b8]">昨日新增</p>
                        <p className="text-lg font-bold text-[#18181c] dark:text-[#f5f6fa]">{yesterday.toLocaleString()}</p>
                    </div>
                </div>
            </div>
        </div>
    );
} 