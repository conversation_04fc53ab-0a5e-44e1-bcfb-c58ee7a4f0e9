import {
  ResourceStatsResponse,
  DashboardStats,
  ApiResponse,
} from "@/types/admin";

/**
 * 获取仪表盘统计数据
 */
export async function getDashboardStats(): Promise<
  ApiResponse<DashboardStats>
> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return {
        success: false,
        message: "未登录",
        error: "需要管理员权限",
      };
    }

    // 并行获取资源统计和用户统计数据
    const [resourceResponse, userResponse] = await Promise.all([
      fetch("/api/admin/dashboard", {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }),
      fetch("/api/admin/users?page=1&size=1", {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }),
    ]);

    // 处理资源统计数据
    const resourceData: ResourceStatsResponse = await resourceResponse.json();
    if (!resourceResponse.ok) {
      return {
        success: false,
        message: resourceData.message || "获取资源统计数据失败",
        error: resourceData.message,
      };
    }

    // 处理用户统计数据
    const userData = await userResponse.json();
    let totalUsers = 0;
    if (
      userResponse.ok &&
      userData.success &&
      userData.data?.pagination?.total
    ) {
      totalUsers = userData.data.pagination.total;
    }

    // 转换API数据为仪表盘数据格式
    const dashboardStats: DashboardStats = {
      // 从API获取的真实数据
      totalResources: resourceData.data?.total || 0,
      yesterdayResources: resourceData.data?.yesterday || 0,
      adminUploadedResources: resourceData.data?.by_source?.admin_uploaded || 0,
      userSubmittedResources: resourceData.data?.by_source?.user_submitted || 0,
      totalUsers: totalUsers,
      pendingTasks: resourceData.data?.pending_tasks || 0,
    };

    return {
      success: true,
      message: "获取仪表盘数据成功",
      data: dashboardStats,
    };
  } catch (error) {
    console.error("获取仪表盘数据失败:", error);
    return {
      success: false,
      message: "网络错误，请稍后重试",
      error: error instanceof Error ? error.message : "未知错误",
    };
  }
}
