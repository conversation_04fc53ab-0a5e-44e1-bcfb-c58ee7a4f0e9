"use client";

import { memo } from "react";
import Link from "next/link";
import Image from "next/image";

interface ResourceCardHeaderProps {
  name: string;
  type: string;
  resourceId?: string;
  isLocal?: boolean;
}

/**
 * 获取网盘类型图标
 */
const getPanTypeIcon = (type: string): string | null => {
  switch (type) {
    case "百度网盘":
      return "/images/baidupan.png";
    case "夸克网盘":
      return "/images/quark.png";
    case "阿里云盘":
      return "/images/alipan.png";
    case "迅雷网盘":
      return "/images/xunlei.png";
    default:
      return null;
  }
};

/**
 * 资源卡片头部组件
 * 用于展示资源标题和类型标签
 */
const ResourceCardHeader = memo(function ResourceCardHeader({
  name,
  type,
  resourceId,
  isLocal,
}: ResourceCardHeaderProps) {
  const panIcon = getPanTypeIcon(type);
  return (
    <div className="flex justify-between items-start mb-2">
      <h3
        className="text-lg font-semibold leading-tight text-gray-900 dark:text-white flex-1 mr-2 break-all"
        title={name}
      >
        <span className="cursor-pointer hover:text-blue-500 dark:hover:text-blue-400 transition-colors border-b border-foreground/80 dark:border-secondary-text pb-0.1 inline-block">
          <Link
            href={`/resources/${resourceId}`}
            target="_blank"
            rel="noopener noreferrer"
          >
            {name}
          </Link>
        </span>
      </h3>
      <div className="flex items-center gap-2">
        {isLocal && (
          <span className="text-[11px] font-medium px-2 py-0.5 rounded-sm bg-gray-100 text-gray-600 pan-type-label">
            本地
          </span>
        )}
        {type && type.toLowerCase() !== "other" && (
          <span className="text-[11px] font-medium px-2 py-0.5 rounded-sm flex items-center gap-1 bg-gray-100 text-gray-700 border border-gray-200 dark:border-gray-400 pan-type-label">
            {panIcon && (
              <Image
                src={panIcon}
                alt={`${type}图标`}
                width={12}
                height={12}
                className="flex-shrink-0"
              />
            )}
            {type}
          </span>
        )}
      </div>
    </div>
  );
});

export default ResourceCardHeader;
