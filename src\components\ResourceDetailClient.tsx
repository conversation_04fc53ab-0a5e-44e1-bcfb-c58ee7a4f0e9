"use client";
import {
  checkResourceStatus,
  getValidResources,
  ApiResource,
} from "@/services/resourceService";
import { ResourceDetail } from "@/types/resource";
import React, {
  useEffect,
  useState,
  useCallback,
  useMemo,
  useRef,
} from "react";
import { useRouter } from "next/navigation";
import SearchBar from "@/components/SearchBar";
import { formatDate } from "@/lib/utils";
import Link from "next/link";
import Image from "next/image";
import {
  DocumentDuplicateIcon,
  ArrowTopRightOnSquareIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from "@heroicons/react/24/outline";
import { ResourceStructuredData } from "@/components/ResourceStructuredData";
import InvalidResourceFeedback from "@/components/InvalidResourceFeedback";
import { useResourceStatusCache } from "@/hooks/useResourceCache";
import { useResourceDetailPerformance } from "@/hooks/usePerformanceMonitor";
import { useToast } from "@/components/ToastProvider";
import PublisherInfo from "@/components/PublisherInfo";
import { useSearch } from "@/hooks/useSearch";
import { useResourceDetailLinkHandler } from "@/hooks/useResourceDetailLinkHandler";
import { SEARCH_CONFIG } from "@/config/constants";

const getPanTypeName = (panType: number): string => {
  switch (panType) {
    case 1:
      return "百度网盘";
    case 2:
      return "夸克网盘";
    case 3:
      return "阿里云盘";
    case 4:
      return "迅雷网盘";
    default:
      return "其他网盘";
  }
};

const getPanTypeIcon = (panType: number): string | null => {
  switch (panType) {
    case 1:
      return "/images/baidupan.png";
    case 2:
      return "/images/quark.png";
    case 3:
      return "/images/alipan.png";
    case 4:
      return "/images/xunlei.png";
    default:
      return null;
  }
};

const getFileTypeName = (fileType?: string): string => {
  if (!fileType) return "其他";

  switch (fileType.toLowerCase()) {
    case "video":
      return "视频";
    case "audio":
      return "音频";
    case "image":
      return "图片";
    case "document":
      return "文档";
    case "archive":
      return "压缩包";
    case "application":
      return "应用";
    default:
      return "其他";
  }
};

interface ResourceDetailClientProps {
  resource: ResourceDetail;
}

export default function ResourceDetailClient({
  resource,
}: ResourceDetailClientProps) {
  const router = useRouter();
  const { showToast } = useToast();

  const { searchType } = useSearch({
    initialType: SEARCH_CONFIG.defaultType,
  });

  // 使用缓存和性能监控
  const statusCache = useResourceStatusCache();
  const { measureApiCall } = useResourceDetailPerformance();

  // 使用 useRef 来存储函数引用，避免依赖问题
  const statusCacheRef = useRef(statusCache);
  const measureApiCallRef = useRef(measureApiCall);
  const showToastRef = useRef(showToast);

  // 更新 ref 的当前值
  statusCacheRef.current = statusCache;
  measureApiCallRef.current = measureApiCall;
  showToastRef.current = showToast;

  const [currentUrl, setCurrentUrl] = useState("");
  const [linkStatus, setLinkStatus] = useState({
    valid: false,
    message: "",
    checking: true,
  });
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);

  // 异步数据状态
  const [similarResources, setSimilarResources] = useState<ApiResource[]>([]);
  const [userResources, setUserResources] = useState<ApiResource[]>([]);
  const [isLoadingSimilar, setIsLoadingSimilar] = useState(true);
  const [isLoadingUser, setIsLoadingUser] = useState(true);

  // 异步加载相似资源
  const loadSimilarResources = useCallback(async () => {
    try {
      setIsLoadingSimilar(true);
      const similarData = await measureApiCallRef.current(
        "load_similar_resources",
        () => getValidResources(resource.title, resource.pan_type, 10, 1),
        {
          resourceKey: resource.resource_key,
          context: "similar_resources",
        }
      );

      // 过滤掉当前资源
      const filteredSimilar = similarData.filter(
        (item: ApiResource) => item.resource_id !== resource.resource_key
      );
      setSimilarResources(filteredSimilar);
    } catch (error) {
      console.error("Failed to load similar resources:", error);
      setSimilarResources([]);
    } finally {
      setIsLoadingSimilar(false);
    }
  }, [resource.title, resource.pan_type, resource.resource_key]);

  // 异步加载用户资源
  const loadUserResources = useCallback(async () => {
    try {
      setIsLoadingUser(true);
      const userData = await measureApiCallRef.current(
        "load_user_resources",
        () =>
          getValidResources(
            "",
            null,
            10,
            1,
            resource.author || "",
            "created_at"
          ),
        {
          resourceKey: resource.resource_key,
          context: "user_resources",
        }
      );

      // 过滤掉当前资源
      const filteredUser = userData.filter(
        (item: ApiResource) => item.resource_id !== resource.resource_key
      );
      setUserResources(filteredUser);
    } catch (error) {
      console.error("Failed to load user resources:", error);
      setUserResources([]);
    } finally {
      setIsLoadingUser(false);
    }
  }, [resource.author, resource.resource_key]);

  // 使用useMemo优化计算，避免不必要的重新计算
  const memoizedSimilarResources = useMemo(
    () => similarResources,
    [similarResources]
  );

  const memoizedUserResources = useMemo(() => userResources, [userResources]);

  const handleSearch = useCallback(
    (query: string, type: "local" | "online") => {
      if (query.trim()) {
        router.push(`/search?q=${encodeURIComponent(query)}&type=${type}`);
      }
    },
    [router]
  );

  const copyToClipboard = useCallback(
    (text: string, displayMessage: string) => {
      // 尝试使用现代剪贴板API
      const copyWithClipboardAPI = async (): Promise<boolean> => {
        try {
          await navigator.clipboard.writeText(text);
          showToast(displayMessage, "success");
          return true;
        } catch {
          return false;
        }
      };

      // 回退到execCommand
      const copyWithExecCommand = (): boolean => {
        try {
          const textArea = document.createElement("textarea");
          textArea.value = text;
          textArea.style.position = "fixed";
          textArea.style.left = "-999999px";
          textArea.style.top = "-999999px";
          document.body.appendChild(textArea);
          textArea.focus();
          textArea.select();
          const success = document.execCommand("copy");
          document.body.removeChild(textArea);
          if (success) {
            showToast(displayMessage, "success");
            return true;
          }
          return false;
        } catch {
          return false;
        }
      };

      // 显示手动复制对话框
      const showManualCopyDialog = () => {
        // 创建弹窗容器
        const modalContainer = document.createElement("div");
        modalContainer.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 9999;
        `;

        // 创建弹窗内容
        const modalContent = document.createElement("div");
        modalContent.style.cssText = `
          background-color: white;
          padding: 24px;
          border-radius: 12px;
          max-width: 90%;
          width: 500px;
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        `;

        // 检查暗色模式
        const isDarkMode = document.documentElement.classList.contains("dark");
        if (isDarkMode) {
          modalContent.style.backgroundColor = "#1f2937";
          modalContent.style.color = "white";
        }

        // 创建标题
        const title = document.createElement("h3");
        title.textContent = "无法自动复制，请手动复制链接";
        title.style.cssText = `
          margin: 0 0 16px 0;
          font-size: 18px;
          font-weight: 600;
          color: ${isDarkMode ? "#f9fafb" : "#111827"};
        `;

        // 创建输入框
        const input = document.createElement("input");
        input.type = "text";
        input.value = text;
        input.readOnly = true;
        input.style.cssText = `
          width: 100%;
          padding: 12px;
          border: 2px solid ${isDarkMode ? "#374151" : "#d1d5db"};
          border-radius: 8px;
          font-size: 14px;
          margin-bottom: 16px;
          background-color: ${isDarkMode ? "#374151" : "#f9fafb"};
          color: ${isDarkMode ? "#f9fafb" : "#111827"};
          box-sizing: border-box;
        `;

        // 创建按钮容器
        const buttonContainer = document.createElement("div");
        buttonContainer.style.cssText = `
          display: flex;
          gap: 12px;
          justify-content: flex-end;
        `;

        // 创建复制按钮
        const copyButton = document.createElement("button");
        copyButton.textContent = "复制";
        copyButton.style.cssText = `
          padding: 8px 16px;
          background-color: #3b82f6;
          color: white;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
        `;

        // 创建关闭按钮
        const closeButton = document.createElement("button");
        closeButton.textContent = "关闭";
        closeButton.style.cssText = `
          padding: 8px 16px;
          background-color: ${isDarkMode ? "#4b5563" : "#f3f4f6"};
          color: ${isDarkMode ? "#f9fafb" : "#374151"};
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
        `;

        // 组装弹窗
        buttonContainer.appendChild(copyButton);
        buttonContainer.appendChild(closeButton);
        modalContent.appendChild(title);
        modalContent.appendChild(input);
        modalContent.appendChild(buttonContainer);
        modalContainer.appendChild(modalContent);
        document.body.appendChild(modalContainer);

        // 选中输入框内容
        input.focus();
        input.select();

        // 复制按钮点击事件
        copyButton.onclick = () => {
          input.select();
          try {
            document.execCommand("copy");
            showToast("链接已复制到剪贴板", "success");
          } catch {
            showToast("请手动选择并复制链接", "info");
          }
        };

        // 关闭按钮点击事件
        const closeModal = () => {
          if (document.body.contains(modalContainer)) {
            document.body.removeChild(modalContainer);
          }
        };

        closeButton.onclick = closeModal;

        // 点击背景关闭
        modalContainer.onclick = (e) => {
          if (e.target === modalContainer) {
            closeModal();
          }
        };

        // ESC键关闭
        const handleKeyDown = (e: KeyboardEvent) => {
          if (e.key === "Escape") {
            closeModal();
            document.removeEventListener("keydown", handleKeyDown);
          }
        };
        document.addEventListener("keydown", handleKeyDown);

        // 自动关闭
        setTimeout(() => {
          closeModal();
          document.removeEventListener("keydown", handleKeyDown);
        }, 30000);
      };

      // 尝试多种复制方法，按照优先级
      (async () => {
        // 尝试方法1: 使用Clipboard API
        const clipboardSuccess = await copyWithClipboardAPI();
        if (clipboardSuccess) return;

        // 尝试方法2: 使用execCommand
        const execCommandSuccess = copyWithExecCommand();
        if (execCommandSuccess) return;

        // 如果都失败，显示手动复制界面
        showManualCopyDialog();
      })();
    },
    [showToast]
  );

  // 使用新的资源详情链接处理Hook
  const { handleLinkAction, isLinkLoading, hasError, errorMessage } =
    useResourceDetailLinkHandler({
      resource,
      copyToClipboard,
      linkStatus,
    });

  useEffect(() => {
    setCurrentUrl(window.location.href);

    const checkStatus = async () => {
      setLinkStatus((prev) => ({ ...prev, checking: true }));

      // 使用缓存和性能监控
      const cacheKey = `${resource.resource_key}_${resource.pan_type}`;
      let status = statusCacheRef.current.get(cacheKey);

      if (!status) {
        status = await measureApiCallRef.current(
          "initial_check_resource_status",
          () => checkResourceStatus(resource.resource_key, resource.pan_type),
          {
            resourceKey: resource.resource_key,
            panType: resource.pan_type,
            context: "initial_load",
          }
        );

        // 缓存有效的状态结果
        if (status.valid) {
          statusCacheRef.current.set(cacheKey, status);
        }
      }

      setLinkStatus({ ...status, checking: false });

      if (!status.valid) {
        showToastRef.current(
          "检测到资源检测已失效,已自动删除。",
          "error",
          4000
        );
      }
    };

    if (resource) {
      checkStatus();
    }
  }, [resource]); // 只依赖 resource，避免函数引用导致的无限循环

  // 异步加载相似资源和用户资源
  useEffect(() => {
    if (resource) {
      // 延迟加载相似资源，避免阻塞主要内容
      setTimeout(() => {
        loadSimilarResources();
      }, 100);

      // 延迟加载用户资源，进一步优化性能
      setTimeout(() => {
        loadUserResources();
      }, 200);
    }
  }, [resource, loadSimilarResources, loadUserResources]);

  return (
    <>
      {showFeedbackModal && (
        <InvalidResourceFeedback
          isOpen={showFeedbackModal}
          resourceId={resource.resource_key}
          resourceName={resource.title}
          panType={resource.pan_type}
          onClose={() => setShowFeedbackModal(false)}
          onFeedbackResult={(status, message) => {
            showToast(message, status === "success" ? "success" : "error");
          }}
        />
      )}
      <div className="min-h-screen py-4 sm:py-6 lg:py-8">
        {resource && currentUrl && (
          <ResourceStructuredData resource={resource} currentUrl={currentUrl} />
        )}
        <header className="container mx-auto">
          <div className="px-4 py-4">
            <SearchBar
              onSearch={handleSearch}
              placeholder="换个关键词试试"
              initialSearchType={searchType}
            />
          </div>
        </header>
        <main className="container mx-auto p-4 mt-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column */}
            <div className="lg:col-span-2 space-y-6">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
                <h1 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
                  {resource.title}
                </h1>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-3 text-sm text-gray-600 dark:text-gray-400">
                  <div className="col-span-full">
                    <span className="font-semibold">资源预览:</span>{" "}
                    <p className="mt-1 dark:text-gray-300 whitespace-pre-line">
                      {resource.text_content}
                    </p>
                  </div>
                  <div>
                    <span className="font-semibold">分享时间:</span>{" "}
                    <span className="dark:text-white">
                      {formatDate(resource.updated_at)}
                    </span>
                  </div>
                  <div>
                    <span className="font-semibold">入库时间:</span>{" "}
                    <span className="dark:text-white">
                      {formatDate(resource.created_at)}
                    </span>
                  </div>
                  <div>
                    <span className="font-semibold">状态检测:</span>
                    {linkStatus.checking ? (
                      <span className="text-yellow-500 font-semibold animate-pulse ml-1">
                        检测中...
                      </span>
                    ) : linkStatus.valid ? (
                      <span className="text-green-500 font-semibold ml-1 inline-flex items-center">
                        <CheckCircleIcon className="w-4 h-4 mr-1" />
                        有效
                      </span>
                    ) : (
                      <span className="text-red-500 font-semibold ml-1 inline-flex items-center">
                        <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                        {linkStatus.message || "失效"}
                      </span>
                    )}
                  </div>
                  <div>
                    <span className="font-semibold">文件类型:</span>{" "}
                    <span className="dark:text-white">
                      {getFileTypeName(resource.file_type)}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-semibold">网盘类型:</span>
                    <span className="dark:text-white inline-flex items-center gap-1">
                      {getPanTypeIcon(resource.pan_type) && (
                        <Image
                          src={getPanTypeIcon(resource.pan_type)!}
                          alt={`${getPanTypeName(resource.pan_type)}图标`}
                          width={16}
                          height={16}
                          className="flex-shrink-0"
                        />
                      )}
                      {getPanTypeName(resource.pan_type)}
                    </span>
                  </div>
                  <div>
                    <span className="font-semibold">资源有问题❓:</span>{" "}
                    <button
                      type="button"
                      onClick={() => setShowFeedbackModal(true)}
                      className="text-[var(--link-color)] hover:underline hover:text-[var(--button-hover)]"
                    >
                      点此反馈
                    </button>
                  </div>
                </div>
                <div className="mt-6 flex flex-wrap items-center gap-4">
                  <button
                    type="button"
                    onClick={() => handleLinkAction("open")}
                    disabled={isLinkLoading}
                    className={`flex items-center justify-center gap-2 px-4 py-2 rounded-sm transition-colors disabled:opacity-70 disabled:cursor-not-allowed ${
                      hasError
                        ? "bg-red-500 hover:bg-red-600 text-white"
                        : "bg-[var(--button-background)] hover:bg-[var(--button-hover)] text-white"
                    }`}
                  >
                    {isLinkLoading ? (
                      <>
                        <div className="h-5 w-5 border-t-2 border-white rounded-full animate-spin"></div>
                        <span>资源校验中</span>
                      </>
                    ) : (
                      <>
                        <ArrowTopRightOnSquareIcon className="w-5 h-5" />
                        <span>进入网盘</span>
                      </>
                    )}
                  </button>
                  <button
                    type="button"
                    onClick={() => handleLinkAction("copy")}
                    disabled={isLinkLoading}
                    className={`flex items-center justify-center gap-2 px-4 py-2 rounded-sm transition-colors disabled:opacity-70 disabled:cursor-not-allowed ${
                      hasError
                        ? "bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900 dark:text-red-100 dark:hover:bg-red-800"
                        : "bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                    } dark-text-white`}
                  >
                    {isLinkLoading ? (
                      <>
                        <div className="h-5 w-5 border-t-2 border-gray-500 rounded-full animate-spin"></div>
                        <span>资源校验中</span>
                      </>
                    ) : (
                      <>
                        <DocumentDuplicateIcon className="w-5 h-5" />
                        <span>复制链接</span>
                      </>
                    )}
                  </button>
                </div>

                {/* 错误状态显示 */}
                {hasError && (
                  <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg dark:bg-red-900/20 dark:border-red-800">
                    <div className="flex items-center gap-2">
                      <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
                      <span className="text-red-700 dark:text-red-300 text-sm">
                        {errorMessage || "资源处理出错"}
                      </span>
                    </div>
                  </div>
                )}
              </div>

              <div
                className="bg-[var(--card-background)] border-l-4 border-[var(--border-color)] text-[var(--secondary-text)] p-4 rounded-r-lg"
                role="alert"
              >
                <p>
                  本站数据均来自于互联网，如果你发现某个资源存在违规，可发送邮件到客服邮箱:{" "}
                  <a
                    href="mailto:<EMAIL>"
                    className="text-[var(--link-color)] hover:underline hover:text-[var(--button-hover)]"
                  >
                    <EMAIL>
                  </a>{" "}
                  提交举报信息
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
                <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                  相似推荐
                </h2>
                {isLoadingSimilar ? (
                  <div className="flex items-center justify-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                    <span className="ml-2 text-gray-500 dark:text-gray-400">
                      加载中...
                    </span>
                  </div>
                ) : memoizedSimilarResources.length > 0 ? (
                  <ul className="space-y-2">
                    {memoizedSimilarResources.map((item) => (
                      <li key={item.resource_id}>
                        <Link
                          href={`/resources/${item.resource_id}`}
                          className="text-blue-500 hover:underline"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          {item.title}
                        </Link>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-gray-500 dark:text-gray-400">
                    暂无相似推荐
                  </p>
                )}
              </div>
            </div>

            {/* Right Column */}
            <div className="lg:col-span-1 space-y-6">
              <PublisherInfo
                author={resource.author || "97_bot"}
                authorAvatar={resource.author_avatar}
              />

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
                <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                  用户最近更新
                </h2>
                {isLoadingUser ? (
                  <div className="flex items-center justify-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                    <span className="ml-2 text-gray-500 dark:text-gray-400">
                      加载中...
                    </span>
                  </div>
                ) : memoizedUserResources.length > 0 ? (
                  <ul className="space-y-2">
                    {memoizedUserResources.map((item) => (
                      <li key={item.resource_id}>
                        <Link
                          href={`/resources/${item.resource_id}`}
                          className="text-blue-500 hover:underline"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          {item.title}
                        </Link>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-gray-500 dark:text-gray-400">
                    暂无该用户的其它资源
                  </p>
                )}
              </div>
            </div>
          </div>
        </main>
      </div>
    </>
  );
}
