"use client";

import React, { useState, useEffect, useRef } from "react";
import { useResourceDetailPerformance } from "@/hooks/usePerformanceMonitor";
import { globalCacheManager } from "@/lib/cacheManager";

interface PerformanceMonitorProps {
  enabled?: boolean;
  position?: "top-right" | "top-left" | "bottom-right" | "bottom-left";
  showDetails?: boolean;
}

/**
 * 性能监控组件
 * 在开发环境中显示性能指标
 */
const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  enabled = process.env.NODE_ENV === "development",
  position = "top-right",
  showDetails = false,
}) => {
  const { getMetricsSummary } = useResourceDetailPerformance();
  const [isVisible, setIsVisible] = useState(false);
  const [metrics, setMetrics] = useState<any>({});
  const [cacheStats, setCacheStats] = useState<any>({});

  // 使用 useRef 来存储最新的函数引用，避免依赖问题
  const getMetricsSummaryRef = useRef(getMetricsSummary);
  getMetricsSummaryRef.current = getMetricsSummary;

  useEffect(() => {
    if (!enabled) return;

    const updateMetrics = () => {
      const newMetrics = getMetricsSummaryRef.current();
      const newCacheStats = globalCacheManager.getStats();

      // 使用 JSON.stringify 进行简单的深度比较，避免不必要的状态更新
      setMetrics((prevMetrics: any) => {
        const newMetricsStr = JSON.stringify(newMetrics);
        const prevMetricsStr = JSON.stringify(prevMetrics);
        return newMetricsStr !== prevMetricsStr ? newMetrics : prevMetrics;
      });

      setCacheStats((prevCacheStats: any) => {
        const newCacheStatsStr = JSON.stringify(newCacheStats);
        const prevCacheStatsStr = JSON.stringify(prevCacheStats);
        return newCacheStatsStr !== prevCacheStatsStr
          ? newCacheStats
          : prevCacheStats;
      });
    };

    // 初始更新
    updateMetrics();

    // 定期更新
    const interval = setInterval(updateMetrics, 2000);

    return () => clearInterval(interval);
  }, [enabled]); // 只依赖 enabled

  if (!enabled) return null;

  const positionClasses = {
    "top-right": "top-4 right-4",
    "top-left": "top-4 left-4",
    "bottom-right": "bottom-4 right-4",
    "bottom-left": "bottom-4 left-4",
  };

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const getPerformanceColor = (
    value: number,
    thresholds: { good: number; warning: number }
  ) => {
    if (value <= thresholds.good) return "text-green-600";
    if (value <= thresholds.warning) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <div className={`fixed ${positionClasses[position]} z-50`}>
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3 text-xs font-mono">
        {/* 切换按钮 */}
        <button
          onClick={() => setIsVisible(!isVisible)}
          className="w-full text-left font-semibold text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 mb-2"
        >
          📊 Performance {isVisible ? "▼" : "▶"}
        </button>

        {isVisible && (
          <div className="space-y-2 min-w-[200px]">
            {/* 页面加载时间 */}
            {metrics.page_load && (
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">
                  Page Load:
                </span>
                <span
                  className={getPerformanceColor(metrics.page_load.avg, {
                    good: 500,
                    warning: 1000,
                  })}
                >
                  {formatTime(metrics.page_load.avg)}
                </span>
              </div>
            )}

            {/* API调用时间 */}
            {Object.entries(metrics)
              .filter(([key]) => key.startsWith("api_"))
              .map(([key, data]: [string, any]) => (
                <div key={key} className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400 truncate">
                    {key.replace("api_", "").replace("_", " ")}:
                  </span>
                  <span
                    className={getPerformanceColor(data.avg, {
                      good: 200,
                      warning: 500,
                    })}
                  >
                    {formatTime(data.avg)}
                  </span>
                </div>
              ))}

            {/* 组件渲染时间 */}
            {Object.entries(metrics)
              .filter(([key]) => key.startsWith("render_"))
              .map(([key, data]: [string, any]) => (
                <div key={key} className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400 truncate">
                    {key.replace("render_", "").replace("_", " ")}:
                  </span>
                  <span
                    className={getPerformanceColor(data.avg, {
                      good: 50,
                      warning: 100,
                    })}
                  >
                    {formatTime(data.avg)}
                  </span>
                </div>
              ))}

            {/* 缓存统计 */}
            <div className="border-t border-gray-200 dark:border-gray-600 pt-2 mt-2">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">
                  Cache Usage:
                </span>
                <span className="text-blue-600 dark:text-blue-400">
                  {cacheStats.memoryUsage}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">
                  Cache Size:
                </span>
                <span className="text-blue-600 dark:text-blue-400">
                  {cacheStats.memorySize}/{cacheStats.memoryMaxSize}
                </span>
              </div>
            </div>

            {/* 详细信息 */}
            {showDetails && (
              <div className="border-t border-gray-200 dark:border-gray-600 pt-2 mt-2">
                <div className="text-gray-500 dark:text-gray-500 text-xs">
                  <div>Total Metrics: {Object.keys(metrics).length}</div>
                  <div>
                    Memory:{" "}
                    {(
                      (performance as any).memory?.usedJSHeapSize /
                        1024 /
                        1024 || 0
                    ).toFixed(1)}
                    MB
                  </div>
                </div>
              </div>
            )}

            {/* 性能建议 */}
            {metrics.page_load?.avg > 1000 && (
              <div className="border-t border-gray-200 dark:border-gray-600 pt-2 mt-2">
                <div className="text-red-600 dark:text-red-400 text-xs">
                  ⚠️ Slow page load detected
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default PerformanceMonitor;
