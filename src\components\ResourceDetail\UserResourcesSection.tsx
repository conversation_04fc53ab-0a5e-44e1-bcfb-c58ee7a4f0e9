"use client";

import React, { memo } from "react";
import Link from "next/link";
import { ApiResource } from "@/services/resourceService";

interface UserResourcesSectionProps {
  resources: ApiResource[];
  currentResourceKey: string;
}

/**
 * 用户其他资源区域组件
 * 使用memo优化，避免不必要的重渲染
 */
const UserResourcesSection = memo(function UserResourcesSection({
  resources,
  currentResourceKey,
}: UserResourcesSectionProps) {
  // 过滤掉当前资源
  const filteredResources = resources.filter(
    (item) => item.resource_id !== currentResourceKey
  );

  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
      <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
        用户最近更新
      </h2>
      {filteredResources.length > 0 ? (
        <ul className="space-y-2">
          {filteredResources.map((item) => (
            <li key={item.resource_id}>
              <Link
                href={`/resources/${item.resource_id}`}
                className="text-blue-500 hover:underline"
                target="_blank"
                rel="noopener noreferrer"
              >
                {item.title}
              </Link>
            </li>
          ))}
        </ul>
      ) : (
        <p className="text-gray-500 dark:text-gray-400">暂无该用户的其它资源</p>
      )}
    </div>
  );
});

export default UserResourcesSection;
