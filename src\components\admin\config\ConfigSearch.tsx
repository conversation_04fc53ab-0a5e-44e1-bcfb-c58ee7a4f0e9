"use client";

import { useState, useCallback, useEffect } from "react";
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  PencilIcon,
  InformationCircleIcon,
  EyeIcon,
  EyeSlashIcon,
} from "@heroicons/react/24/outline";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { ConfigSearchResult } from "@/types/config";
import { formatConfigValue } from "@/services/configService";

interface ConfigSearchProps {
  onSearch: (query: string) => void;
  results: ConfigSearchResult[];
  loading?: boolean;
  onResultSelect?: (result: ConfigSearchResult) => void;
  onResultEdit?: (result: ConfigSearchResult) => void;
  showSensitive?: boolean;
  onToggleSensitive?: () => void;
  className?: string;
}

export default function ConfigSearch({
  onSearch,
  results,
  loading = false,
  onResultSelect,
  onResultEdit,
  showSensitive = false,
  onToggleSensitive,
  className = "",
}: ConfigSearchProps) {
  const [query, setQuery] = useState("");
  const [searchHistory, setSearchHistory] = useState<string[]>([]);

  // 从localStorage加载搜索历史
  useEffect(() => {
    const saved = localStorage.getItem("config-search-history");
    if (saved) {
      try {
        setSearchHistory(JSON.parse(saved));
      } catch {
        // 忽略搜索历史加载失败
      }
    }
  }, []);

  // 保存搜索历史到localStorage
  const saveSearchHistory = useCallback((newHistory: string[]) => {
    try {
      localStorage.setItem("config-search-history", JSON.stringify(newHistory));
    } catch {
      // 忽略搜索历史保存失败
    }
  }, []);

  const handleSearch = useCallback(() => {
    if (!query.trim()) return;

    onSearch(query.trim());

    // 更新搜索历史
    const newHistory = [
      query.trim(),
      ...searchHistory.filter((h) => h !== query.trim()),
    ].slice(0, 10);
    setSearchHistory(newHistory);
    saveSearchHistory(newHistory);
  }, [query, onSearch, searchHistory, saveSearchHistory]);

  const handleKeyPress = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter") {
        handleSearch();
      }
    },
    [handleSearch]
  );

  const handleClear = useCallback(() => {
    setQuery("");
  }, []);

  const handleHistoryClick = useCallback(
    (historyQuery: string) => {
      setQuery(historyQuery);
      onSearch(historyQuery);
    },
    [onSearch]
  );

  const handleResultSelect = useCallback(
    (result: ConfigSearchResult) => {
      onResultSelect?.(result);
    },
    [onResultSelect]
  );

  const handleResultEdit = useCallback(
    (e: React.MouseEvent, result: ConfigSearchResult) => {
      e.stopPropagation();
      onResultEdit?.(result);
    },
    [onResultEdit]
  );

  return (
    <div
      className={`config-search bg-card-background rounded-lg border border-border-color ${className}`}
    >
      {/* 搜索输入框 */}
      <div className="search-input-section p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3">
          <div className="flex-1 relative">
            <Input
              type="text"
              placeholder="搜索配置项（支持路径、键名、注释）..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              className="pl-10 pr-10"
            />
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            {query && (
              <button
                type="button"
                onClick={handleClear}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                title="清除搜索"
              >
                <XMarkIcon className="w-4 h-4" />
              </button>
            )}
          </div>

          <Button
            onClick={handleSearch}
            disabled={!query.trim() || loading}
            className="px-4 py-2"
          >
            {loading ? "搜索中..." : "搜索"}
          </Button>

          {onToggleSensitive && (
            <button
              type="button"
              onClick={onToggleSensitive}
              className="flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              title={showSensitive ? "隐藏敏感信息" : "显示敏感信息"}
            >
              {showSensitive ? (
                <EyeSlashIcon className="w-4 h-4" />
              ) : (
                <EyeIcon className="w-4 h-4" />
              )}
            </button>
          )}
        </div>

        {/* 搜索历史 */}
        {searchHistory.length > 0 && !query && (
          <div className="mt-3">
            <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">
              搜索历史:
            </div>
            <div className="flex flex-wrap gap-2">
              {searchHistory.slice(0, 5).map((historyQuery, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={() => handleHistoryClick(historyQuery)}
                  className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  {historyQuery}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 搜索结果 */}
      <div className="search-results-section flex-1 overflow-auto max-h-[calc(100vh-300px)]">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600 dark:text-gray-400">
              搜索中...
            </span>
          </div>
        ) : results.length > 0 ? (
          <div className="search-results">
            <div className="p-3 border-b border-gray-200 dark:border-gray-700">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                找到 {results.length} 个配置项
              </span>
            </div>

            <div className="results-list">
              {results.map((result, index) => (
                <div
                  key={index}
                  className="result-item p-4 border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer transition-colors"
                  onClick={() => handleResultSelect(result)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      {/* 配置键名 */}
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium text-gray-900 dark:text-gray-100 truncate">
                          {result.key}
                        </h4>

                        {/* 敏感信息标识 */}
                        {result.sensitive && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                            敏感
                          </span>
                        )}

                        {/* 类型标识 */}
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-100">
                          {result.type}
                        </span>
                      </div>

                      {/* 配置值 */}
                      <div className="text-sm text-gray-600 dark:text-gray-200 mb-1">
                        <span className="font-mono">
                          {formatConfigValue(result.value, result.type)}
                        </span>
                      </div>

                      {/* 注释 */}
                      {result.comment && (
                        <div className="text-sm text-gray-500 dark:text-gray-300 italic mb-1">
                          {result.comment}
                        </div>
                      )}

                      {/* 路径 */}
                      <div className="text-xs text-gray-400 dark:text-gray-500 font-mono">
                        {result.path}
                      </div>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex items-center space-x-1 ml-3">
                      <button
                        type="button"
                        onClick={(e) => handleResultEdit(e, result)}
                        className="p-1.5 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                        title="编辑配置"
                      >
                        <PencilIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : query ? (
          <div className="flex items-center justify-center py-8 text-gray-500 dark:text-gray-400">
            <InformationCircleIcon className="w-5 h-5 mr-2" />
            未找到匹配的配置项
          </div>
        ) : (
          <div className="flex items-center justify-center py-8 text-gray-500 dark:text-gray-400">
            <MagnifyingGlassIcon className="w-5 h-5 mr-2" />
            输入关键词搜索配置项
          </div>
        )}
      </div>
    </div>
  );
}
