import { useState, useEffect } from "react";

interface AdminSidebarState {
  isOpen: boolean;
  isCollapsed: boolean;
  isMobile: boolean;
}

interface AdminSidebarActions {
  toggleSidebar: () => void;
  toggleCollapse: () => void;
  closeSidebar: () => void;
  openSidebar: () => void;
  setMobile: (isMobile: boolean) => void;
}

const STORAGE_KEY = "admin-sidebar-collapsed";

export function useAdminSidebar(): AdminSidebarState & AdminSidebarActions {
  const [isOpen, setIsOpen] = useState(true);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 从localStorage恢复折叠状态
  useEffect(() => {
    const savedCollapsed = localStorage.getItem(STORAGE_KEY);
    if (savedCollapsed !== null) {
      setIsCollapsed(JSON.parse(savedCollapsed));
    }
  }, []);

  // 保存折叠状态到localStorage
  useEffect(() => {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(isCollapsed));
  }, [isCollapsed]);

  // 检测屏幕尺寸变化
  useEffect(() => {
    const checkMobile = () => {
      const width = window.innerWidth;
      const mobile = width < 768;

      // 设备类型检测
      let deviceType = "desktop";
      if (width < 480) deviceType = "mobile-small";
      else if (width < 768) deviceType = "mobile-large";
      else if (width < 1024) deviceType = "tablet";

      console.log("📱 useAdminSidebar: 设备检测", {
        width,
        deviceType,
        isMobile: mobile,
        previousMobile: isMobile,
      });

      setIsMobile(mobile);

      // 移动端默认关闭侧边栏，桌面端默认打开
      if (mobile) {
        setIsOpen(false);
      } else {
        setIsOpen(true);
      }
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, [isMobile]);

  // 移动端点击外部区域关闭侧边栏
  useEffect(() => {
    if (!isMobile || !isOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;

      // 检查点击的元素是否在侧边栏外部
      if (
        !target.closest(".admin-sidebar") &&
        !target.closest(".admin-sidebar-toggle")
      ) {
        console.log("🔄 useAdminSidebar: 点击外部区域，关闭侧边栏");
        setIsOpen(false);
      }
    };

    // 延迟添加事件监听器，避免立即触发
    const timer = setTimeout(() => {
      document.addEventListener("click", handleClickOutside);
    }, 100);

    return () => {
      clearTimeout(timer);
      document.removeEventListener("click", handleClickOutside);
    };
  }, [isMobile, isOpen]);

  const toggleSidebar = () => {
    console.log("🔄 useAdminSidebar: toggleSidebar 被调用", {
      currentIsOpen: isOpen,
      newIsOpen: !isOpen,
      isMobile,
    });
    setIsOpen(!isOpen);
  };

  const toggleCollapse = () => {
    // 只在桌面端允许折叠
    if (!isMobile) {
      console.log("🔄 useAdminSidebar: toggleCollapse 被调用", {
        currentIsCollapsed: isCollapsed,
        newIsCollapsed: !isCollapsed,
        isMobile,
      });
      setIsCollapsed(!isCollapsed);
    } else {
      console.log("⚠️ useAdminSidebar: 移动端不允许折叠操作", { isMobile });
    }
  };

  const closeSidebar = () => {
    setIsOpen(false);
  };

  const openSidebar = () => {
    setIsOpen(true);
  };

  const setMobile = (mobile: boolean) => {
    setIsMobile(mobile);
  };

  return {
    isOpen,
    isCollapsed,
    isMobile,
    toggleSidebar,
    toggleCollapse,
    closeSidebar,
    openSidebar,
    setMobile,
  };
}
