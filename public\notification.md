# 公告管理说明

## 公告配置文件

公告配置存储在 `public/notification.yaml` 文件中，格式如下：

```yaml
# 是否启用公告（true/false）
enabled: True

# 公告标题
title: 系统公告

# 公告内容（支持换行和简单格式）
content: |
  欢迎使用我们的网盘搜索服务！
  
  请遵守相关法律法规，不要传播违法违规内容。
  所有资源均来自互联网，如有侵权，请联系删除。
  如果在使用过程中遇到问题，请及时联系我们。
  邮箱: <EMAIL>
  感谢您的支持！

# 公告显示时间（格式：YYYY-MM-DD，为空则永久有效）
start_time: 2023-10-01
end_time: 2024-12-31

# 重置键（用于强制用户再次看到公告）
reset_key: "20230601"  # 可以是任何唯一值，建议使用日期或时间戳
```

## 管理公告

### 方法1：直接编辑配置文件

直接编辑 `public/notification.yaml` 文件，修改相应的值。

### 方法2：使用管理脚本

我们提供了一个简单的管理脚本，可以快速启用或禁用公告：

1. **启用公告**

```bash
node scripts/update-notification.js enable "公告标题" "公告内容" 2023-10-01 2024-12-31
```

参数说明：
- `enable`: 启用公告
- `"公告标题"`: 公告标题（可选，如不提供则使用当前值）
- `"公告内容"`: 公告内容（可选，如不提供则使用当前值）
- `2023-10-01`: 开始日期（可选，如不提供则无开始日期限制）
- `2024-12-31`: 结束日期（可选，如不提供则无结束日期限制）

2. **禁用公告**

```bash
node scripts/update-notification.js disable
```

## 公告显示规则

1. 公告仅在 `enabled` 为 `true` 时显示
2. 如果设置了 `start_time` 或 `end_time`，公告仅在有效期内显示
3. 用户关闭公告后，将不会再次显示（除非清除浏览器本地存储）

## 清除用户的"已读"标记

如需强制用户再次看到公告（即使他们已经关闭过），可以通过以下方式：

1. 在公告内容中明确说明这是一个新的、重要的公告
2. 或者修改 `PublicNotification.tsx` 文件中的存储键名（将 `notification_dismissed` 改为其他值）

## 重置公告（强制所有用户再次看到）

如果您修改了公告内容，并希望所有用户再次看到公告（即使他们之前已经关闭过），可以使用重置功能：

### 方法1：使用重置脚本

```bash
node scripts/reset-notification.js
```

这个脚本会自动更新公告配置文件中的 `reset_key`，使所有用户在下次访问网站时再次看到公告。

### 方法2：手动修改 reset_key

在 `public/notification.yaml` 文件中，添加或修改 `reset_key` 属性：

```yaml
# ... 其他配置 ...

# 重置键（用于强制用户再次看到公告）
reset_key: "20230601"  # 可以是任何唯一值，建议使用日期或时间戳
```

每次更改 `reset_key` 的值，系统会清除所有用户的"已关闭"状态，使公告再次显示。

## 调试公告

如果公告没有按预期显示，可以访问调试页面 `/debug-notification` 来诊断问题。该页面提供了以下功能：

1. 查看公告配置和状态
2. 检查用户的"已关闭"状态
3. 重置"已关闭"状态
4. 强制显示公告

您也可以在浏览器控制台中使用以下命令：

- `window.__resetNotificationDismissed()` - 重置已关闭状态
- `window.__reloadNotification()` - 重新加载公告 