'use client';

import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface PageContainerProps {
    children: ReactNode;
    className?: string;
    fullWidth?: boolean;
}

/**
 * 页面容器组件
 * 用于统一页面布局结构，提供一致的页面边距和最大宽度
 */
export function PageContainer({
    children,
    className,
    fullWidth = false
}: PageContainerProps) {
    return (
        <div
            className={cn(
                "mx-auto px-4 py-6",
                fullWidth ? 'w-full' : 'container',
                className
            )}
        >
            {children}
        </div>
    );
} 