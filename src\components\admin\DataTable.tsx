"use client";

import { ReactNode } from "react";
import { Button } from "@/components/ui/Button";

export interface Column<T> {
  key: keyof T | string;
  title: string;
  render?: (value: any, record: T, index: number) => ReactNode;
  width?: string;
  align?: "left" | "center" | "right";
  sortable?: boolean;
}

export interface DataTableProps<T> {
  columns: Column<T>[];
  data: T[];
  loading?: boolean;
  pagination?: {
    current: number;
    total: number;
    pageSize: number;
    onChange: (page: number) => void;
  };
  rowKey?: keyof T | ((record: T) => string | number);
  onRow?: (
    record: T,
    index: number
  ) => {
    onClick?: () => void;
    className?: string;
  };
  rowSelection?: {
    selectedRowKeys: (string | number)[];
    onChange: (selectedRowKeys: (string | number)[]) => void;
  };
  emptyText?: string;
  className?: string;
}

export default function DataTable<T extends Record<string, any>>({
  columns,
  data,
  loading = false,
  pagination,
  rowKey = "id" as keyof T,
  onRow,
  rowSelection,
  emptyText = "暂无数据",
  className = "",
}: DataTableProps<T>) {
  const getRowKey = (record: T, index: number): string | number => {
    if (typeof rowKey === "function") {
      return rowKey(record);
    }
    return record[rowKey] || index;
  };

  const getValue = (record: T, key: keyof T | string): any => {
    if (typeof key === "string" && key.includes(".")) {
      // 支持嵌套属性，如 "user.name"
      return key.split(".").reduce((obj, k) => obj?.[k], record);
    }
    return record[key as keyof T];
  };

  if (loading) {
    return (
      <div
        className={`bg-white/80 backdrop-blur-xl border border-gray-200/50 rounded-xl shadow-lg ${className}`}
      >
        <div className="px-6 py-12 text-center">
          <div className="animate-spin rounded-full h-10 w-10 border-2 border-gray-300 border-t-blue-600 mx-auto mb-6"></div>
          <p className="text-gray-600 font-medium">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`bg-white/80 backdrop-blur-xl border border-gray-200/50 rounded-xl shadow-lg h-full flex flex-col ${className}`}
    >
      <div className="flex-1 overflow-auto rounded-xl">
        {/* 移动端优化：添加水平滚动容器 */}
        <div className="min-w-full overflow-x-auto admin-table-scroll">
          <table className="w-full admin-table border-collapse">
            <thead className="bg-gray-50/80 backdrop-blur-sm sticky top-0 z-10">
              <tr>
                {rowSelection && (
                  <th className="px-6 py-4 text-center text-sm font-semibold text-gray-700 border-b border-gray-200/60 bg-gray-50/80 backdrop-blur-sm w-12">
                    <input
                      type="checkbox"
                      checked={
                        (data?.length || 0) > 0 &&
                        (rowSelection?.selectedRowKeys?.length || 0) ===
                          (data?.length || 0)
                      }
                      onChange={(e) => {
                        if (e.target.checked) {
                          rowSelection?.onChange(
                            (data || []).map((record, index) =>
                              getRowKey(record, index)
                            )
                          );
                        } else {
                          rowSelection?.onChange([]);
                        }
                      }}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      aria-label="全选"
                      title="全选"
                    />
                  </th>
                )}
                {columns.map((column, index) => (
                  <th
                    key={index}
                    className="px-6 py-4 text-center text-sm font-semibold text-gray-700 border-b border-gray-200/60 bg-gray-50/80 backdrop-blur-sm"
                    {...(column.width && { style: { width: column.width } })}
                  >
                    <div className="flex items-center justify-center h-full">
                      <span className="truncate">{column.title}</span>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white/90 backdrop-blur-sm divide-y divide-gray-200/60">
              {(data?.length || 0) === 0 ? (
                <tr>
                  <td
                    colSpan={(columns?.length || 0) + (rowSelection ? 1 : 0)}
                    className="px-4 py-12 text-center text-gray-500"
                  >
                    <div className="flex items-center justify-center h-full">
                      {emptyText}
                    </div>
                  </td>
                </tr>
              ) : (
                (data || []).map((record, index) => {
                  const rowProps = onRow?.(record, index) || {};
                  return (
                    <tr
                      key={getRowKey(record, index)}
                      className={`hover:bg-blue-50/50 transition-all duration-200 ${
                        rowProps.className || ""
                      } ${rowProps.onClick ? "cursor-pointer" : ""}`}
                      onClick={rowProps.onClick}
                    >
                      {rowSelection && (
                        <td className="px-6 py-4 text-center border-b border-gray-100/60">
                          <input
                            type="checkbox"
                            checked={(
                              rowSelection?.selectedRowKeys || []
                            ).includes(getRowKey(record, index))}
                            onChange={(e) => {
                              const key = getRowKey(record, index);
                              if (e.target.checked) {
                                rowSelection?.onChange([
                                  ...(rowSelection?.selectedRowKeys || []),
                                  key,
                                ]);
                              } else {
                                rowSelection?.onChange(
                                  (rowSelection?.selectedRowKeys || []).filter(
                                    (k) => k !== key
                                  )
                                );
                              }
                            }}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            aria-label={`选择第${index + 1}行`}
                          />
                        </td>
                      )}
                      {columns.map((column, colIndex) => {
                        const value = getValue(record, column.key);
                        return (
                          <td
                            key={colIndex}
                            className="px-6 py-4 text-center text-sm text-gray-900 border-b border-gray-100/60"
                            {...(column.width && {
                              style: { width: column.width },
                            })}
                          >
                            <div className="flex items-center justify-center h-full min-h-[44px]">
                              <div className="max-w-full overflow-hidden">
                                {column.render
                                  ? column.render(value, record, index)
                                  : value}
                              </div>
                            </div>
                          </td>
                        );
                      })}
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* 分页 - 移动端优化 */}
      {pagination && pagination.total > pagination.pageSize && (
        <div className="flex-shrink-0 px-6 py-4 border-t border-gray-200/60 bg-white/80 backdrop-blur-sm rounded-b-xl">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-3 sm:gap-4">
            <div className="text-sm font-medium text-gray-700 order-2 sm:order-1 text-center sm:text-left">
              第 {pagination.current} 页，共{" "}
              {Math.ceil(pagination.total / pagination.pageSize)} 页，总计{" "}
              {pagination.total} 条记录
            </div>
            <div className="flex space-x-1 sm:space-x-2 order-1 sm:order-2">
              <Button
                size="sm"
                variant="outline"
                disabled={pagination.current === 1}
                onClick={() => {
                  console.log("⬅️ DataTable: 上一页按钮被点击", {
                    current: pagination.current,
                  });
                  pagination.onChange(pagination.current - 1);
                }}
                className="min-h-[40px] sm:min-h-[44px] px-2 sm:px-4 text-xs sm:text-sm"
              >
                上一页
              </Button>
              <Button
                size="sm"
                variant="outline"
                disabled={
                  pagination.current >=
                  Math.ceil(pagination.total / pagination.pageSize)
                }
                onClick={() => {
                  console.log("➡️ DataTable: 下一页按钮被点击", {
                    current: pagination.current,
                  });
                  pagination.onChange(pagination.current + 1);
                }}
                className="min-h-[40px] sm:min-h-[44px] px-2 sm:px-4 text-xs sm:text-sm"
              >
                下一页
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
