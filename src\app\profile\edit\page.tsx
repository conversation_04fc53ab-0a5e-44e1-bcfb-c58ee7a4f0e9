"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import { getMyProfile } from "@/services/profileService";
import { ProfileEditForm } from "@/components/profile/ProfileEditForm";
import { AvatarUpload } from "@/components/profile/AvatarUpload";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/Button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { PageContainer } from "@/components/layout/PageContainer";
import AuthGuard from "@/components/AuthGuard";
import { ArrowLeft, AlertCircle, RefreshCw, User, Camera } from "lucide-react";

export default function ProfileEditPage() {
  const router = useRouter();
  const { user, setUser } = useAuth();

  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await getMyProfile();

      if (result.success && result.data) {
        setProfile(result.data);
      } else {
        setError(result.message || "获取个人信息失败");
      }
    } catch (err) {
      setError("网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateSuccess = (updatedProfile: any) => {
    setProfile(updatedProfile);
    // 更新全局用户状态
    if (setUser) {
      setUser(updatedProfile);
    }
  };

  const handleAvatarUploadSuccess = (newAvatarUrl: string) => {
    if (profile) {
      const updatedProfile = { ...profile, avatar: newAvatarUrl };
      setProfile(updatedProfile);
      // 更新全局用户状态
      if (setUser) {
        setUser(updatedProfile);
      }
    }
  };

  const handleCancel = () => {
    router.back();
  };

  if (loading) {
    return (
      <AuthGuard>
        <PageContainer>
          <div className="min-h-[60vh] flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-muted-foreground">加载中...</p>
            </div>
          </div>
        </PageContainer>
      </AuthGuard>
    );
  }

  if (error || !profile) {
    return (
      <AuthGuard>
        <PageContainer>
          <div className="py-8">
            <div className="mb-6">
              <Button
                variant="outline"
                onClick={() => router.back()}
                className="mb-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
              </Button>
            </div>

            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>{error || "获取个人信息失败"}</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadProfile}
                  className="ml-4"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  重试
                </Button>
              </AlertDescription>
            </Alert>
          </div>
        </PageContainer>
      </AuthGuard>
    );
  }

  return (
    <AuthGuard>
      <PageContainer>
        <div className="py-8">
          {/* 页面标题和导航 */}
          <div className="mb-8">
            <Button
              variant="outline"
              onClick={() => router.back()}
              className="mb-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回
            </Button>

            <h1 className="text-3xl font-display font-bold">编辑个人资料</h1>
            <p className="text-muted-foreground mt-2">更新您的个人信息和设置</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 左侧：头像上传 */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Camera className="h-5 w-5 mr-2" />
                    头像设置
                  </CardTitle>
                  <CardDescription>上传或更换您的头像</CardDescription>
                </CardHeader>
                <CardContent>
                  <AvatarUpload
                    currentAvatar={profile.avatar}
                    username={profile.username}
                    nickname={profile.nickname}
                    onUploadSuccess={handleAvatarUploadSuccess}
                  />
                </CardContent>
              </Card>

              {/* 账户信息 */}
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <User className="h-5 w-5 mr-2" />
                    账户信息
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      用户名
                    </label>
                    <p className="text-sm">{profile.username}</p>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      邮箱
                    </label>
                    <div className="flex items-center space-x-2">
                      <p className="text-sm">{profile.email}</p>
                      {profile.email_verified ? (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                          已验证
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                          未验证
                        </span>
                      )}
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      注册时间
                    </label>
                    <p className="text-sm">
                      {new Date(profile.created_at).toLocaleDateString(
                        "zh-CN",
                        {
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                        }
                      )}
                    </p>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      当前积分
                    </label>
                    <p className="text-sm font-semibold text-blue-600 dark:text-blue-400">
                      {profile.points} 分
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 右侧：编辑表单 */}
            <div className="lg:col-span-2">
              <ProfileEditForm
                profile={profile}
                onUpdateSuccess={handleUpdateSuccess}
                onCancel={handleCancel}
              />
            </div>
          </div>
        </div>
      </PageContainer>
    </AuthGuard>
  );
}
