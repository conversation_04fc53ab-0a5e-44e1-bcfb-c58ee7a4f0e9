/**
 * 配置管理相关的类型定义
 */

// 配置树节点
export interface ConfigTreeNode {
  key: string;
  display_name: string;
  path: string;
  type: string;
  value: any;
  comment?: string;
  children?: Record<string, ConfigTreeNode>;
  is_leaf: boolean;
  sensitive: boolean;
  required: boolean;
  validation_rules: Record<string, any>;
  effect_type: string;
}

// 配置树响应
export interface ConfigTreeResponse {
  tree: Record<string, ConfigTreeNode>;
  total_nodes: number;
  max_depth: number;
}

// 配置路径请求
export interface ConfigPathRequest {
  value: any;
  comment?: string;
}

// 配置路径响应
export interface ConfigPathResponse {
  path: string;
  value: any;
  type: string;
  comment?: string;
  sensitive: boolean;
  parent_path?: string;
  children_paths?: string[];
}

// 验证结果
export interface ValidationResult {
  valid: boolean;
  message?: string;
  errors?: string[];
}

// 配置模式响应
export interface ConfigSchemaResponse {
  schemas: Record<string, any>;
  total_schemas: number;
}

// 配置搜索结果
export interface ConfigSearchResult {
  path: string;
  key: string;
  value: any;
  type: string;
  comment?: string;
  sensitive: boolean;
}

// 配置搜索响应
export interface ConfigSearchResponse {
  status: string;
  data: {
    query: string;
    results: ConfigSearchResult[];
    total: number;
  };
}

// 配置更新响应
export interface ConfigUpdateResponse {
  status: string;
  message: string;
  data: {
    path: string;
    old_value: any;
    new_value: any;
    effect_type: string;
    comment?: string;
  };
}

// 配置效果类型
export type ConfigEffectType = 'immediate' | 'restart' | 'reload' | 'manual';

// 配置值类型
export type ConfigValueType = 'string' | 'number' | 'boolean' | 'object' | 'array' | 'unknown';
