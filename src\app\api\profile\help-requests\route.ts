import { NextRequest, NextResponse } from "next/server";

const BACKEND_API_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

export async function GET(request: NextRequest) {
  try {
    const authorization = request.headers.get("authorization");
    
    if (!authorization) {
      return NextResponse.json(
        { success: false, message: "未提供认证令牌" },
        { status: 401 }
      );
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const page = searchParams.get("page") || "1";
    const size = searchParams.get("size") || "20";
    const statusFilter = searchParams.get("status_filter");

    // 构建后端API URL
    const backendUrl = new URL(`${BACKEND_API_URL}/api/profile/help-requests`);
    backendUrl.searchParams.set("page", page);
    backendUrl.searchParams.set("size", size);
    if (statusFilter) {
      backendUrl.searchParams.set("status_filter", statusFilter);
    }

    // 转发请求到后端服务
    const response = await fetch(backendUrl.toString(), {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: authorization,
      },
    });

    // 获取后端响应数据
    const data = await response.json();

    // 如果后端返回错误状态码，保持相同的状态码
    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    // 返回成功响应
    return NextResponse.json(data);
  } catch (error) {
    console.error("获取求助列表失败:", error);
    return NextResponse.json(
      {
        success: false,
        message: "服务器处理请求时出错",
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 }
    );
  }
}
