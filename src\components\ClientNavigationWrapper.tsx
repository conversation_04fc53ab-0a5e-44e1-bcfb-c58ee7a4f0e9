"use client";
import dynamic from "next/dynamic";
import { usePathname } from "next/navigation";

const Navigation = dynamic(() => import("@/components/Navigation"), {
  ssr: false,
});

export default function ClientNavigationWrapper() {
  const pathname = usePathname();
  const isAdminRoute = pathname?.startsWith("/admin");

  // 管理后台路由不渲染主站导航栏
  if (isAdminRoute) {
    return null;
  }

  return <Navigation />;
}
