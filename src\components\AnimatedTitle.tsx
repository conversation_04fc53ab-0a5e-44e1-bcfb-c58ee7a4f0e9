'use client';

import { useEffect, useState } from 'react';

interface AnimatedTitleProps {
    text: string;
    className?: string;
}

export default function AnimatedTitle({ text, className = '' }: AnimatedTitleProps) {
    const [displayText, setDisplayText] = useState('');
    const [showCursor, setShowCursor] = useState(true);

    useEffect(() => {
        let currentIndex = 0;
        const interval = setInterval(() => {
            if (currentIndex <= text.length) {
                setDisplayText(text.substring(0, currentIndex));
                currentIndex++;
            } else {
                clearInterval(interval);
                // 继续显示光标闪烁效果
                const cursorInterval = setInterval(() => {
                    setShowCursor(prev => !prev);
                }, 500);
                return () => clearInterval(cursorInterval);
            }
        }, 150);

        return () => clearInterval(interval);
    }, [text]);

    return (
        <h1 className={`font-bold relative ${className}`}>
            <span>{displayText}</span>
            <span className={`absolute ${showCursor ? 'opacity-100' : 'opacity-0'} transition-opacity duration-100`}>|</span>
        </h1>
    );
} 