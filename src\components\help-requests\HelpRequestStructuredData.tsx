/**
 * 求助功能结构化数据组件
 * 为求助页面添加JSON-LD结构化数据，提升SEO效果
 */

import { HelpRequest } from "@/types/help-request";
import {
  generateHelpRequestStructuredData,
  generateHelpRequestListStructuredData,
  generateBreadcrumbStructuredData,
  generateSearchBoxStructuredData,
  generateOrganizationStructuredData,
} from "@/config/help-request-seo";

// 求助详情页结构化数据
export function HelpRequestStructuredData({ helpRequest }: { helpRequest: HelpRequest }) {
  const structuredData = generateHelpRequestStructuredData(helpRequest);

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}

// 求助列表页结构化数据
export function HelpRequestListStructuredData({ helpRequests }: { helpRequests: HelpRequest[] }) {
  const structuredData = generateHelpRequestListStructuredData(helpRequests);

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}

// 面包屑导航结构化数据
export function BreadcrumbStructuredData({ 
  items 
}: { 
  items: Array<{ name: string; url?: string }> 
}) {
  const structuredData = generateBreadcrumbStructuredData(items);

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}

// 网站搜索框结构化数据
export function SearchBoxStructuredData() {
  const structuredData = generateSearchBoxStructuredData();

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}

// 组织信息结构化数据
export function OrganizationStructuredData() {
  const structuredData = generateOrganizationStructuredData();

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}

// 面包屑导航组件
export function Breadcrumb({ 
  items,
  className = "",
}: { 
  items: Array<{ name: string; url?: string }>;
  className?: string;
}) {
  return (
    <>
      <BreadcrumbStructuredData items={items} />
      <nav className={`flex ${className}`} aria-label="面包屑导航">
        <ol className="inline-flex items-center space-x-1 md:space-x-3">
          {items.map((item, index) => (
            <li key={index} className="inline-flex items-center">
              {index > 0 && (
                <svg
                  className="w-3 h-3 text-gray-400 mx-1"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 6 10"
                >
                  <path
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="m1 9 4-4-4-4"
                  />
                </svg>
              )}
              {item.url ? (
                <a
                  href={item.url}
                  className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"
                >
                  {item.name}
                </a>
              ) : (
                <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">
                  {item.name}
                </span>
              )}
            </li>
          ))}
        </ol>
      </nav>
    </>
  );
}

// 求助页面完整SEO组件
export function HelpRequestPageSEO({
  helpRequest,
  breadcrumbItems,
}: {
  helpRequest?: HelpRequest;
  breadcrumbItems?: Array<{ name: string; url?: string }>;
}) {
  return (
    <>
      {/* 组织信息 */}
      <OrganizationStructuredData />
      
      {/* 搜索框 */}
      <SearchBoxStructuredData />
      
      {/* 面包屑导航 */}
      {breadcrumbItems && <BreadcrumbStructuredData items={breadcrumbItems} />}
      
      {/* 求助详情 */}
      {helpRequest && <HelpRequestStructuredData helpRequest={helpRequest} />}
    </>
  );
}

// 求助列表页面完整SEO组件
export function HelpRequestListPageSEO({
  helpRequests,
  breadcrumbItems,
}: {
  helpRequests: HelpRequest[];
  breadcrumbItems?: Array<{ name: string; url?: string }>;
}) {
  return (
    <>
      {/* 组织信息 */}
      <OrganizationStructuredData />
      
      {/* 搜索框 */}
      <SearchBoxStructuredData />
      
      {/* 面包屑导航 */}
      {breadcrumbItems && <BreadcrumbStructuredData items={breadcrumbItems} />}
      
      {/* 求助列表 */}
      {helpRequests.length > 0 && <HelpRequestListStructuredData helpRequests={helpRequests} />}
    </>
  );
}
