# 链接处理配置说明

## 概述

前端服务器现在支持通过环境变量配置来控制复制链接按钮和进入网盘按钮的行为，决定是否调用 `get_share` 接口。

## 配置选项

### 1. NEXT_PUBLIC_ENABLE_GET_SHARE_API

控制是否启用 `get_share` 接口调用。

- **类型**: `boolean`
- **默认值**: `true`
- **说明**:
  - `true`: 启用 `get_share` 接口调用（默认行为）
  - `false`: 禁用 `get_share` 接口，所有网盘类型都只调用 `check_resource_status` 接口

### 2. NEXT_PUBLIC_GET_SHARE_TYPES

指定哪些网盘类型使用 `get_share` 接口。

- **类型**: `string`（逗号分隔的列表）
- **默认值**: `"baidu,quark"`
- **可选值**: `baidu`, `quark`, `thunder`, `aliyun`
- **说明**: 只有在 `NEXT_PUBLIC_ENABLE_GET_SHARE_API=true` 时才生效

## 配置场景

### 场景 1: 完全禁用 get_share 接口

所有网盘类型的复制和进入按钮都只调用 `check_resource_status` 接口。

```env
NEXT_PUBLIC_ENABLE_GET_SHARE_API=false
```

**行为**:
- 点击"复制链接"或"进入网盘"按钮时
- 只调用 `check_resource_status` 接口
- 直接使用返回的 `share_url` 进行复制或跳转
- 不会调用 `get_share` 接口

### 场景 2: 部分网盘类型使用 get_share

只有指定的网盘类型使用 `get_share` 接口，其他类型只调用 `check_resource_status`。

```env
NEXT_PUBLIC_ENABLE_GET_SHARE_API=true
NEXT_PUBLIC_GET_SHARE_TYPES=baidu
```

**行为**:
- 百度网盘: 调用 `get_share` 接口
- 夸克网盘、迅雷网盘、阿里云盘: 只调用 `check_resource_status` 接口

### 场景 3: 所有网盘类型都使用 get_share

```env
NEXT_PUBLIC_ENABLE_GET_SHARE_API=true
NEXT_PUBLIC_GET_SHARE_TYPES=baidu,quark,thunder,aliyun
```

**行为**:
- 所有网盘类型都会调用 `get_share` 接口（原有行为）

## 技术实现

### 配置文件位置

配置定义在 `src/config/constants.ts` 中：

```typescript
export const LINK_CONFIG = {
  enableGetShareAPI: process.env.NEXT_PUBLIC_ENABLE_GET_SHARE_API !== "false",
  enableGetShareForTypes: process.env.NEXT_PUBLIC_GET_SHARE_TYPES?.split(",") || ["baidu", "quark"],
};
```

### 核心逻辑

在 `src/utils/cloudLinkUtils.ts` 的 `processCloudLink` 函数中：

```typescript
// 检查是否启用 get_share 接口
const shouldUseGetShare = LINK_CONFIG.enableGetShareAPI && 
                          LINK_CONFIG.enableGetShareForTypes.includes(type);

if (!shouldUseGetShare) {
  // 只调用 check_resource_status 接口
  const checkUrl = `/api/check_resource_status?resource_id=${idToUse}&pan_type=${panTypeMap[type]}`;
  const checkRes = await fetch(checkUrl);
  const checkData = await checkRes.json();
  
  return {
    success: true,
    url: checkData.share_url,
  };
} else {
  // 调用 get_share 接口（原有逻辑）
  const shareLinkResponse = await getShareLink(type, undefined, idToUse);
  // ...
}
```

## 后端接口要求

当禁用 `get_share` 接口时，`check_resource_status` 接口需要在响应中包含 `share_url` 字段：

```json
{
  "valid": true,
  "message": "资源有效",
  "share_url": "https://pan.baidu.com/s/1234567890",
  "share_pwd": "abcd"
}
```

## 部署配置

### 开发环境

创建 `.env.local` 文件：

```env
NEXT_PUBLIC_ENABLE_GET_SHARE_API=false
```

### 生产环境

在部署平台设置环境变量：

- Vercel: 在项目设置的 Environment Variables 中添加
- Docker: 在 docker-compose.yml 或 Dockerfile 中设置
- PM2: 在 ecosystem.config.js 中配置

## 注意事项

1. **环境变量前缀**: 所有客户端可访问的环境变量必须以 `NEXT_PUBLIC_` 开头
2. **重启要求**: 修改环境变量后需要重启应用
3. **缓存清理**: 在生产环境中可能需要清理构建缓存
4. **后端兼容性**: 确保后端 `check_resource_status` 接口返回完整的链接信息

## 故障排除

### 问题 1: 配置不生效

**解决方案**:
1. 检查环境变量名称是否正确（注意 `NEXT_PUBLIC_` 前缀）
2. 重启开发服务器或重新部署
3. 检查浏览器控制台是否有错误信息

### 问题 2: 链接获取失败

**解决方案**:
1. 检查后端 `check_resource_status` 接口是否返回 `share_url`
2. 验证网络连接和 API 地址配置
3. 查看浏览器网络面板的请求响应

### 问题 3: 部分网盘类型配置无效

**解决方案**:
1. 检查 `NEXT_PUBLIC_GET_SHARE_TYPES` 的值格式（逗号分隔，无空格）
2. 确认网盘类型名称正确：`baidu`, `quark`, `thunder`, `aliyun`
3. 验证 `NEXT_PUBLIC_ENABLE_GET_SHARE_API=true`
