"use client";

import { useState, useEffect, useCallback } from "react";
import { Button } from "@/components/ui/Button";
import { useToast } from "@/components/ToastProvider";
import {
  getUserList,
  createUser,
  updateUser,
  deleteUser,
  freezeUser,
  unfreezeUser,
  User,
  UserListResponse,
} from "@/services/authService";
import DataTable, { Column } from "@/components/admin/DataTable";
import SearchFilter, { FilterField } from "@/components/admin/SearchFilter";
import UserModal from "@/components/admin/UserModal";
import {
  PencilIcon,
  LockClosedIcon,
  LockOpenIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";

export default function UserManagement() {
  const { showToast } = useToast();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalUsers, setTotalUsers] = useState(0);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  // 当前搜索关键词状态（用于实际搜索）
  const [currentSearchTerm, setCurrentSearchTerm] = useState("");

  const loadUsers = useCallback(async () => {
    setLoading(true);
    try {
      const result: UserListResponse = await getUserList(
        currentPage,
        20,
        currentSearchTerm || undefined,
        roleFilter || undefined,
        statusFilter || undefined
      );

      if (result.success) {
        setUsers(result.data.users);
        setTotalUsers(result.data.total);
      } else {
        showToast(result.message || "获取用户列表失败", "error");
      }
    } catch {
      showToast("获取用户列表失败", "error");
    } finally {
      setLoading(false);
    }
  }, [currentPage, currentSearchTerm, roleFilter, statusFilter, showToast]);

  // 初始加载和筛选条件变化时重新加载（不包括搜索词）
  useEffect(() => {
    loadUsers();
  }, [loadUsers]);

  const handleSearch = () => {
    setCurrentSearchTerm(searchTerm);
    setCurrentPage(1);
  };

  const handleCreateUser = async (userData: any) => {
    try {
      const result = await createUser(userData);
      if (result.success) {
        showToast(result.message, "success");
        setShowCreateModal(false);
        loadUsers();
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast("创建用户失败", "error");
    }
  };

  const handleUpdateUser = async (userId: number, updates: Partial<User>) => {
    try {
      const result = await updateUser(userId, updates);
      if (result.success) {
        showToast(result.message, "success");
        setEditingUser(null);
        loadUsers();
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast("更新用户失败", "error");
    }
  };

  const handleDeleteUser = async (userId: number, username: string) => {
    if (!confirm(`确定要删除用户 "${username}" 吗？此操作不可恢复。`)) {
      return;
    }

    try {
      const result = await deleteUser(userId);
      if (result.success) {
        showToast(result.message, "success");
        loadUsers();
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast("删除用户失败", "error");
    }
  };

  const handleFreezeUser = async (
    userId: number,
    username: string,
    freeze: boolean
  ) => {
    const action = freeze ? "冻结" : "解冻";
    if (!confirm(`确定要${action}用户 "${username}" 吗？`)) {
      return;
    }

    try {
      const result = freeze
        ? await freezeUser(userId)
        : await unfreezeUser(userId);
      if (result.success) {
        showToast(result.message, "success");
        loadUsers();
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast(`${action}用户失败`, "error");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("zh-CN");
  };

  const getRoleText = (role: {
    id: string;
    name: string;
    display_name?: string;
  }) => {
    const roleMap: { [key: string]: string } = {
      admin: "管理员",
      user: "普通用户",
      moderator: "版主",
    };
    return roleMap[role.name] || role.display_name || role.name;
  };

  const getStatusText = (status: string) => {
    const statusMap: { [key: string]: string } = {
      active: "正常",
      inactive: "未激活",
      suspended: "已冻结",
      banned: "已封禁",
      deleted: "已删除",
    };
    return statusMap[status] || status;
  };

  const getStatusColor = (status: string) => {
    const colorMap: { [key: string]: string } = {
      active: "text-green-600",
      inactive: "text-yellow-600",
      suspended: "text-red-600",
      banned: "text-red-800",
      deleted: "text-gray-500",
    };
    return colorMap[status] || "text-gray-600";
  };

  const filters: FilterField[] = [
    {
      key: "role",
      label: "角色筛选",
      type: "select",
      value: roleFilter,
      onChange: setRoleFilter,
      options: [
        { label: "管理员", value: "admin" },
        { label: "版主", value: "moderator" },
        { label: "注册用户", value: "user" },
      ],
    },
    {
      key: "status",
      label: "状态筛选",
      type: "select",
      value: statusFilter,
      onChange: setStatusFilter,
      options: [
        { label: "正常", value: "active" },
        { label: "未激活", value: "inactive" },
        { label: "已冻结", value: "suspended" },
        { label: "已封禁", value: "banned" },
        { label: "已删除", value: "deleted" },
      ],
    },
  ];

  const columns: Column<User>[] = [
    {
      key: "id",
      title: "ID",
      width: "80px",
      render: (id) => (
        <span className="text-sm text-gray-900 dark:text-gray-100 font-medium">
          {id}
        </span>
      ),
    },
    {
      key: "username",
      title: "用户名",
      width: "120px",
      render: (username) => (
        <span className="text-sm text-gray-900 dark:text-gray-100 font-medium">
          {username}
        </span>
      ),
    },
    {
      key: "email",
      title: "邮箱",
      width: "180px",
      render: (email) => (
        <span className="text-sm text-gray-900 dark:text-gray-100">
          {email}
        </span>
      ),
    },
    {
      key: "role",
      title: "角色",
      width: "100px",
      render: (role) => (
        <span className="text-sm text-gray-900 dark:text-gray-100">
          {getRoleText(role)}
        </span>
      ),
    },
    {
      key: "status",
      title: "状态",
      width: "100px",
      render: (status) => (
        <span className={`text-sm ${getStatusColor(status)}`}>
          {getStatusText(status)}
        </span>
      ),
    },
    {
      key: "created_at",
      title: "注册时间",
      width: "140px",
      render: (date) => (
        <span className="text-sm text-gray-900">{formatDate(date)}</span>
      ),
    },
    {
      key: "last_login_at",
      title: "最后登录",
      width: "140px",
      render: (date) => (
        <span className="text-sm text-gray-900">
          {date ? formatDate(date) : "从未登录"}
        </span>
      ),
    },
    {
      key: "actions",
      title: "操作",
      width: "200px",
      render: (_, record) => (
        <div className="flex items-center justify-center gap-2">
          <button
            type="button"
            onClick={() => setEditingUser(record)}
            className="p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50/80 rounded-lg transition-all duration-200 backdrop-blur-sm"
            title="编辑用户"
          >
            <PencilIcon className="h-4 w-4" />
          </button>
          <button
            type="button"
            onClick={() =>
              handleFreezeUser(
                record.id,
                record.username,
                record.status === "active"
              )
            }
            className="p-2 text-orange-600 hover:text-orange-700 hover:bg-orange-50/80 rounded-lg transition-all duration-200 backdrop-blur-sm"
            title={record.status === "active" ? "冻结用户" : "解冻用户"}
          >
            {record.status === "active" ? (
              <LockClosedIcon className="h-4 w-4" />
            ) : (
              <LockOpenIcon className="h-4 w-4" />
            )}
          </button>
          <button
            type="button"
            onClick={() => handleDeleteUser(record.id, record.username)}
            className="p-2 text-red-600 hover:text-red-700 hover:bg-red-50/80 rounded-lg transition-all duration-200 backdrop-blur-sm"
            title="删除用户"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      ),
    },
  ];

  return (
    <div className="h-full flex flex-col admin-content-container">
      {/* 主内容区域 - 修复移动端滚动问题 */}
      <div className="flex-1 flex flex-col overflow-hidden bg-background">
        {/* 搜索和筛选区域 - 移动端优化 */}
        <div className="flex-shrink-0 mb-4">
          <div className="admin-form-mobile">
            <SearchFilter
              searchValue={searchTerm}
              onSearchChange={setSearchTerm}
              onSearch={handleSearch}
              filters={filters}
              actions={
                <Button
                  onClick={() => {
                    setShowCreateModal(true);
                  }}
                  size="sm"
                  className="px-4 py-1.5 text-sm"
                >
                  新增用户
                </Button>
              }
            />
          </div>
        </div>

        {/* 用户列表区域 - 修复移动端滚动和内容显示 */}
        <div className="flex-1 overflow-auto">
          <DataTable
            columns={columns}
            data={users}
            loading={loading}
            pagination={{
              current: currentPage,
              total: totalUsers,
              pageSize: 20,
              onChange: (page) => {
                setCurrentPage(page);
              },
            }}
            emptyText="暂无用户数据"
            className="admin-table-mobile"
          />
        </div>
      </div>

      {/* 创建用户模态框 */}
      {showCreateModal && (
        <UserModal
          title="创建新用户"
          onSubmit={handleCreateUser}
          onCancel={() => setShowCreateModal(false)}
        />
      )}

      {/* 编辑用户模态框 */}
      {editingUser && (
        <UserModal
          title="编辑用户"
          user={editingUser}
          onSubmit={(data: any) => handleUpdateUser(editingUser.id, data)}
          onCancel={() => setEditingUser(null)}
        />
      )}
    </div>
  );
}
