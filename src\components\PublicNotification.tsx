"use client";
import React, { useState, useEffect, useCallback } from "react";
import { Dialog } from "@headlessui/react";
import yaml from "js-yaml";
import { useToast } from "./ToastProvider";

// 为Window添加自定义属性的类型声明
declare global {
  interface Window {
    __resetNotificationDismissed: () => void;
    __reloadNotification: () => void;
    __forceShowNotification: () => void;
  }
}

interface NotificationConfig {
  enabled: boolean;
  title: string;
  content: string;
  start_time?: string;
  end_time?: string;
  reset_key?: string; // 重置键，用于强制用户再次看到公告
}

const PublicNotification: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [config, setConfig] = useState<NotificationConfig | null>(null);
  const { showToast } = useToast();

  // 用于解析日期字符串，解决不同格式问题
  const parseDate = (dateStr?: string): Date | null => {
    if (!dateStr) return null;

    try {
      // 尝试标准ISO格式
      const date = new Date(dateStr);
      // 检查是否为有效日期
      if (!isNaN(date.getTime())) return date;

      // 尝试YYYY-MM-DD格式
      if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
        const [year, month, day] = dateStr.split("-").map(Number);
        return new Date(year, month - 1, day);
      }
    } catch {
      // 解析日期出错
      return null;
    }
    return null;
  };

  // 重置已关闭状态
  const resetDismissedStatus = useCallback(() => {
    localStorage.removeItem("notification_dismissed");
    localStorage.removeItem("notification_reset_key");
    sessionStorage.removeItem("notification_seen_this_session");
    showToast("公告状态已重置", "success");
  }, [showToast]);

  useEffect(() => {
    const loadNotificationConfig = async () => {
      try {
        // 1. 获取通知配置
        const response = await fetch("/notification.yaml", {
          cache: "no-store",
        });
        if (!response.ok) {
          showToast("加载公告配置失败", "error");
          return;
        }

        const yamlText = await response.text();
        const yamlConfig = yaml.load(yamlText) as NotificationConfig;

        // 更新配置
        setConfig(yamlConfig);

        // 如果配置禁用，直接返回
        if (!yamlConfig.enabled) {
          return;
        }

        // 检查是否在有效时间范围内
        const now = new Date();
        const startDate = parseDate(yamlConfig.start_time);
        const endDate = parseDate(yamlConfig.end_time);

        // 检查是否在有效时间范围内
        if (startDate && now < startDate) {
          return; // 还未到开始时间
        }

        if (endDate && now > endDate) {
          return; // 已过结束时间
        }

        // 检查是否已经被用户关闭过
        const dismissed =
          localStorage.getItem("notification_dismissed") === "true";
        const seenThisSession =
          sessionStorage.getItem("notification_seen_this_session") === "true";

        // 如果有reset_key且与存储的不同，则忽略dismissed状态
        const storedResetKey = localStorage.getItem("notification_reset_key");
        const resetKeyChanged =
          yamlConfig.reset_key && yamlConfig.reset_key !== storedResetKey;

        // 决定是否显示公告
        const shouldShow = !seenThisSession && (!dismissed || resetKeyChanged);

        if (shouldShow) {
          // 标记本次会话已显示过公告
          sessionStorage.setItem("notification_seen_this_session", "true");
          setIsOpen(true);
        } else {
          // 公告不显示
          setIsOpen(false);
        }
      } catch (error) {
        showToast(
          `加载公告配置失败: ${
            error instanceof Error ? error.message : String(error)
          }`,
          "error"
        );
      }
    };

    // 立即加载
    loadNotificationConfig();

    // 添加调试按钮，在控制台可以手动重置和触发
    if (typeof window !== "undefined") {
      // 确保在客户端环境中
      window.__resetNotificationDismissed = resetDismissedStatus;
      window.__reloadNotification = loadNotificationConfig;
      window.__forceShowNotification = () => {
        resetDismissedStatus();
        loadNotificationConfig();
        setTimeout(() => setIsOpen(true), 500);
      };
    }

    return () => {
      if (typeof window !== "undefined") {
        // @ts-ignore
        delete window.__resetNotificationDismissed;
        // @ts-ignore
        delete window.__reloadNotification;
        // @ts-ignore
        delete window.__forceShowNotification;
      }
    };
  }, [resetDismissedStatus, showToast]);

  // 处理关闭
  const handleClose = () => {
    setIsOpen(false);
    // 记录用户已关闭公告 - 同时在本地存储和会话存储中记录
    localStorage.setItem("notification_dismissed", "true");
    sessionStorage.setItem("notification_seen_this_session", "true");
    if (config?.reset_key) {
      localStorage.setItem("notification_reset_key", config.reset_key);
    }
  };

  return (
    <>
      <Dialog open={isOpen} onClose={handleClose} className="relative z-50">
        <div
          className="fixed inset-0 bg-black/30 backdrop-blur-sm"
          aria-hidden="true"
        />
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="mx-auto max-w-md w-full rounded-lg bg-white p-6 shadow-lg dark:bg-gray-800 transform transition-all">
            <div className="flex items-center justify-between mb-4">
              <Dialog.Title className="text-lg font-medium dark:text-white">
                {config?.title || "公告"}
              </Dialog.Title>
            </div>
            <div className="mt-2 mb-6 text-gray-700 dark:text-white whitespace-pre-wrap">
              {config?.content || "公告内容加载中..."}
            </div>
            <div className="flex justify-end">
              <button
                type="button"
                onClick={handleClose}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-700 dark:hover:bg-blue-600"
              >
                我知道了
              </button>
            </div>
          </Dialog.Panel>
        </div>
      </Dialog>
    </>
  );
};

export default PublicNotification;
