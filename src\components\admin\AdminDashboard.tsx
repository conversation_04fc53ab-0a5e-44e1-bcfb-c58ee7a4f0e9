"use client";

import { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import {
  UsersIcon,
  FolderIcon,
  ExclamationTriangleIcon,
  CloudArrowUpIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";
import { useToast } from "@/components/ToastProvider";
import { getDashboardStats } from "@/services/adminDashboardService";
import { DashboardStats, StatCard } from "@/types/admin";

export default function AdminDashboard() {
  const { showToast } = useToast();
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalResources: 0,
    yesterdayResources: 0,
    adminUploadedResources: 0,
    userSubmittedResources: 0,
    pendingTasks: 0,
  });
  const [loading, setLoading] = useState(true);

  const loadDashboardStats = useCallback(async () => {
    try {
      setLoading(true);

      // 获取统计数据
      const statsResult = await getDashboardStats();

      // 处理统计数据
      if (statsResult.success && statsResult.data) {
        setStats(statsResult.data);
      } else {
        showToast(statsResult.message || "获取统计数据失败", "error");
      }

      setLoading(false);
    } catch (error) {
      console.error("加载仪表盘数据失败:", error);
      showToast("加载仪表盘数据失败", "error");
      setLoading(false);
    }
  }, [showToast]);

  useEffect(() => {
    loadDashboardStats();
  }, [loadDashboardStats]);

  const statCards: StatCard[] = [
    {
      title: "总用户数",
      value: stats.totalUsers.toLocaleString(),
      change: stats.totalUsers > 0 ? "实时数据" : "暂无数据",
      changeType: "neutral",
      icon: UsersIcon,
      href: "/admin/users",
      color: "from-blue-600 to-blue-700",
    },
    {
      title: "总资源数",
      value: stats.totalResources.toLocaleString(),
      change:
        stats.yesterdayResources > 0
          ? `昨日+${stats.yesterdayResources}`
          : "昨日无新增",
      changeType: stats.yesterdayResources > 0 ? "increase" : "neutral",
      icon: FolderIcon,
      href: "/admin/resources",
      color: "from-purple-600 to-purple-700",
    },
    {
      title: "待处理任务",
      value: stats.pendingTasks.toLocaleString(),
      change: stats.pendingTasks > 0 ? "需要处理" : "全部处理完成",
      changeType: stats.pendingTasks > 0 ? "neutral" : "increase",
      icon: ExclamationTriangleIcon,
      href: "/admin/resources?status=pending",
      color:
        stats.pendingTasks > 0
          ? "from-orange-600 to-orange-700"
          : "from-green-600 to-green-700",
    },
    {
      title: "管理员上传",
      value: stats.adminUploadedResources.toLocaleString(),
      change: `占比 ${(
        (stats.adminUploadedResources / (stats.totalResources || 1)) *
        100
      ).toFixed(1)}%`,
      changeType: "neutral",
      icon: CloudArrowUpIcon,
      href: "/admin/resources?source=admin",
      color: "from-indigo-600 to-indigo-700",
    },
    {
      title: "用户提交",
      value: stats.userSubmittedResources.toLocaleString(),
      change: `占比 ${(
        (stats.userSubmittedResources / (stats.totalResources || 1)) *
        100
      ).toFixed(1)}%`,
      changeType: "neutral",
      icon: UserGroupIcon,
      href: "/admin/resources?source=user",
      color: "from-teal-600 to-teal-700",
    },
  ];

  const getChangeColor = (type?: "increase" | "decrease" | "neutral") => {
    switch (type) {
      case "increase":
        return "text-green-600 dark:text-green-400";
      case "decrease":
        return "text-red-600 dark:text-red-400";
      case "neutral":
        return "text-yellow-600 dark:text-yellow-400";
      default:
        return "text-secondary-text";
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-hover-background rounded w-48 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div
                key={i}
                className="bg-card-background rounded-lg p-6 border border-border-color"
              >
                <div className="h-4 bg-hover-background rounded w-24 mb-4"></div>
                <div className="h-8 bg-hover-background rounded w-16 mb-2"></div>
                <div className="h-3 bg-hover-background rounded w-20"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* 主内容区域 */}
      <div className="flex-1 overflow-auto">
        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {statCards.map((card, index) => (
            <div
              key={index}
              className="bg-card-background rounded-lg shadow-lg border border-border-color overflow-hidden hover:shadow-xl transition-shadow"
            >
              {card.href ? (
                <Link href={card.href} className="block p-6">
                  <div className="flex items-center">
                    <div
                      className={`p-3 rounded-lg bg-gradient-to-r ${card.color} mr-4`}
                    >
                      <card.icon className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-secondary-text mb-1">
                        {card.title}
                      </p>
                      <p className="text-2xl font-bold text-foreground mb-1">
                        {card.value}
                      </p>
                      {card.change && (
                        <p
                          className={`text-sm ${getChangeColor(
                            card.changeType
                          )}`}
                        >
                          {card.change}
                        </p>
                      )}
                    </div>
                  </div>
                </Link>
              ) : (
                <div className="p-6">
                  <div className="flex items-center">
                    <div
                      className={`p-3 rounded-lg bg-gradient-to-r ${card.color} mr-4`}
                    >
                      <card.icon className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-secondary-text mb-1">
                        {card.title}
                      </p>
                      <p className="text-2xl font-bold text-foreground mb-1">
                        {card.value}
                      </p>
                      {card.change && (
                        <p
                          className={`text-sm ${getChangeColor(
                            card.changeType
                          )}`}
                        >
                          {card.change}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
