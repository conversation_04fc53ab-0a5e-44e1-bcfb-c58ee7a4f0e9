"use client";

import { useAuth } from "@/hooks/useAuth";
import AdminLayout from "@/components/admin/AdminLayout";
import AdminDashboard from "@/components/admin/AdminDashboard";
import { AdminGuard } from "@/components/AuthGuard";

export default function AdminPage() {
  const { isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-button-background mx-auto mb-4"></div>
          <p className="text-secondary-text">验证管理员权限中...</p>
        </div>
      </div>
    );
  }

  // 使用 AdminGuard 来处理权限检查和跳转
  return (
    <AdminGuard>
      <AdminLayout>
        <AdminDashboard />
      </AdminLayout>
    </AdminGuard>
  );
}
