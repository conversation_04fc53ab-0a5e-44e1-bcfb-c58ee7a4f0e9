"use client";

import { memo } from "react";
import { formatDate } from "@/lib/utils";

interface ResourceCardMetadataProps {
  fileType?: string;
  date?: string;
}

/**
 * 资源卡片元数据组件
 * 用于展示文件类型和更新时间
 */
const ResourceCardMetadata = memo(function ResourceCardMetadata({
  fileType,
  date,
}: ResourceCardMetadataProps) {
  // 获取文件类型的中文名称
  const getFileTypeName = (type?: string): string => {
    if (!type) return "未知类型";

    switch (type.toLowerCase()) {
      case "video":
        return "视频";
      case "audio":
        return "音频";
      case "image":
        return "图片";
      case "document":
        return "文档";
      case "archive":
        return "压缩包";
      case "application":
        return "应用";
      default:
        return type;
    }
  };

  return (
    <div className="flex flex-wrap gap-y-2 text-sm text-secondary-text mb-4">
      <div className="w-full sm:w-1/2">
        <span className="font-medium">文件类型：</span>{" "}
        {getFileTypeName(fileType) || "未知类型"}
      </div>
      <div className="w-full sm:w-1/2">
        <span className="font-medium">更新时间：</span> {formatDate(date)}
      </div>
    </div>
  );
});

export default ResourceCardMetadata;
