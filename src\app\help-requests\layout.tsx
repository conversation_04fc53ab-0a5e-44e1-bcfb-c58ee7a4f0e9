import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "资源求助 - 97盘搜",
  description: "在97盘搜发布资源求助，获得社区帮助找到您需要的网盘资源",
  keywords: "资源求助,网盘资源,百度网盘,夸克网盘,阿里云盘,迅雷网盘,社区互助",
  openGraph: {
    title: "资源求助 - 97盘搜",
    description: "在97盘搜发布资源求助，获得社区帮助找到您需要的网盘资源",
    type: "website",
  },
};

export default function HelpRequestsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-6">
        {children}
      </div>
    </div>
  );
}
