import { ResourceDetail } from '@/types/resource';
import Script from 'next/script';

interface ResourceStructuredDataProps {
  resource: ResourceDetail;
  currentUrl: string;
}

const generateJsonLd = (resource: ResourceDetail, currentUrl: string) => {
  const breadcrumbData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: '首页',
        item: new URL('/', currentUrl).toString(),
      },
      {
        '@type': 'ListItem',
        position: 2,
        name: resource.title,
        item: currentUrl,
      },
    ],
  };

  const digitalDocumentData = {
    '@context': 'https://schema.org',
    '@type': 'DigitalDocument',
    name: resource.seo_title || resource.title,
    description: resource.seo_description,
    url: currentUrl,
    author: {
      '@type': 'Person',
      name: resource.author || '匿名',
    },
    datePublished: resource.created_at,
    dateModified: resource.updated_at,
    encodingFormat: resource.file_type,
    fileSize: resource.file_size,
  };

  return [breadcrumbData, digitalDocumentData];
};

export function ResourceStructuredData({ resource, currentUrl }: ResourceStructuredDataProps) {
  const jsonLdData = generateJsonLd(resource, currentUrl);

  return (
    <>
      {jsonLdData.map((data, index) => (
        <Script
          key={`json-ld-${index}`}
          id={`json-ld-resource-${index}`}
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
        />
      ))}
    </>
  );
} 