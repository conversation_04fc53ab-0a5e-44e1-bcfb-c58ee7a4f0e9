import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// 在实际项目中，这个函数应该将数据写入数据库
async function saveReport(resourceId: string, resourceName: string) {
    try {
        // 获取报告文件路径
        const reportsDir = path.join(process.cwd(), 'reports');
        const reportFile = path.join(reportsDir, 'invalid-resources.json');

        // 确保目录存在
        if (!fs.existsSync(reportsDir)) {
            fs.mkdirSync(reportsDir, { recursive: true });
        }

        // 读取现有报告或创建新的
        let reports = [];
        if (fs.existsSync(reportFile)) {
            const data = fs.readFileSync(reportFile, 'utf8');
            reports = JSON.parse(data);
        }

        // 添加新的报告
        reports.push({
            id: resourceId,
            name: resourceName,
            reportedAt: new Date().toISOString()
        });

        // 写入文件
        fs.writeFileSync(reportFile, JSON.stringify(reports, null, 2));

        return true;
    } catch (error) {
        console.error('保存资源失效报告失败:', error);
        return false;
    }
}

export async function POST(request: Request) {
    try {
        const body = await request.json();
        const { resourceId, resourceName } = body;

        if (!resourceId || !resourceName) {
            return NextResponse.json(
                { success: false, message: '资源ID和名称不能为空' },
                { status: 400 }
            );
        }

        // 保存报告
        const success = await saveReport(resourceId, resourceName);

        if (success) {
            return NextResponse.json({ success: true });
        } else {
            return NextResponse.json(
                { success: false, message: '保存报告失败' },
                { status: 500 }
            );
        }
    } catch (error) {
        console.error('处理资源失效报告请求失败:', error);
        return NextResponse.json(
            { success: false, message: '服务器错误' },
            { status: 500 }
        );
    }
} 