"use client";

import { usePathname } from "next/navigation";
import FooterStats from "@/components/FooterStats";
import PublicNotification from "@/components/PublicNotification";

interface ConditionalLayoutProps {
  children: React.ReactNode;
}

export default function ConditionalLayout({
  children,
}: ConditionalLayoutProps) {
  const pathname = usePathname();
  const isAdminRoute = pathname?.startsWith("/admin");

  if (isAdminRoute) {
    // 管理后台使用全屏布局，不添加容器样式和footer
    return <>{children}</>;
  }

  // 前台页面使用原有的容器布局
  return (
    <>
      <main className="flex-grow container mx-auto px-4 py-8">{children}</main>
      <footer className="apple-footer">
        <div className="container mx-auto px-4">
          <div className="flex flex-col sm:flex-row items-center justify-between py-2">
            <FooterStats />
            <div className="apple-footer-links flex flex-wrap justify-center gap-x-4 gap-y-1 text-sm mt-3 sm:mt-0">
              <a
                href="https://pan.baidu.com/"
                target="_blank"
                rel="noopener noreferrer"
              >
                百度网盘
              </a>
              <meta content="always" name="referrer" />
              <a
                href="https://pan.quark.cn/"
                target="_blank"
                rel="noopener noreferrer"
              >
                夸克网盘
              </a>
              <a
                href="https://pan.xunlei.com/"
                target="_blank"
                rel="noopener noreferrer"
              >
                迅雷网盘
              </a>
              <a href="/links">友情链接</a>
            </div>
          </div>
          <div className="apple-footer-copyright text-center pt-2 border-t border-[color:var(--footer-border)] mt-1">
            <p className="text-xs">
              声明:本站所有资源均来自网络爬虫公开资源,无意侵犯任何组织和个体的权益,如有侵权违规请联系站长删除.
              <br />
              站长邮箱:
              <a href="mailto:<EMAIL>" className="text-blue-500">
                <EMAIL>
              </a>
              <br />
              &copy; {new Date().getFullYear()} 97盘搜 · 保留所有权利
            </p>
          </div>
        </div>
      </footer>

      {/* 全局公告组件 */}
      <PublicNotification />
    </>
  );
}
