import { NextResponse } from "next/server";

const PUBLIC_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "https://pansoo.cn";

function generateStaticSitemap(): string {
  const staticPages = [
    { url: `${PUBLIC_URL}/`, priority: 1.0, changeFrequency: "daily" },
    { url: `${PUBLIC_URL}/search`, priority: 0.9, changeFrequency: "daily" },
    {
      url: `${PUBLIC_URL}/tutorials`,
      priority: 0.8,
      changeFrequency: "weekly",
    },
    { url: `${PUBLIC_URL}/submit`, priority: 0.8, changeFrequency: "weekly" },
  ];

  const urls = staticPages
    .map(
      (page) => `
    <url>
        <loc>${page.url}</loc>
        <lastmod>${new Date().toISOString()}</lastmod>
        <changefreq>${page.changeFrequency}</changefreq>
        <priority>${page.priority}</priority>
    </url>`
    )
    .join("");

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    ${urls}
</urlset>`;
}

export async function GET() {
  const sitemapXml = generateStaticSitemap();
  return new NextResponse(sitemapXml, {
    headers: { "Content-Type": "application/xml" },
  });
}
