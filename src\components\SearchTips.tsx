"use client";

import { useState, useEffect, useRef } from "react";
import { Lightbulb } from "lucide-react";
import { createPortal } from "react-dom";

const SearchTips = () => {
  const [isOpen, setIsOpen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [popupStyle, setPopupStyle] = useState<React.CSSProperties>({});

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (isOpen && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const top = rect.bottom + window.scrollY + 8;
      let left = rect.left + window.scrollX + rect.width / 2;
      const popupWidth = Math.min(window.innerWidth * 0.95, 320);
      left = left - popupWidth / 2;
      if (left < 8) left = 8;
      if (left + popupWidth > window.innerWidth - 8)
        left = window.innerWidth - popupWidth - 8;
      setPopupStyle({
        position: "absolute",
        top,
        left,
        width: popupWidth,
        zIndex: 9999,
      });
    }
  }, [isOpen]);

  return (
    <div className="relative flex items-center" ref={containerRef}>
      <button
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        className="search-tips-btn flex items-center space-x-2 px-3 py-1.5 text-sm font-medium rounded-md bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
      >
        <Lightbulb className="w-5 h-5 text-yellow-400" />
        <span>搜索提示</span>
      </button>
      {isOpen &&
        typeof window !== "undefined" &&
        createPortal(
          <>
            <div
              style={{ position: "fixed", inset: 0, zIndex: 9998 }}
              onClick={() => setIsOpen(false)}
            />
            <div
              style={popupStyle}
              className="bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-300 text-sm rounded-md shadow-lg p-3 border border-gray-200 dark:border-gray-700"
            >
              <p className="font-bold mb-2">搜索技巧提示</p>
              <ul className="list-disc list-inside text-left space-y-1">
                <li>
                  搜索时请尽量使用关键词, 如: 《复仇者联盟》 请搜索: 复仇者联盟
                </li>
                <li>可以直接搜索你想看的集数,如:凡人修仙传147</li>
                <li>善用精确模式和文件类型筛选</li>
              </ul>
              <div className="absolute left-1/2 -translate-x-1/2 bottom-full w-0 h-0 border-x-8 border-x-transparent border-b-8 border-b-white dark:border-b-gray-800 rotate-180"></div>
            </div>
          </>,
          document.body
        )}
    </div>
  );
};

export default SearchTips;
