"use client";

import { useState } from "react";
import PageHeader from "@/components/PageHeader";
import { useToast } from "@/components/ToastProvider";
import { useAuth } from "@/hooks/useAuth";
import ExcelImport from "@/components/ExcelImport";

interface SubmissionStatusResult {
  task_id: string;
  original_url: string;
  status: string;
  resource_title: string | null;
  error_message: string | null;
  updated_at: string;
}

const statusMapping: { [key: string]: string } = {
  pending: "待处理",
  processing: "处理中",
  success: "已处理完成",
  failed_parse_url: "URL解析失败",
  failed_fetch_details: "获取详情失败",
  duplicate_parsed: "重复资源且已解析",
  duplicate_pending_parse: "重复资源且待解析/正在解析",
  invalid_url: "无效的URL格式或不支持的网盘",
  accepted: "已接受",
  failed_duplicate: "重复资源",
};

const getStatusColorClass = (status: string): string => {
  const s = status.toLowerCase();
  switch (s) {
    case "success":
      return "text-green-600 dark:text-green-400";
    case "failed_parse_url":
    case "failed_fetch_details":
    case "invalid_url":
      return "text-red-600 dark:text-red-400";
    case "processing":
    case "accepted":
    case "failed_duplicate":
    case "duplicate_parsed":
    case "duplicate_pending_parse":
      return "text-yellow-600 dark:text-yellow-400";
    default:
      return "text-[var(--secondary-text)]";
  }
};

export default function SubmitPage() {
  const [resources, setResources] = useState("");
  const [queryLinks, setQueryLinks] = useState("");
  const [isSubmittingResources, setIsSubmittingResources] = useState(false);
  const [isQueryingStatus, setIsQueryingStatus] = useState(false);
  const [queryResult, setQueryResult] = useState<
    SubmissionStatusResult[] | null
  >(null);
  const [submissionResult, setSubmissionResult] = useState<any>(null);
  const [activeTab, setActiveTab] = useState<"manual" | "excel">("manual");

  // 管理员特殊选项
  const [isPersonalResource, setIsPersonalResource] = useState(false);
  const [fillParseFlag, setFillParseFlag] = useState(false);

  const { showToast } = useToast();
  const { user, isAuthenticated, isAdmin, isLoading } = useAuth();

  const resourceLines = resources.split("\n").filter(Boolean);
  const resourceCount = resourceLines.length;

  // 处理Excel导入完成
  const handleExcelImportComplete = (result: any) => {
    // Excel导入完成后的处理，可以显示结果或刷新页面
    console.log("Excel导入完成:", result);
  };

  // 提交资源
  const handleSubmitResources = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!resources.trim()) return;

    // 检查链接是否包含中文
    const containsChinese = (text: string) => /[\u4e00-\u9fa5]/.test(text);
    const urls = resources
      .split("\n")
      .map((url) => url.trim())
      .filter(Boolean);

    if (urls.length > 2000) {
      showToast("单次最多提交2000条链接。", "error");
      return;
    }

    if (urls.some(containsChinese)) {
      showToast("提交错误，请单独提交链接，不要包含中文。", "error");
      return;
    }

    setIsSubmittingResources(true);
    setSubmissionResult(null);
    try {
      // 构建请求体
      const requestBody: any = {
        urls: urls.map((url) => ({ url })),
        is_guest: !isAuthenticated, // 标记是否为游客提交
      };

      // 如果是管理员，添加特殊选项
      if (isAuthenticated && isAdmin()) {
        requestBody.is_mine = isPersonalResource;
        requestBody.is_parsed = fillParseFlag;
        requestBody.admin_submit = true;
      }

      // 添加认证头（如果已登录）
      const headers: any = { "Content-Type": "application/json" };
      const token = localStorage.getItem("auth_token");
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await fetch("/api/submit_resources", {
        method: "POST",
        headers,
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "提交失败");
      }

      setResources("");
      showToast(data.message || "资源提交成功！", "success");
      setSubmissionResult(data);
    } catch (error: any) {
      console.error("提交资源失败:", error);
      showToast(error.message || "提交失败，请稍后重试", "error");
    } finally {
      setIsSubmittingResources(false);
    }
  };

  // 查询链接状态
  const handleQueryLinks = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!queryLinks.trim()) return;

    setIsQueryingStatus(true);
    setQueryResult(null);
    try {
      const urls = queryLinks
        .split("\n")
        .map((url) => url.trim())
        .filter(Boolean);
      const response = await fetch("/api/query_submission_status", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ urls }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "查询失败");
      }

      if (data && data.length === 0) {
        showToast("未查询到链接状态信息", "error");
      }
      setQueryResult(data);
    } catch (error: any) {
      console.error("查询链接失败:", error);
      showToast(error.message || "查询失败，请稍后重试", "error");
    } finally {
      setIsQueryingStatus(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto pt-8">
      <div className="text-center mb-4">
        <h1 className="text-3xl font-bold text-[var(--foreground)]">
          资源提交
        </h1>
      </div>

      <div className="mt-6 space-y-8">
        {/* 资源提交表单 */}
        <div className="bg-[var(--card-background)] p-6 rounded-xl shadow-sm border border-[var(--border-color)]">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold">提交资源</h2>
            <div className="flex bg-[var(--hover-background)] rounded-lg p-1">
              <button
                type="button"
                onClick={() => setActiveTab("manual")}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                  activeTab === "manual"
                    ? "bg-[var(--button-background)] text-white"
                    : "text-[var(--secondary-text)] hover:text-[var(--foreground)]"
                }`}
              >
                手动输入
              </button>
              <button
                type="button"
                onClick={() => setActiveTab("excel")}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                  activeTab === "excel"
                    ? "bg-[var(--button-background)] text-white"
                    : "text-[var(--secondary-text)] hover:text-[var(--foreground)]"
                }`}
              >
                Excel导入
              </button>
            </div>
          </div>

          {/* 手动输入标签页 */}
          {activeTab === "manual" && (
            <form onSubmit={handleSubmitResources}>
              {/* 用户登录状态提示 */}
              {!isLoading && (
                <div className="mb-4 p-4 rounded-lg border border-[var(--border-color)]">
                  {isAuthenticated ? (
                    <div className="flex items-center text-green-600 dark:text-green-400">
                      <svg
                        className="w-5 h-5 mr-2"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <span>已登录用户：{user?.username}</span>
                      {isAdmin() && (
                        <span className="ml-2 px-2 py-1 text-xs bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 rounded-full">
                          管理员
                        </span>
                      )}
                    </div>
                  ) : (
                    <div className="flex items-center text-orange-600 dark:text-orange-400">
                      <svg
                        className="w-5 h-5 mr-2"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <span>未登录，将以游客身份提交资源</span>
                    </div>
                  )}
                </div>
              )}

              <div className="mb-4">
                <textarea
                  value={resources}
                  onChange={(e) => {
                    const inputValue = e.target.value;
                    const lines = inputValue.split("\n");
                    const extractedLinks = lines.map((line) => {
                      const match = line.match(/(https?:\/\/\S+)/);
                      return match ? match[0] : line;
                    });
                    const newValue = extractedLinks.join("\n");
                    setResources(newValue);

                    if (submissionResult) {
                      setSubmissionResult(null);
                    }
                  }}
                  placeholder={`支持网盘类型:百度网盘、阿里云盘、夸克网盘、迅雷云盘
单次最多支持提交2000条,一行一条链接,如需自定义资源标题请使用模板导入功能
请勿提交违规内容、纯引流内容,严重违规者封禁提交权限`}
                  className="w-full h-40 p-4 rounded-lg border border-[var(--border-color)] bg-[var(--background)] text-[var(--foreground)] focus:outline-none focus:ring-2 focus:ring-[var(--button-background)]"
                  required
                />
                <p
                  className={`text-right text-sm mt-1 ${
                    resourceCount > 2000
                      ? "text-red-500"
                      : "text-[var(--secondary-text)]"
                  }`}
                >
                  {resourceCount} / 2000
                </p>
              </div>

              {/* 管理员特殊选项 - 只有登录且是管理员才显示 */}
              {isAuthenticated && isAdmin() && (
                <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                  <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-3">
                    管理员选项
                  </h4>
                  <div className="space-y-3">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={isPersonalResource}
                        onChange={(e) =>
                          setIsPersonalResource(e.target.checked)
                        }
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                      />
                      <span className="ml-2 text-sm text-blue-700 dark:text-blue-300">
                        标记为本人资源
                      </span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={fillParseFlag}
                        onChange={(e) => setFillParseFlag(e.target.checked)}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                      />
                      <span className="ml-2 text-sm text-blue-700 dark:text-blue-300">
                        填充解析标志
                      </span>
                    </label>
                  </div>
                </div>
              )}

              <button
                type="submit"
                disabled={isSubmittingResources}
                className="w-full py-3 bg-[var(--button-background)] hover:bg-[var(--button-hover)] text-white font-medium rounded-lg transition-colors duration-200 disabled:opacity-50"
              >
                {isSubmittingResources ? "提交中..." : "提交资源"}
              </button>

              {submissionResult && (
                <div className="mt-6 border-t border-[var(--border-color)] pt-4">
                  <h3 className="text-lg font-medium mb-3">提交结果</h3>
                  <div className="space-y-2">
                    <p className="font-semibold">{submissionResult.message}</p>
                    <p className="text-sm text-[var(--secondary-text)]">
                      总共提交: {submissionResult.total_submitted}, 成功处理:{" "}
                      {submissionResult.accepted_for_processing}
                    </p>
                  </div>

                  {submissionResult.initial_results?.length > 0 && (
                    <div className="mt-4 overflow-x-auto">
                      <table className="min-w-full">
                        <thead>
                          <tr className="bg-[var(--hover-background)] text-left">
                            <th className="py-2 px-4 rounded-tl-lg">链接</th>
                            <th className="py-2 px-4">状态</th>
                            <th className="py-2 px-4 rounded-tr-lg">信息</th>
                          </tr>
                        </thead>
                        <tbody>
                          {submissionResult.initial_results.map(
                            (result: any, index: number) => (
                              <tr
                                key={index}
                                className={
                                  index % 2 === 0
                                    ? "bg-[var(--background)]"
                                    : "bg-[var(--card-background)]"
                                }
                              >
                                <td className="py-2 px-4 truncate max-w-xs">
                                  {result.url}
                                </td>
                                <td
                                  className={`py-2 px-4 font-medium ${
                                    result.status === "accepted"
                                      ? "text-green-600 dark:text-green-400"
                                      : "text-red-600 dark:text-red-400"
                                  }`}
                                >
                                  {result.status === "FAILED_DUPLICATE"
                                    ? "资源已重复"
                                    : statusMapping[
                                        result.status.toLowerCase()
                                      ] || result.status}
                                </td>
                                <td className="py-2 px-4 text-[var(--secondary-text)]">
                                  {result.status === "FAILED_DUPLICATE"
                                    ? result.notes
                                    : result.message}
                                </td>
                              </tr>
                            )
                          )}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              )}
            </form>
          )}

          {/* Excel导入标签页 */}
          {activeTab === "excel" && (
            <div>
              {/* 用户登录状态提示 */}
              {!isLoading && (
                <div className="mb-4 p-4 rounded-lg border border-[var(--border-color)]">
                  {isAuthenticated ? (
                    <div className="flex items-center text-green-600 dark:text-green-400">
                      <svg
                        className="w-5 h-5 mr-2"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <span>已登录用户：{user?.username}</span>
                      {isAdmin() && (
                        <span className="ml-2 px-2 py-1 text-xs bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 rounded-full">
                          管理员
                        </span>
                      )}
                    </div>
                  ) : (
                    <div className="flex items-center text-orange-600 dark:text-orange-400">
                      <svg
                        className="w-5 h-5 mr-2"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <span>未登录，将以游客身份提交资源</span>
                    </div>
                  )}
                </div>
              )}

              {/* 管理员特殊选项 */}
              {isAuthenticated && isAdmin() && (
                <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                  <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-3">
                    管理员选项
                  </h4>
                  <div className="space-y-3">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={isPersonalResource}
                        onChange={(e) =>
                          setIsPersonalResource(e.target.checked)
                        }
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                      />
                      <span className="ml-2 text-sm text-blue-700 dark:text-blue-300">
                        标记为本人资源
                      </span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={fillParseFlag}
                        onChange={(e) => setFillParseFlag(e.target.checked)}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                      />
                      <span className="ml-2 text-sm text-blue-700 dark:text-blue-300">
                        填充解析标志
                      </span>
                    </label>
                  </div>
                </div>
              )}

              <ExcelImport
                onImportComplete={handleExcelImportComplete}
                maxRows={2000}
                isPersonalResource={isPersonalResource}
                fillParseFlag={fillParseFlag}
                isAdmin={isAdmin()}
              />
            </div>
          )}
        </div>

        {/* 链接状态查询表单 */}
        <div className="bg-[var(--card-background)] p-6 rounded-xl shadow-sm border border-[var(--border-color)]">
          <h2 className="text-xl font-semibold mb-4">查询链接状态</h2>
          <form onSubmit={handleQueryLinks}>
            <div className="mb-4">
              <textarea
                value={queryLinks}
                onChange={(e) => {
                  setQueryLinks(e.target.value);
                  if (queryResult) {
                    setQueryResult(null);
                  }
                }}
                placeholder="输入需要查询的链接，支持多条链接查询，一行一条"
                className="w-full h-32 p-4 rounded-lg border border-[var(--border-color)] bg-[var(--background)] text-[var(--foreground)] focus:outline-none focus:ring-2 focus:ring-[var(--button-background)]"
                required
              />
            </div>
            <button
              type="submit"
              disabled={isQueryingStatus}
              className="w-full py-3 bg-[var(--button-background)] hover:bg-[var(--button-hover)] text-white font-medium rounded-lg transition-colors duration-200 disabled:opacity-50"
            >
              {isQueryingStatus ? "查询中..." : "查询链接状态"}
            </button>
          </form>

          {/* 查询结果显示 */}
          {queryResult && queryResult.length > 0 && (
            <div className="mt-6 border-t border-[var(--border-color)] pt-4">
              <h3 className="text-lg font-medium mb-3">查询结果</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full">
                  <thead>
                    <tr className="bg-[var(--hover-background)] text-left">
                      <th className="py-2 px-4 rounded-tl-lg">链接</th>
                      <th className="py-2 px-4">状态</th>
                      <th className="py-2 px-4">详情</th>
                      <th className="py-2 px-4 rounded-tr-lg">最后更新时间</th>
                    </tr>
                  </thead>
                  <tbody>
                    {queryResult.map((result, index) => (
                      <tr
                        key={result.task_id}
                        className={
                          index % 2 === 0
                            ? "bg-[var(--background)]"
                            : "bg-[var(--card-background)]"
                        }
                      >
                        <td className="py-2 px-4 truncate max-w-xs">
                          <a
                            href={result.original_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-500 hover:underline"
                          >
                            {result.original_url}
                          </a>
                        </td>
                        <td className="py-2 px-4">
                          <span className={getStatusColorClass(result.status)}>
                            {statusMapping[result.status.toLowerCase()] ||
                              result.status}
                          </span>
                        </td>
                        <td className="py-2 px-4">
                          {result.resource_title ||
                            result.error_message ||
                            "N/A"}
                        </td>
                        <td className="py-2 px-4">
                          {new Date(result.updated_at).toLocaleString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
