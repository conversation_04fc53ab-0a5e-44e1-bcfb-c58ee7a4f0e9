/**
 * Progress 组件测试用例
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { Progress } from '@/components/ui/progress';

describe('Progress 组件', () => {
  it('应该正确渲染基本进度条', () => {
    render(<Progress value={50} data-testid="progress" />);
    
    const progressContainer = screen.getByTestId('progress');
    expect(progressContainer).toBeInTheDocument();
    expect(progressContainer).toHaveClass('h-2', 'w-full', 'rounded-full');
  });

  it('应该正确处理进度值', () => {
    const { rerender } = render(<Progress value={25} data-testid="progress" />);
    
    let progressBar = screen.getByTestId('progress').firstChild as HTMLElement;
    expect(progressBar).toHaveStyle('transform: translateX(-75%)');

    // 测试不同的进度值
    rerender(<Progress value={75} data-testid="progress" />);
    progressBar = screen.getByTestId('progress').firstChild as HTMLElement;
    expect(progressBar).toHaveStyle('transform: translateX(-25%)');

    // 测试 100% 进度
    rerender(<Progress value={100} data-testid="progress" />);
    progressBar = screen.getByTestId('progress').firstChild as HTMLElement;
    expect(progressBar).toHaveStyle('transform: translateX(-0%)');
  });

  it('应该正确处理边界值', () => {
    // 测试负值
    const { rerender } = render(<Progress value={-10} data-testid="progress" />);
    let progressBar = screen.getByTestId('progress').firstChild as HTMLElement;
    expect(progressBar).toHaveStyle('transform: translateX(-100%)');

    // 测试超过最大值
    rerender(<Progress value={150} data-testid="progress" />);
    progressBar = screen.getByTestId('progress').firstChild as HTMLElement;
    expect(progressBar).toHaveStyle('transform: translateX(-0%)');
  });

  it('应该支持自定义最大值', () => {
    render(<Progress value={50} max={200} data-testid="progress" />);
    
    const progressBar = screen.getByTestId('progress').firstChild as HTMLElement;
    // 50/200 = 25%，所以应该是 translateX(-75%)
    expect(progressBar).toHaveStyle('transform: translateX(-75%)');
  });

  it('应该支持自定义样式类名', () => {
    render(<Progress value={50} className="custom-class" data-testid="progress" />);
    
    const progressContainer = screen.getByTestId('progress');
    expect(progressContainer).toHaveClass('custom-class');
  });

  it('应该正确处理默认值', () => {
    render(<Progress data-testid="progress" />);
    
    const progressBar = screen.getByTestId('progress').firstChild as HTMLElement;
    // 默认值为 0，所以应该是 translateX(-100%)
    expect(progressBar).toHaveStyle('transform: translateX(-100%)');
  });

  it('应该支持传递其他 HTML 属性', () => {
    render(<Progress value={50} id="test-progress" aria-label="Upload progress" data-testid="progress" />);
    
    const progressContainer = screen.getByTestId('progress');
    expect(progressContainer).toHaveAttribute('id', 'test-progress');
    expect(progressContainer).toHaveAttribute('aria-label', 'Upload progress');
  });

  it('应该有正确的样式类', () => {
    render(<Progress value={50} data-testid="progress" />);
    
    const progressContainer = screen.getByTestId('progress');
    expect(progressContainer).toHaveClass(
      'relative',
      'h-2',
      'w-full',
      'overflow-hidden',
      'rounded-full',
      'bg-gray-200',
      'dark:bg-gray-700'
    );

    const progressBar = progressContainer.firstChild as HTMLElement;
    expect(progressBar).toHaveClass(
      'h-full',
      'w-full',
      'flex-1',
      'bg-blue-600',
      'transition-all',
      'duration-300',
      'ease-in-out'
    );
  });
});
