"use client";

import { memo } from "react";
import { ExclamationTriangleIcon } from "@heroicons/react/24/outline";

interface ResourceCardFeedbackProps {
  searchType?: "local" | "online";
  onReportInvalid: () => void;
}

/**
 * 资源卡片反馈组件
 * 用于展示资源失效反馈按钮
 */
const ResourceCardFeedback = memo(function ResourceCardFeedback({
  searchType,
  onReportInvalid,
}: ResourceCardFeedbackProps) {
  if (searchType !== "local") {
    return null;
  }

  return (
    <div className="mt-4 pt-3 border-t border-gray-100 dark:border-gray-600">
      <button
        type="button"
        onClick={onReportInvalid}
        className="w-full py-2 px-3 rounded text-sm inline-flex items-center justify-center gap-1 bg-gray-100 text-gray-700 hover:bg-gray-200 resource-feedback-btn transition-colors"
      >
        <ExclamationTriangleIcon className="h-4 w-4" />
        资源失效反馈
      </button>
    </div>
  );
});

export default ResourceCardFeedback;
