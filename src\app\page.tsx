"use client";

import { useRouter } from "next/navigation";
import { useCallback } from "react";
import AnimatedTitle from "@/components/AnimatedTitle";
import SearchBar from "@/components/SearchBar";
import { PageContainer } from "@/components/layout/PageContainer";
import { SEARCH_CONFIG } from "@/config/constants";
import { useSearch } from "@/hooks/useSearch";

export default function Home() {
  const router = useRouter();
  const { searchType } = useSearch({
    initialType: SEARCH_CONFIG.defaultType,
  });

  const handleSearch = useCallback(
    (query: string, type: "local" | "online") => {
      if (query.trim()) {
        router.push(`/search?q=${encodeURIComponent(query)}&type=${type}`);
      }
    },
    [router]
  );

  return (
    <PageContainer>
      <div className="flex flex-col items-center justify-center min-h-[70vh] text-center">
        <div className="mb-8 px-4">
          <AnimatedTitle
            text="PAN SO"
            className="text-5xl md:text-6xl lg:text-7xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 mb-4"
          />
          <p className="text-xl text-secondary-text max-w-2xl mx-auto mt-4">
            探索、发现、分享各类网盘资源
          </p>
        </div>

        <div className="w-full max-w-2xl px-4">
          <SearchBar
            onSearch={handleSearch}
            placeholder="搜索电影、音乐、软件、文档等资源..."
            initialSearchType={searchType}
          />
        </div>
      </div>
    </PageContainer>
  );
}
