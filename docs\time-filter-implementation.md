# 时间过滤功能实现文档

## 功能概述

在搜索页面中实现了时间过滤功能，允许用户根据资源的更新时间来筛选搜索结果。

## 实现的功能

### UI组件
- 添加了"更新时间"下拉选择器组件
- 包含6个时间范围选项：全部时间、最近一周、最近半月、最近一月、最近半年、最近一年
- 默认选中"全部时间"
- 完全适配亮色/暗色主题
- 与现有UI组件保持视觉一致性

### 功能集成
- 时间过滤器集成到现有搜索功能中
- 用户选择不同时间范围后自动重新触发搜索请求
- 支持本地搜索和在线搜索两种模式

### 后端接口对接
- 在调用搜索接口时添加了`time_filter`参数
- 参数值映射关系：
  - "全部时间" → "all"
  - "最近一周" → "week"
  - "最近半月" → "half_month"
  - "最近一月" → "month"
  - "最近半年" → "half_year"
  - "最近一年" → "year"

## 文件修改清单

### 新增文件
- `src/components/TimeFilter.tsx` - 时间过滤器组件

### 修改文件
- `src/app/search/page.tsx` - 搜索页面主组件
  - 添加时间过滤器状态管理
  - 更新FilterBar组件以包含时间过滤器
  - 修改所有搜索函数调用以传递时间过滤参数
  - 添加时间过滤器变更处理函数

- `src/services/resourceService.ts` - 资源服务
  - 更新`searchPanResources`函数以支持时间过滤参数
  - 更新`getValidResources`函数以支持时间过滤参数
  - 修改API调用以包含时间过滤参数

- `src/app/globals.css` - 全局样式
  - 添加时间过滤器在暗色模式下的样式规则

## 技术实现细节

### 组件架构
- `TimeFilter`组件是一个受控组件，接收`value`和`onChange`属性
- 使用与现有过滤器相同的样式类名以保持一致性
- 支持完整的无障碍访问（aria-label等）

### 状态管理
- 在`SearchResults`组件中添加了`timeFilter`状态
- 使用`handleTimeFilterChange`函数处理时间过滤器变更
- 所有搜索相关的useCallback依赖数组都已更新以包含`timeFilter`

### API集成
- 本地搜索API调用：`/api/cached_resources?...&time_filter=${timeFilter}`
- 在线搜索API调用：通过`searchPanResources`函数传递时间过滤参数
- 缓存键包含时间过滤参数以确保正确的缓存隔离

## 使用方法

1. 用户在搜索页面输入搜索关键词
2. 选择所需的时间范围（默认为"全部时间"）
3. 系统自动根据选择的时间范围重新执行搜索
4. 搜索结果将只包含指定时间范围内更新的资源

## 响应式设计

- 时间过滤器在不同屏幕尺寸下都能正常显示
- 使用flexbox布局确保在移动设备上的良好表现
- 与其他过滤器组件保持一致的间距和对齐

## 主题支持

- 完全支持亮色和暗色主题
- 使用CSS变量和Tailwind类名确保主题切换的平滑过渡
- 下拉选项在暗色模式下具有正确的背景色和文字颜色
