import { NextResponse } from "next/server";

const BACKEND_API_URL = process.env.API_PROXY_TARGET || "http://127.0.0.1:9999";

export async function POST(request: Request) {
  try {
    const body = await request.json();

    // 获取认证头
    const authHeader = request.headers.get("authorization");

    // 构建转发请求的头部
    const headers: any = {
      "Content-Type": "application/json",
    };

    // 如果有认证头，添加到转发请求中
    if (authHeader) {
      headers.Authorization = authHeader;
    }

    // 转发请求到后端服务
    const response = await fetch(`${BACKEND_API_URL}/api/submit_resources`, {
      method: "POST",
      headers,
      body: JSON.stringify(body),
    });

    // 获取后端响应数据
    const data = await response.json();

    // 如果后端返回错误状态码，保持相同的状态码
    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    // 返回成功响应
    return NextResponse.json(data);
  } catch (error) {
    console.error("提交资源失败:", error);
    return NextResponse.json(
      {
        error: "服务器处理请求时出错",
        message: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 }
    );
  }
}
