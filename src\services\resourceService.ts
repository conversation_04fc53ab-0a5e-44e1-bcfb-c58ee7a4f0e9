import { ResourceDetail } from "@/types/resource";
import { cache } from "react";
import { apiRequest } from "./api";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

/**
 * 根据 resource_key 获取资源详细信息
 * @param resource_key 资源 key
 */
export const getResourceByKey = cache(
  async (resourceKey: string): Promise<ResourceDetail | null> => {
    if (!resourceKey) {
      return null;
    }
    try {
      const response = await fetch(
        `${API_BASE_URL}/api/resource/${resourceKey}`
      );
      if (!response.ok) {
        return null;
      }
      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Failed to fetch resource by key:", error);
      return null;
    }
  }
);

export interface ResourceStats {
  total: number;
  yesterday: number;
}

export interface Resource {
  id: string;
  name: string;
  description: string;
  type: string;
  size: string;
  baiduLink?: string;
  quarkLink?: string;
  aliyunLink?: string;
  thunderLink?: string;
  date: string;
  isLocal?: boolean;
  verified?: boolean;
  page?: number;
  resourceId?: string;
  text_content?: string;
  file_type?: string;
}

// API返回的资源类型
export interface ApiResource {
  title: string;
  detail_url: string;
  share_url: string;
  original_url?: string;
  pan_type: number;
  valid: boolean;
  status_message: string;
  verified_status?: string;
  created_at?: string;
  updated_at?: string;
  resource_id?: string;
  text_content?: string;
  file_type?: string;
}

// 搜索结果接口
export interface SearchResponse {
  status: string;
  message: string;
  total: number;
  results: SearchResult[];
}

// 搜索结果项接口
export interface SearchResult {
  resource_id: string;
  file_name: string;
  file_size: string;
  file_type: string;
  source: string;
  platform: string;
  detail_url: string;
  thumbnail?: string;
  create_time?: string;
  share_url: string;
  original_url?: string;
  pan_type: number;
  updated_at?: string;
  created_at?: string;
  text_content?: string;
}

// 请求缓存，用于防止重复请求
const requestCache: { [key: string]: { timestamp: number; data: any } } = {};

// 分层缓存策略：不同类型数据使用不同的缓存时间
const CACHE_TIMEOUTS = {
  RESOURCE_DETAIL: 10 * 60 * 1000, // 资源详情：10分钟
  SEARCH_RESULTS: 5 * 60 * 1000, // 搜索结果：5分钟
  RESOURCE_STATUS: 2 * 60 * 1000, // 资源状态：2分钟
  SIMILAR_RESOURCES: 15 * 60 * 1000, // 相似资源：15分钟
} as const;

// 默认缓存时间（向后兼容）
const CACHE_TIMEOUT = CACHE_TIMEOUTS.SEARCH_RESULTS;

/**
 * 获取资源统计
 */
export async function getResourceStats(): Promise<ResourceStats> {
  // 由于resources.json已被删除，返回默认值
  return {
    total: 0,
    yesterday: 0,
  };
}

/**
 * 获取数据库中缓存的有效资源
 * @param query 搜索关键词
 * @param panType 网盘类型 0:全部资源 1:百度网盘 2:夸克网盘 3:阿里云盘 4:迅雷网盘
 * @param limit 限制返回数量
 * @param page 页码，默认为1
 * @param user 用户名，可选
 * @param sort_by 排序方式，默认为relevance
 * @param timeFilter 时间过滤: all(全部时间), week(最近一周), half_month(最近半月), month(最近一月), half_year(最近半年), year(最近一年)
 */
export async function getValidResources(
  query: string,
  panType: number | null,
  limit: number = 30,
  page: number = 1,
  user?: string,
  sort_by: string = "relevance",
  timeFilter: string = "all"
): Promise<ApiResource[]> {
  try {
    const cacheKey = `cached_resources_${query}_${panType}_${limit}_${page}_${
      user || ""
    }_${sort_by}_${timeFilter}`;
    const now = Date.now();

    // 使用相似资源的缓存时间（因为这个函数主要用于获取相似资源）
    const cacheTimeout = user
      ? CACHE_TIMEOUTS.SIMILAR_RESOURCES
      : CACHE_TIMEOUTS.SEARCH_RESULTS;

    // 检查缓存
    if (
      requestCache[cacheKey] &&
      now - requestCache[cacheKey].timestamp < cacheTimeout
    ) {
      return requestCache[cacheKey].data;
    }

    const params = new URLSearchParams();
    params.append("title", query);
    if (panType !== null) {
      params.append("pan_type", panType.toString());
    }
    params.append("limit", limit.toString());
    params.append("page", page.toString());
    if (user !== undefined) {
      params.append("user", user);
    }
    params.append("sort_by", sort_by);
    if (timeFilter !== "all") {
      params.append("time_filter", timeFilter);
    }

    const response = await fetch(
      `${API_BASE_URL}/api/custom_search?${params.toString()}`
    );

    if (!response.ok) {
      throw new Error(`API调用失败: ${response.status}`);
    }

    const data = await response.json();
    const results = data.resources || [];

    // 更新缓存
    requestCache[cacheKey] = {
      timestamp: now,
      data: results,
    };

    return results;
  } catch (error) {
    console.error("获取缓存资源失败:", error);
    return [];
  }
}

/**
 * 搜索网盘资源
 * @param keyword 搜索关键词
 * @param panType 网盘类型 0:全部资源 1:百度网盘 2:夸克网盘 3:阿里云盘 4:迅雷网盘
 * @param page 页码，默认为1
 * @param limit 每页数量，默认为30
 * @param exactMatch 是否精确搜索，默认为false
 * @param fileType 文件类型筛选: video(视频), audio(音频), image(图片), document(文档), archive(压缩), application(应用)
 * @param timeFilter 时间过滤: all(全部时间), week(最近一周), half_month(最近半月), month(最近一月), half_year(最近半年), year(最近一年)
 */
export async function searchPanResources(
  keyword: string,
  panType: number,
  page: number = 1,
  limit: number = 30,
  exactMatch: boolean = false,
  fileType: string = "all",
  timeFilter: string = "all"
): Promise<SearchResponse> {
  try {
    const cacheKey = `search_${keyword}_${panType}_${page}_${limit}_${exactMatch}_${fileType}_${timeFilter}`;
    const now = Date.now();

    // 检查缓存
    if (
      requestCache[cacheKey] &&
      now - requestCache[cacheKey].timestamp < CACHE_TIMEOUT
    ) {
      return requestCache[cacheKey].data;
    }

    // 添加精确搜索参数、文件类型参数和时间过滤参数
    const exactParam = exactMatch ? "&exact=1" : "";
    const fileTypeParam = fileType !== "all" ? `&file_type=${fileType}` : "";
    const timeFilterParam =
      timeFilter !== "all" ? `&time_filter=${timeFilter}` : "";

    const response = await fetch(
      `${API_BASE_URL}/api/search?keyword=${encodeURIComponent(
        keyword
      )}&pan_type=${panType}&page=${page}&limit=${limit}${exactParam}${fileTypeParam}${timeFilterParam}`
    );

    if (!response.ok) {
      throw new Error(`搜索API调用失败: ${response.status}`);
    }

    const data: SearchResponse = await response.json();

    // 更新缓存
    requestCache[cacheKey] = {
      timestamp: now,
      data: data,
    };

    return data;
  } catch (error) {
    console.error("搜索网盘资源失败:", error);
    return { status: "error", message: "搜索失败", total: 0, results: [] };
  }
}

/**
 * 检查资源链接状态
 * @param resourceId 资源ID
 * @param panType 网盘类型 1:百度网盘 2:夸克网盘
 */
export async function checkResourceStatus(
  resourceId: string,
  panType: number
): Promise<{ valid: boolean; message: string }> {
  try {
    // 添加缓存机制，资源状态检查结果缓存2分钟
    const cacheKey = `resource_status_${resourceId}_${panType}`;
    const now = Date.now();

    // 检查缓存
    if (
      requestCache[cacheKey] &&
      now - requestCache[cacheKey].timestamp < CACHE_TIMEOUTS.RESOURCE_STATUS
    ) {
      return requestCache[cacheKey].data;
    }

    const params = new URLSearchParams();
    params.set("resource_id", resourceId);
    params.set("pan_type", panType.toString());
    const response = await fetch(
      `${API_BASE_URL}/api/check_resource_status?${params.toString()}`
    );

    if (!response.ok) {
      throw new Error(`API调用失败: ${response.status}`);
    }

    const data = await response.json();
    const result = {
      valid: data.valid || false,
      message: data.message || "未知状态",
    };

    // 更新缓存
    requestCache[cacheKey] = {
      timestamp: now,
      data: result,
    };

    return result;
  } catch (error) {
    console.error("检查资源状态失败:", error);
    return { valid: false, message: "检查过程出错" };
  }
}

export interface ShareLinkResponse {
  status: string;
  message: string;
  share_url?: string;
  share_pwd?: string;
  title?: string;
  file_type?: string;
  expiry_days?: number;
}

export const getShareLink = async (
  platform: string,
  originalSharePwd?: string,
  resourceId?: string
): Promise<ShareLinkResponse> => {
  try {
    const queryParams = new URLSearchParams({
      platform,
      ...(originalSharePwd && { original_share_pwd: originalSharePwd }),
      ...(resourceId && { resource_id: resourceId }),
    });

    const response = await fetch(
      `${API_BASE_URL}/api/get_share?${queryParams.toString()}`,
      {
        method: "POST",
        headers: {
          accept: "application/json",
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      throw new Error(`API调用失败: ${response.status}`);
    }

    const data: ShareLinkResponse = await response.json();
    return data;
  } catch (error: unknown) {
    console.error("获取分享链接失败:", error);
    throw error;
  }
};

/**
 * 该函数在静态网站中已不再使用
 * 请使用以下方法之一收集资源失效反馈:
 * 1. 预构建HTML表单 (/report-form.html)
 * 2. 使用Formspree, Google Forms或其他第三方表单服务
 * 3. 使用Email mailto链接
 */
export async function reportInvalidResource(): Promise<boolean> {
  try {
    const response = await fetch(`${API_BASE_URL}/api/report_invalid`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    });
    return response.ok;
  } catch (error) {
    console.error("报告资源失效失败:", error);
    return false;
  }
}

// 新增获取本地资源函数
export function searchLocalResources(): Resource[] {
  // 由于resources.json已被删除，直接返回空数组
  return [];
}

// ==================== 管理后台相关接口 ====================

// 管理后台资源类型定义
export interface AdminResource {
  id: number;
  resource_key: string;
  title: string;
  pan_type: number;
  pan_type_name?: string;
  file_type?: string;
  file_size?: string | null;
  author: string | null;
  is_mine?: boolean;
  verified_status?: string | null;
  access_count?: number;
  created_at?: string;
  updated_at: string;
  source: string;
  text_content?: string | null;
}

// 分页信息
export interface AdminPagination {
  page: number;
  size: number;
  total: number;
  pages: number;
}

// 资源列表响应
export interface AdminResourceListResponse {
  status: string;
  data: {
    resources: AdminResource[];
    pagination: AdminPagination;
  };
}

// 资源查询参数
export interface AdminResourceQueryParams {
  page?: number;
  size?: number;
  sort_by?: string;
  sort_order?: "asc" | "desc";
  time_filter?: string;
  pan_type?: string;
  file_type?: string;
  status?: string; // 更改为status，匹配后端API
  is_mine?: string;
  keyword?: string; // 更改为keyword，匹配后端API
}

// 获取管理后台资源列表
export const getAdminResourceList = async (
  params: AdminResourceQueryParams = {}
): Promise<AdminResourceListResponse> => {
  const queryParams = new URLSearchParams();

  // 设置默认参数
  queryParams.append("page", (params.page || 1).toString());
  queryParams.append("size", (params.size || 20).toString());
  queryParams.append("sort_by", params.sort_by || "updated_at");
  queryParams.append("sort_order", params.sort_order || "desc");
  queryParams.append("time_filter", params.time_filter || "all");

  // 添加可选参数
  if (params.pan_type) queryParams.append("pan_type", params.pan_type);
  if (params.file_type) queryParams.append("file_type", params.file_type);
  if (params.status) queryParams.append("status", params.status);
  if (params.is_mine) queryParams.append("is_mine", params.is_mine);
  if (params.keyword) queryParams.append("keyword", params.keyword);

  return apiRequest<AdminResourceListResponse>(
    `/api/admin/resources?${queryParams.toString()}`,
    {
      method: "GET",
    }
  );
};

// 删除资源
export const deleteAdminResource = async (
  resourceId: number
): Promise<{ success: boolean; message: string }> => {
  try {
    await apiRequest(`/api/admin/resources/${resourceId}`, {
      method: "DELETE",
    });
    return { success: true, message: "资源删除成功" };
  } catch {
    return { success: false, message: "资源删除失败" };
  }
};

// 批量删除资源
export const batchDeleteAdminResources = async (
  resourceIds: number[]
): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await apiRequest("/api/admin/resources/batch-delete", {
      method: "POST",
      body: JSON.stringify(resourceIds), // 直接发送数组，不包装在对象中
    });

    // 处理后端返回的响应格式
    if (response.status === "success") {
      return { success: true, message: response.message };
    } else {
      return { success: false, message: response.message || "批量删除失败" };
    }
  } catch (error: any) {
    console.error("批量删除资源失败:", error);
    return {
      success: false,
      message: error?.message || "批量删除失败",
    };
  }
};

// 验证资源状态
export const verifyAdminResource = async (
  resourceId: number,
  status: string
): Promise<{ success: boolean; message: string }> => {
  try {
    await apiRequest(`/api/admin/resources/${resourceId}/verify`, {
      method: "POST",
      body: JSON.stringify({ verified_status: status }),
    });
    return { success: true, message: "资源状态更新成功" };
  } catch {
    return { success: false, message: "资源状态更新失败" };
  }
};
