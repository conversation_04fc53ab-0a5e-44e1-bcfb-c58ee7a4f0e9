import React, { useState } from "react";
import { Dialog } from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/outline";

interface InvalidResourceFeedbackProps {
  isOpen: boolean;
  onClose: () => void;
  resourceId: string;
  resourceName: string;
  panType: number;
  onFeedbackResult?: (
    status: string,
    message: string,
    is_deleted?: boolean
  ) => void;
}

interface FeedbackFormData {
  invalid_type: number;
  description?: string;
  contact_info?: string;
}

export default function InvalidResourceFeedback({
  isOpen,
  onClose,
  resourceId,
  resourceName,
  panType,
  onFeedbackResult,
}: InvalidResourceFeedbackProps) {
  const [formData, setFormData] = useState<FeedbackFormData>({
    invalid_type: 1,
    description: "",
    contact_info: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch("/api/report_invalid_resource", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          resource_id: resourceId,
          pan_type: panType,
          ...formData,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        if (typeof onFeedbackResult === "function") {
          onFeedbackResult(
            "success",
            data.message || "反馈提交成功",
            data.is_deleted
          );
        }
        onClose();
      } else {
        if (typeof onFeedbackResult === "function") {
          onFeedbackResult(
            "error",
            data.message || "提交失败，请稍后重试",
            false
          );
        }
      }
    } catch {
      if (typeof onFeedbackResult === "function") {
        onFeedbackResult("error", "提交失败，请稍后重试", false);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="mx-auto max-w-md rounded-lg bg-white p-6 dark:bg-gray-800 dark:text-white">
          <div className="flex items-center justify-between mb-4">
            <Dialog.Title className="text-lg font-medium">
              资源失效反馈
            </Dialog.Title>
            <button
              type="button"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500 dark:text-gray-300 dark:hover:text-gray-200"
              title="关闭"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>

          <p className="text-sm text-gray-500 mb-4 dark:text-gray-400">
            你正在为资源{" "}
            <span className="font-semibold text-gray-800 dark:text-gray-200">
              {resourceName}
            </span>{" "}
            提交反馈。
          </p>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
                失效类型 <span className="text-red-500">*</span>
              </label>
              <select
                value={formData.invalid_type}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    invalid_type: Number(e.target.value),
                  }))
                }
                className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                required
                title="失效类型"
                aria-label="失效类型"
              >
                <option value={1}>链接错误</option>
                <option value={2}>资源失效</option>
                <option value={3}>文件不存在</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
                补充说明
              </label>
              <textarea
                value={formData.description}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                rows={3}
                placeholder="请详细描述资源失效的具体情况..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
                联系方式
              </label>
              <input
                type="text"
                value={formData.contact_info}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    contact_info: e.target.value,
                  }))
                }
                className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                placeholder="您的邮箱或其他联系方式（选填）"
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 dark:bg-blue-700 dark:hover:bg-blue-600"
              >
                {isSubmitting ? "提交中..." : "提交反馈"}
              </button>
            </div>
          </form>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
}
