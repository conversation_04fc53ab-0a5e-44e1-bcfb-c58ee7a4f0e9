import { MetadataRoute } from "next";

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      {
        userAgent: "*",
        allow: "/",
        disallow: [
          "/api/",
          "/private/",
          "/*.json$",
          "/api/resources/", // 添加禁止爬取资源API路由
        ],
      },
      {
        userAgent: "Baiduspider",
        allow: "/",
        disallow: ["/api/", "/private/", "/*.json$", "/api/resources/"],
        crawlDelay: 2, // 百度爬虫的抓取延迟
      },
      {
        userAgent: "Googlebot",
        allow: "/",
        disallow: ["/api/", "/private/", "/*.json$", "/api/resources/"],
      },
      {
        userAgent: "bingbot",
        allow: "/",
        disallow: ["/api/", "/private/", "/*.json$", "/api/resources/"],
      },
    ],
    sitemap: "https://pansoo.cn/sitemap.xml",
    host: "https://pansoo.cn",
  };
}
