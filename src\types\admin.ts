/**
 * 管理后台相关的类型定义
 */

// 仪表盘统计数据接口
export interface DashboardStats {
  totalUsers: number;
  totalResources: number;
  yesterdayResources: number;
  adminUploadedResources: number;
  userSubmittedResources: number;
  pendingTasks: number;
}

// 资源统计API响应接口
export interface ResourceStatsResponse {
  success: boolean;
  message: string;
  data: {
    total: number;
    yesterday: number;
    pending_tasks: number;
    by_source: {
      admin_uploaded: number;
      user_submitted: number;
    };
  };
}

// 统计卡片接口
export interface StatCard {
  title: string;
  value: string | number;
  change?: string;
  changeType?: "increase" | "decrease" | "neutral";
  icon: React.ComponentType<{ className?: string }>;
  href?: string;
  color: string;
}

// 系统状态接口
export interface SystemStatus {
  serverStatus: "正常" | "异常" | "维护中";
  databaseConnection: "正常" | "异常";
  storageUsage: number; // 百分比
  apiResponseTime: number; // 毫秒
}

// 最近活动接口
export interface RecentActivity {
  id: string;
  type: "user_register" | "resource_upload" | "user_feedback" | "admin_action";
  description: string;
  timestamp: string;
  user?: string;
}

// API响应基础接口
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}
