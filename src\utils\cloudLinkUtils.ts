import { checkResourceStatus, getShareLink } from "@/services/resourceService";
import { LINK_CONFIG } from "@/config/constants";

type CloudType = "baidu" | "quark" | "thunder" | "aliyun";

// 简化的链接处理逻辑，移除复杂的防重复点击管理器

interface ProcessCloudLinkParams {
  type: CloudType;
  idToUse: string;
  searchType?: "local" | "online";
  baiduLink?: string;
  quarkLink?: string;
  aliyunLink?: string;
  thunderLink?: string;
}

interface ProcessCloudLinkResult {
  success: boolean;
  url?: string;
  error?: string;
}

/**
 * 显示手动复制对话框 - 供外部调用
 */
export const showManualCopyDialog = (
  url: string,
  showToast?: (message: string, type?: "success" | "error" | "info") => void
) => {
  // 创建弹窗容器
  const modalContainer = document.createElement("div");
  modalContainer.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
  `;

  // 创建弹窗内容
  const modalContent = document.createElement("div");
  modalContent.style.cssText = `
    background-color: white;
    padding: 24px;
    border-radius: 12px;
    max-width: 90%;
    width: 500px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  `;

  // 检查暗色模式
  const isDarkMode = document.documentElement.classList.contains("dark");
  if (isDarkMode) {
    modalContent.style.backgroundColor = "#1f2937";
    modalContent.style.color = "white";
  }

  // 创建标题
  const title = document.createElement("h3");
  title.textContent = "浏览器阻止了弹窗";
  title.style.cssText = `
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: ${isDarkMode ? "#f9fafb" : "#111827"};
  `;

  // 创建说明文字
  const description = document.createElement("p");
  description.textContent = "请复制下方链接并在新标签页中打开：";
  description.style.cssText = `
    margin: 0 0 16px 0;
    font-size: 14px;
    color: ${isDarkMode ? "#d1d5db" : "#6b7280"};
  `;

  // 创建输入框
  const input = document.createElement("input");
  input.type = "text";
  input.value = url;
  input.readOnly = true;
  input.style.cssText = `
    width: 100%;
    padding: 12px;
    border: 2px solid ${isDarkMode ? "#374151" : "#d1d5db"};
    border-radius: 8px;
    font-size: 14px;
    margin-bottom: 16px;
    background-color: ${isDarkMode ? "#374151" : "#f9fafb"};
    color: ${isDarkMode ? "#f9fafb" : "#111827"};
    box-sizing: border-box;
  `;

  // 创建按钮容器
  const buttonContainer = document.createElement("div");
  buttonContainer.style.cssText = `
    display: flex;
    gap: 12px;
    justify-content: flex-end;
  `;

  // 创建复制按钮
  const copyButton = document.createElement("button");
  copyButton.textContent = "复制链接";
  copyButton.style.cssText = `
    padding: 8px 16px;
    background-color: #3b82f6;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.2s;
  `;

  // 创建打开链接按钮
  const openButton = document.createElement("button");
  openButton.textContent = "打开链接";
  openButton.style.cssText = `
    padding: 8px 16px;
    background-color: #10b981;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.2s;
  `;

  // 创建关闭按钮
  const closeButton = document.createElement("button");
  closeButton.textContent = "关闭";
  closeButton.style.cssText = `
    padding: 8px 16px;
    background-color: ${isDarkMode ? "#4b5563" : "#f3f4f6"};
    color: ${isDarkMode ? "#f9fafb" : "#374151"};
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.2s;
  `;

  // 组装弹窗
  buttonContainer.appendChild(openButton);
  buttonContainer.appendChild(copyButton);
  buttonContainer.appendChild(closeButton);
  modalContent.appendChild(title);
  modalContent.appendChild(description);
  modalContent.appendChild(input);
  modalContent.appendChild(buttonContainer);
  modalContainer.appendChild(modalContent);
  document.body.appendChild(modalContainer);

  // 选中输入框内容
  input.focus();
  input.select();

  // 打开链接按钮点击事件
  openButton.onclick = () => {
    try {
      // 直接使用window.location.href在当前标签页打开
      window.open(url, "_blank", "noopener,noreferrer");
      closeModal();
    } catch (error) {
      console.warn("Failed to open link:", error);
      showToast?.("无法打开链接，请手动复制", "error");
    }
  };

  // 复制按钮点击事件
  copyButton.onclick = async () => {
    input.select();
    try {
      // 优先使用现代的 Clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(url);
        showToast?.("链接已复制到剪贴板", "success");
      } else {
        // 回退到传统方法
        document.execCommand("copy");
        showToast?.("链接已复制到剪贴板", "success");
      }
    } catch {
      showToast?.("请手动选择并复制链接", "info");
    }
  };

  // 关闭按钮点击事件
  const closeModal = () => {
    if (document.body.contains(modalContainer)) {
      document.body.removeChild(modalContainer);
    }
  };

  closeButton.onclick = closeModal;

  // 点击背景关闭
  modalContainer.onclick = (e) => {
    if (e.target === modalContainer) {
      closeModal();
    }
  };

  // ESC键关闭
  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === "Escape") {
      closeModal();
      document.removeEventListener("keydown", handleKeyDown);
    }
  };
  document.addEventListener("keydown", handleKeyDown);

  // 自动关闭
  setTimeout(() => {
    closeModal();
    document.removeEventListener("keydown", handleKeyDown);
  }, 30000);
};

/**
 * 打开链接的通用逻辑 - 最终版本
 *
 * 基于研究和测试，弹窗阻止检测是不可靠的，因为：
 * 1. 不同浏览器的 window.open() 返回值行为不一致
 * 2. 跨域限制会影响窗口属性访问
 * 3. 用户可能在窗口打开后立即关闭，导致误判
 *
 * 最佳实践：简单调用 window.open()，不进行复杂检测
 * 只在真正需要时（用户主动请求）提供备用方案
 */
export const openLinkInNewTab = (
  url: string,
  showToast?: (message: string, type?: "success" | "error" | "info") => void
) => {
  try {
    // 简单直接地打开新窗口
    window.open(url, "_blank", "noopener,noreferrer");

    // 不进行任何检测，因为：
    // 1. 检测不可靠，容易误判
    // 2. 如果用户真的需要链接，他们会主动寻求帮助
    // 3. 现代浏览器通常会显示弹窗阻止通知
  } catch (error) {
    // 只在真正抛出异常时才显示错误
    console.warn("window.open failed:", error);
    showToast?.("无法打开链接", "error");
  }
};

/**
 * 处理云盘链接的核心逻辑
 */
export const processCloudLink = async ({
  type,
  idToUse,
  searchType = "local",
}: ProcessCloudLinkParams): Promise<ProcessCloudLinkResult> => {
  const panTypeMap: Record<string, number> = {
    baidu: 1,
    quark: 2,
    thunder: 4,
    aliyun: 3,
  };

  try {
    // 检查是否启用 get_share 接口
    const shouldUseGetShare =
      LINK_CONFIG.enableGetShareAPI &&
      LINK_CONFIG.enableGetShareForTypes.includes(type);

    // 如果禁用 get_share 接口或者当前类型不在启用列表中，只调用 check_resource_status
    if (!shouldUseGetShare) {
      const checkUrl = `/api/check_resource_status?resource_id=${idToUse}&pan_type=${panTypeMap[type]}`;
      const checkRes = await fetch(checkUrl);
      const checkData = await checkRes.json();

      if (!checkData.valid || !checkData.share_url) {
        return {
          success: false,
          error: checkData.message || "资源不可用",
        };
      }

      return {
        success: true,
        url: checkData.share_url,
      };
    }

    // 原有逻辑：启用 get_share 接口的情况
    // 迅雷和阿里云只调用check_resource_status（保持原有逻辑）
    if (type === "thunder" || type === "aliyun") {
      const checkUrl = `/api/check_resource_status?resource_id=${idToUse}&pan_type=${panTypeMap[type]}`;
      const checkRes = await fetch(checkUrl);
      const checkData = await checkRes.json();

      if (!checkData.valid || !checkData.share_url) {
        return {
          success: false,
          error: checkData.message || "资源不可用",
        };
      }

      return {
        success: true,
        url: checkData.share_url,
      };
    }

    // 百度和夸克的处理逻辑（启用 get_share 时）
    if (searchType === "online") {
      // 1. 校验资源有效性
      const status = await checkResourceStatus(idToUse, panTypeMap[type]);
      if (!status.valid) {
        return {
          success: false,
          error: status.message || "资源不可用",
        };
      }
    }

    // 2. 获取分享链接
    const shareLinkResponse = await getShareLink(type, undefined, idToUse);
    if (shareLinkResponse.status === "success" && shareLinkResponse.share_url) {
      return {
        success: true,
        url: shareLinkResponse.share_url,
      };
    } else {
      return {
        success: false,
        error: shareLinkResponse.message || "获取分享链接失败",
      };
    }
  } catch {
    return {
      success: false,
      error: "处理链接出错",
    };
  }
};

/**
 * 获取原始链接
 */
export const getOriginalLink = (
  type: CloudType,
  baiduLink?: string,
  quarkLink?: string,
  aliyunLink?: string,
  thunderLink?: string
): string => {
  switch (type) {
    case "baidu":
      return baiduLink || "";
    case "quark":
      return quarkLink || "";
    case "thunder":
      return thunderLink || "";
    case "aliyun":
      return aliyunLink || "";
    default:
      return "";
  }
};
