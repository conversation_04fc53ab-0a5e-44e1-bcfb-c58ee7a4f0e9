---
description: 
globs: 
alwaysApply: false
---
# pan-so-frontend 项目助手规则

## 1. 语言与沟通
- **主要语言**: 所有与您的沟通都将使用 **中文** 进行。
- **代码注释**: 所有新增或修改的代码注释都必须使用 **中文**，清晰地解释代码的意tu、实现逻辑或注意事项。

## 2. 技术栈与框架
- **核心框架**: 本项目基于 **Next.js** 和 **React** 构建，并使用 App Router 进行路由管理。
- **编程语言**: 项目主要使用 **TypeScript**。所有代码都应是类型安全的，请充分利用 TypeScript 的特性。
- **样式方案**: 使用 **Tailwind CSS** 进行样式开发。请遵循 `tailwind.config.js` 中的设计系统，并优先使用 `@apply` 来组合原子类。
- **代码质量**: 遵循项目已配置的 **ESLint** (`eslint.config.mjs`) 规则，以确保代码风格的统一和质量。

## 3. 文件与代码结构
- **组件命名**:
  - React 组件文件和组件本身都应使用 **PascalCase** 命名 (例如 `MyComponent.tsx`)。
- **目录结构**:
  - **通用组件**: 存放于 `src/components/` 目录，并可根据功能进行分组。
  - **页面路由**: App Router 的页面组件统一使用 `page.tsx`，布局使用 `layout.tsx`。
  - **API 路由**: 服务端 API 路由应放置在 `src/app/api/` 目录下。
  - **类型定义**: 共享的 TypeScript 类型定义应位于 `src/types/` 目录下。
  - **工具函数**: 全局通用的工具函数应放在 `src/lib/utils.ts` 或相关模块文件中。

## 4. Git 提交规范 (建议)
- **Commit Messages**: 建议遵循 Conventional Commits 规范来撰写提交信息，以提高提交历史的可读性。
  - **格式**: `<type>(<scope>): <subject>`
  - **示例**: `feat(search): add new search filter component`
  - **常用 `type`**:
    - `feat`: 新增功能
    - `fix`: 修复 Bug
    - `docs`: 修改文档
    - `style`: 代码格式调整（不影响代码逻辑）
    - `refactor`: 代码重构
    - `test`: 新增或修改测试
    - `chore`: 构建过程或辅助工具的变动
