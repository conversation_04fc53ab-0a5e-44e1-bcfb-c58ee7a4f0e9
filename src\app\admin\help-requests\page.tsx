"use client";

import { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import {
  MagnifyingGlassIcon,
  EyeIcon,
  TrashIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import { useToast } from "@/components/ToastProvider";
import AdminLayout from "@/components/admin/AdminLayout";
import { AdminGuard } from "@/components/AuthGuard";
import Pagination from "@/components/Pagination";
import {
  getAdminHelpRequests,
  adminDeleteHelpRequest,
  adminBatchDeleteHelpRequests,
  getHelpRequestStats,
} from "@/services/helpRequestService";
import {
  HelpRequest,
  AdminHelpRequestFilters,
  HelpRequestStats,
  PAN_TYPE_MAP,
  RESOURCE_TYPES,
} from "@/types/help-request";

// 统计卡片组件
function StatsCard({
  title,
  value,
  color,
  icon: Icon,
}: {
  title: string;
  value: number;
  color: string;
  icon: React.ComponentType<{ className?: string }>;
}) {
  return (
    <div className="bg-card-background border border-border-color rounded-lg p-6">
      <div className="flex items-center">
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
        <div className="ml-4">
          <p className="text-sm font-medium text-secondary-text">{title}</p>
          <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {value}
          </p>
        </div>
      </div>
    </div>
  );
}

// 筛选器组件
function HelpRequestFilters({
  filters,
  onFiltersChange,
  onSearch,
}: {
  filters: AdminHelpRequestFilters;
  onFiltersChange: (filters: AdminHelpRequestFilters) => void;
  onSearch: () => void;
}) {
  const [searchQuery, setSearchQuery] = useState(filters.search || "");

  const handleFilterChange = (
    key: keyof AdminHelpRequestFilters,
    value: any
  ) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const handleSearch = () => {
    onFiltersChange({
      ...filters,
      search: searchQuery,
    });
    onSearch();
  };

  return (
    <div className="bg-card-background rounded-lg p-4 mb-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        {/* 状态筛选 */}
        <div>
          <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
            状态
          </label>
          <select
            value={filters.status || "all"}
            onChange={(e) => handleFilterChange("status", e.target.value)}
            className="w-full px-3 py-2 border border-border-color rounded-md bg-background text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            title="选择状态筛选条件"
          >
            <option value="all">全部状态</option>
            <option value="open">求助中</option>
            <option value="solved">已解决</option>
            <option value="closed">已关闭</option>
          </select>
        </div>

        {/* 网盘类型筛选 */}
        <div>
          <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
            网盘类型
          </label>
          <select
            value={filters.pan_type || ""}
            onChange={(e) =>
              handleFilterChange(
                "pan_type",
                e.target.value ? parseInt(e.target.value) : undefined
              )
            }
            className="w-full px-3 py-2 border border-border-color rounded-md bg-background text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            title="选择网盘类型筛选条件"
          >
            <option value="">全部网盘</option>
            {Object.entries(PAN_TYPE_MAP).map(([key, value]) => (
              <option key={key} value={key}>
                {value}
              </option>
            ))}
          </select>
        </div>

        {/* 资源类型筛选 */}
        <div>
          <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
            资源类型
          </label>
          <select
            value={filters.resource_type || ""}
            onChange={(e) =>
              handleFilterChange("resource_type", e.target.value || undefined)
            }
            className="w-full px-3 py-2 border border-border-color rounded-md bg-background text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            title="选择资源类型筛选条件"
          >
            <option value="">全部类型</option>
            {RESOURCE_TYPES.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>

        {/* 排序方式 */}
        <div>
          <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
            排序方式
          </label>
          <select
            value={filters.sort_by || "latest"}
            onChange={(e) => handleFilterChange("sort_by", e.target.value)}
            className="w-full px-3 py-2 border border-border-color rounded-md bg-background text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            title="选择排序方式"
          >
            <option value="latest">最新发布</option>
            <option value="oldest">最早发布</option>
            <option value="most_answers">回答最多</option>
            <option value="most_viewed">浏览最多</option>
            <option value="unsolved">未解决</option>
          </select>
        </div>
      </div>

      {/* 搜索框 */}
      <div className="flex gap-2">
        <div className="flex-1 relative">
          <input
            type="text"
            placeholder="搜索求助标题、描述或用户名..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => e.key === "Enter" && handleSearch()}
            className="w-full px-3 py-2 pl-10 border border-border-color rounded-md bg-background text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <MagnifyingGlassIcon className="h-5 w-5 text-secondary-text absolute left-3 top-1/2 transform -translate-y-1/2" />
        </div>
        <button
          type="button"
          onClick={handleSearch}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          搜索
        </button>
      </div>
    </div>
  );
}

// 求助表格组件
function HelpRequestTable({
  helpRequests,
  selectedIds,
  onSelectionChange,
  onDelete,
}: {
  helpRequests: (HelpRequest & { user_email?: string })[];
  selectedIds: number[];
  onSelectionChange: (ids: number[]) => void;
  onDelete: (id: number) => void;
}) {
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(helpRequests.map((hr) => hr.id));
    } else {
      onSelectionChange([]);
    }
  };

  const handleSelectOne = (id: number, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedIds, id]);
    } else {
      onSelectionChange(selectedIds.filter((selectedId) => selectedId !== id));
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "solved":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "closed":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "open":
        return "求助中";
      case "solved":
        return "已解决";
      case "closed":
        return "已关闭";
      default:
        return "未知";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN");
  };

  return (
    <div className="bg-card-background border border-border-color rounded-lg overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-border-color">
          <thead className="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-text uppercase tracking-wider">
                选择
                <input
                  type="checkbox"
                  checked={
                    selectedIds.length === helpRequests.length &&
                    helpRequests.length > 0
                  }
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-border-color rounded ml-2"
                  title="全选/取消全选"
                  aria-label="全选或取消全选所有求助"
                />
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-text uppercase tracking-wider">
                求助信息
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-text uppercase tracking-wider">
                发布者
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-text uppercase tracking-wider">
                状态
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-text uppercase tracking-wider">
                统计
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-text uppercase tracking-wider">
                发布时间
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-secondary-text uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="bg-card-background divide-y divide-border-color">
            {helpRequests.map((helpRequest) => (
              <tr key={helpRequest.id} className="hover:bg-hover-background">
                <td className="px-6 py-4 whitespace-nowrap">
                  <input
                    type="checkbox"
                    checked={selectedIds.includes(helpRequest.id)}
                    onChange={(e) =>
                      handleSelectOne(helpRequest.id, e.target.checked)
                    }
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-border-color rounded"
                    title={`选择求助: ${helpRequest.title}`}
                    aria-label={`选择求助: ${helpRequest.title}`}
                  />
                </td>
                <td className="px-6 py-4">
                  <div className="max-w-xs">
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100 line-clamp-2">
                      {helpRequest.title}
                    </div>
                    {helpRequest.description && (
                      <div className="text-sm text-secondary-text line-clamp-1 mt-1">
                        {helpRequest.description}
                      </div>
                    )}
                    <div className="flex flex-wrap gap-1 mt-2">
                      {helpRequest.pan_types.slice(0, 2).map((panType) => (
                        <span
                          key={panType}
                          className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 rounded text-xs"
                        >
                          {PAN_TYPE_MAP[panType as keyof typeof PAN_TYPE_MAP]}
                        </span>
                      ))}
                      {helpRequest.pan_types.length > 2 && (
                        <span className="px-2 py-1 bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400 rounded text-xs">
                          +{helpRequest.pan_types.length - 2}
                        </span>
                      )}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-100">
                    {helpRequest.user.username}
                  </div>
                  {helpRequest.user_email && (
                    <div className="text-sm text-secondary-text">
                      {helpRequest.user_email}
                    </div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                      helpRequest.status
                    )}`}
                  >
                    {getStatusText(helpRequest.status)}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-text">
                  <div>{helpRequest.answers_count} 回答</div>
                  <div>{helpRequest.view_count} 浏览</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-text">
                  {formatDate(helpRequest.created_at)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex items-center space-x-2">
                    <Link
                      href={`/help-requests/${helpRequest.id}`}
                      target="_blank"
                      className="text-blue-600 hover:text-blue-900 transition-colors"
                      title="查看详情"
                    >
                      <EyeIcon className="h-4 w-4" />
                    </Link>
                    <button
                      type="button"
                      onClick={() => onDelete(helpRequest.id)}
                      className="text-red-600 hover:text-red-900 transition-colors"
                      title="删除求助"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

function AdminHelpRequestsContent() {
  const { showToast } = useToast();
  const [helpRequests, setHelpRequests] = useState<
    (HelpRequest & { user_email?: string })[]
  >([]);
  const [stats, setStats] = useState<HelpRequestStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [filters, setFilters] = useState<AdminHelpRequestFilters>({
    status: "all",
    sort_by: "latest",
  });

  const pageSize = 20;

  const loadStats = useCallback(async () => {
    try {
      const response = await getHelpRequestStats();
      if (response.success && response.data) {
        setStats(response.data);
      }
    } catch {
      console.error("获取求助统计失败");
    }
  }, []);

  const loadHelpRequests = useCallback(async () => {
    setLoading(true);
    try {
      const response = await getAdminHelpRequests(
        currentPage,
        pageSize,
        filters
      );
      if (response.success) {
        setHelpRequests(response.data.help_requests);
        setTotalPages(response.data.total_pages);
        setTotal(response.data.total);
      } else {
        showToast(response.error || "获取求助列表失败", "error");
      }
    } catch {
      showToast("获取求助列表失败", "error");
    } finally {
      setLoading(false);
    }
  }, [currentPage, filters, showToast]);

  useEffect(() => {
    loadStats();
  }, [loadStats]);

  useEffect(() => {
    loadHelpRequests();
  }, [loadHelpRequests]);

  const handleFiltersChange = (newFilters: AdminHelpRequestFilters) => {
    setFilters(newFilters);
    setCurrentPage(1);
    setSelectedIds([]);
  };

  const handleSearch = () => {
    setCurrentPage(1);
    setSelectedIds([]);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    setSelectedIds([]);
    window.scrollTo(0, 0);
  };

  const handleDelete = async (id: number) => {
    if (!confirm("确定要删除这个求助吗？此操作不可恢复。")) {
      return;
    }

    try {
      const response = await adminDeleteHelpRequest(id);
      if (response.success) {
        showToast("删除成功", "success");
        loadHelpRequests();
        loadStats();
      } else {
        showToast(response.message || "删除失败", "error");
      }
    } catch {
      showToast("删除失败", "error");
    }
  };

  const handleBatchDelete = async () => {
    if (selectedIds.length === 0) {
      showToast("请选择要删除的求助", "error");
      return;
    }

    if (
      !confirm(
        `确定要删除选中的 ${selectedIds.length} 个求助吗？此操作不可恢复。`
      )
    ) {
      return;
    }

    try {
      const response = await adminBatchDeleteHelpRequests(selectedIds);
      if (response.success) {
        showToast("批量删除成功", "success");
        setSelectedIds([]);
        loadHelpRequests();
        loadStats();
      } else {
        showToast(response.message || "批量删除失败", "error");
      }
    } catch {
      showToast("批量删除失败", "error");
    }
  };

  return (
    <div>
      {/* 页面头部 */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          求助管理
        </h1>
        <p className="text-secondary-text mt-1">管理用户发布的资源求助和回答</p>
      </div>

      {/* 统计卡片 */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <StatsCard
            title="总求助数"
            value={stats.total_requests}
            color="bg-blue-600"
            icon={({ className }) => <div className={className}>📝</div>}
          />
          <StatsCard
            title="求助中"
            value={stats.open_requests}
            color="bg-green-600"
            icon={({ className }) => <div className={className}>🔍</div>}
          />
          <StatsCard
            title="已解决"
            value={stats.solved_requests}
            color="bg-purple-600"
            icon={({ className }) => <div className={className}>✅</div>}
          />
          <StatsCard
            title="总回答数"
            value={stats.total_answers}
            color="bg-orange-600"
            icon={({ className }) => <div className={className}>💬</div>}
          />
        </div>
      )}

      {/* 筛选器 */}
      <HelpRequestFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onSearch={handleSearch}
      />

      {/* 批量操作 */}
      {selectedIds.length > 0 && (
        <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-800 dark:text-blue-200">
              已选择 {selectedIds.length} 个求助
            </span>
            <button
              type="button"
              onClick={handleBatchDelete}
              className="inline-flex items-center px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm"
            >
              <TrashIcon className="h-4 w-4 mr-1" />
              批量删除
            </button>
          </div>
        </div>
      )}

      {/* 统计信息 */}
      {!loading && (
        <div className="mb-4 text-sm text-secondary-text">
          共找到{" "}
          <span className="font-semibold text-gray-900 dark:text-gray-100">
            {total}
          </span>{" "}
          个求助
        </div>
      )}

      {/* 求助列表 */}
      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-secondary-text">加载中...</span>
        </div>
      ) : helpRequests.length > 0 ? (
        <HelpRequestTable
          helpRequests={helpRequests}
          selectedIds={selectedIds}
          onSelectionChange={setSelectedIds}
          onDelete={handleDelete}
        />
      ) : (
        <div className="text-center py-12">
          <ExclamationTriangleIcon className="h-12 w-12 text-secondary-text mx-auto mb-4" />
          <p className="text-secondary-text">暂无求助数据</p>
        </div>
      )}

      {/* 分页 */}
      {!loading && totalPages > 1 && (
        <div className="mt-8">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
}

export default function AdminHelpRequestsPage() {
  return (
    <AdminGuard>
      <AdminLayout>
        <AdminHelpRequestsContent />
      </AdminLayout>
    </AdminGuard>
  );
}
