"use client";

import { useState, useCallback } from "react";
import axios from "axios";
import { API_PATHS, ERROR_MESSAGES } from "@/config/constants";
import { useLoadingStore } from "@/store/loadingStore";
import { useToast } from "@/components/ToastProvider";

interface SearchResult {
  id: string;
  title: string;
  url: string;
  description: string;
  size?: string;
  type?: string;
  date?: string;
  // 其他可能的属性
}

interface SearchResponse {
  results: SearchResult[];
  total: number;
  page: number;
  totalPages: number;
}

interface UseSearchProps {
  initialQuery?: string;
  initialType?: "local" | "online";
  initialPage?: number;
}

export function useSearch({
  initialQuery = "",
  initialType = "local",
  initialPage = 1,
}: UseSearchProps = {}) {
  const [query, setQuery] = useState(initialQuery);
  const [searchType, setSearchType] = useState<"local" | "online">(initialType);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    total: 0,
    page: initialPage,
    totalPages: 0,
  });

  const { setLoading } = useLoadingStore();
  const { showToast } = useToast();

  const performSearch = useCallback(
    async (searchQuery: string, type: "local" | "online", page: number = 1) => {
      if (!searchQuery.trim()) {
        setResults([]);
        setPagination({ total: 0, page: 1, totalPages: 0 });
        return;
      }

      setIsSearching(true);
      setError(null);
      setLoading(true);

      try {
        const response = await axios.get<SearchResponse>(API_PATHS.search, {
          params: { q: searchQuery, type, page },
        });

        setResults(response.data.results);
        setPagination({
          total: response.data.total,
          page: response.data.page,
          totalPages: response.data.totalPages,
        });
      } catch (error) {
        console.error("Search error:", error);
        const errorMessage = ERROR_MESSAGES.searchFailed;
        setError(errorMessage);
        showToast(errorMessage, "error");
        setResults([]);
      } finally {
        setIsSearching(false);
        setLoading(false);
      }
    },
    [setLoading, showToast]
  );

  const search = useCallback(
    (newQuery?: string, newType?: "local" | "online") => {
      const searchQuery = newQuery !== undefined ? newQuery : query;
      const type = newType !== undefined ? newType : searchType;

      if (newQuery !== undefined) setQuery(searchQuery);
      if (newType !== undefined) setSearchType(type);

      performSearch(searchQuery, type, 1);
    },
    [query, searchType, performSearch]
  );

  const changePage = useCallback(
    (page: number) => {
      performSearch(query, searchType, page);
    },
    [query, searchType, performSearch]
  );

  return {
    query,
    setQuery,
    searchType,
    setSearchType,
    results,
    isSearching,
    error,
    pagination,
    search,
    changePage,
  };
}
