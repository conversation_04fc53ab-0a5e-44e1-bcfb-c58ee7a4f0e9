/**
 * 配置管理API服务
 */

import { apiRequest } from "./api";
import {
  ConfigTreeResponse,
  ConfigPathRequest,
  ConfigPathResponse,
  ConfigSearchResponse,
  ConfigUpdateResponse,
  ValidationResult,
  ConfigSchemaResponse,
} from "@/types/config";

/**
 * 获取配置树形结构
 */
export async function getConfigTree(
  showSensitive: boolean = false
): Promise<ConfigTreeResponse> {
  try {
    const response = await apiRequest<ConfigTreeResponse>(
      `/api/admin/config/tree?show_sensitive=${showSensitive}`,
      {
        method: "GET",
      }
    );
    return response;
  } catch (error) {
    console.error("获取配置树失败:", error);
    throw new Error("获取配置树失败");
  }
}

/**
 * 按路径获取配置
 */
export async function getConfigByPath(
  path: string,
  showSensitive: boolean = false
): Promise<ConfigPathResponse> {
  try {
    const response = await apiRequest<ConfigPathResponse>(
      `/api/admin/config/path/${encodeURIComponent(
        path
      )}?show_sensitive=${showSensitive}`,
      {
        method: "GET",
      }
    );
    return response;
  } catch (error) {
    console.error("获取配置失败:", error);
    throw new Error("获取配置失败");
  }
}

/**
 * 按路径修改配置
 */
export async function updateConfigByPath(
  path: string,
  data: ConfigPathRequest
): Promise<ConfigUpdateResponse> {
  try {
    const response = await apiRequest<ConfigUpdateResponse>(
      `/api/admin/config/path/${encodeURIComponent(path)}`,
      {
        method: "PUT",
        body: JSON.stringify(data),
      }
    );
    return response;
  } catch (error) {
    console.error("更新配置失败:", error);
    throw new Error("更新配置失败");
  }
}

/**
 * 验证配置值
 */
export async function validateConfigValue(
  path: string,
  value: any
): Promise<ValidationResult> {
  try {
    const response = await apiRequest<ValidationResult>(
      `/api/admin/config/validate?path=${encodeURIComponent(
        path
      )}&value=${encodeURIComponent(JSON.stringify(value))}`,
      {
        method: "POST",
      }
    );
    return response;
  } catch (error) {
    console.error("验证配置失败:", error);
    return {
      valid: false,
      message: "验证失败",
    };
  }
}

/**
 * 搜索配置项
 */
export async function searchConfig(
  query: string,
  showSensitive: boolean = false
): Promise<ConfigSearchResponse> {
  try {
    const response = await apiRequest<ConfigSearchResponse>(
      `/api/admin/config/search?query=${encodeURIComponent(
        query
      )}&show_sensitive=${showSensitive}`,
      {
        method: "GET",
      }
    );
    return response;
  } catch (error) {
    console.error("搜索配置失败:", error);
    throw new Error("搜索配置失败");
  }
}

/**
 * 获取配置模式
 */
export async function getConfigSchema(): Promise<ConfigSchemaResponse> {
  try {
    const response = await apiRequest<ConfigSchemaResponse>(
      "/api/admin/config/schema",
      {
        method: "GET",
      }
    );
    return response;
  } catch (error) {
    console.error("获取配置模式失败:", error);
    throw new Error("获取配置模式失败");
  }
}

/**
 * 格式化配置值显示
 */
export function formatConfigValue(value: any, type: string): string {
  if (value === null || value === undefined) {
    return "未设置";
  }

  switch (type) {
    case "boolean":
      return value ? "是" : "否";
    case "object":
    case "array":
      return JSON.stringify(value, null, 2);
    case "string":
      return String(value);
    case "number":
      return String(value);
    default:
      return String(value);
  }
}

/**
 * 解析配置值
 */
export function parseConfigValue(value: string, type: string): any {
  if (!value || value.trim() === "") {
    return null;
  }

  try {
    switch (type) {
      case "boolean":
        return (
          value.toLowerCase() === "true" || value === "1" || value === "yes"
        );
      case "number":
        const num = Number(value);
        return isNaN(num) ? null : num;
      case "object":
      case "array":
        return JSON.parse(value);
      case "string":
        return value;
      default:
        return value;
    }
  } catch (error) {
    console.error("解析配置值失败:", error);
    return value;
  }
}

/**
 * 获取配置效果类型的显示文本
 */
export function getEffectTypeText(effectType: string): string {
  switch (effectType) {
    case "immediate":
      return "立即生效";
    case "restart":
      return "重启后生效";
    case "reload":
      return "重载后生效";
    case "manual":
      return "手动生效";
    default:
      return "未知";
  }
}

/**
 * 获取配置效果类型的颜色
 */
export function getEffectTypeColor(effectType: string): string {
  switch (effectType) {
    case "immediate":
      return "text-green-600 dark:text-green-400";
    case "restart":
      return "text-red-600 dark:text-red-400";
    case "reload":
      return "text-yellow-600 dark:text-yellow-400";
    case "manual":
      return "text-blue-600 dark:text-blue-400";
    default:
      return "text-gray-600 dark:text-gray-400";
  }
}
