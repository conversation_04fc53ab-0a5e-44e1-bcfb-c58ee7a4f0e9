"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { User } from "@/services/authService";

interface UserFormData {
  username: string;
  email: string;
  password?: string;
  nickname?: string;
  role_id: number;
  status: string;
}

interface UserModalProps {
  title: string;
  user?: User;
  onSubmit: (data: UserFormData) => void;
  onCancel: () => void;
}

export default function UserModal({
  title,
  user,
  onSubmit,
  onCancel,
}: UserModalProps) {
  const [formData, setFormData] = useState<UserFormData>({
    username: "",
    email: "",
    password: "",
    nickname: "",
    role_id: 1, // 默认普通用户
    status: "active",
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (user) {
      setFormData({
        username: user.username,
        email: user.email,
        nickname: user.nickname || "",
        role_id:
          user.role.name === "admin"
            ? 3
            : user.role.name === "moderator"
            ? 2
            : 1,
        status: user.status,
      });
    }
  }, [user]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === "role_id" ? parseInt(value) : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.username.trim()) {
      alert("请输入用户名");
      return;
    }

    if (!formData.email.trim()) {
      alert("请输入邮箱");
      return;
    }

    if (!user && !formData.password?.trim()) {
      alert("请输入密码");
      return;
    }

    setLoading(true);
    try {
      await onSubmit(formData);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-card-background rounded-lg shadow-xl p-6 w-full max-w-md border border-border-color max-h-[90vh] overflow-y-auto">
        <h3 className="text-lg font-semibold text-foreground mb-6">{title}</h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              用户名 *
            </label>
            <Input
              type="text"
              name="username"
              value={formData.username}
              onChange={handleInputChange}
              placeholder="请输入用户名"
              required
              disabled={!!user} // 编辑时不允许修改用户名
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              邮箱 *
            </label>
            <Input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              placeholder="请输入邮箱"
              required
            />
          </div>

          {!user && (
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                密码 *
              </label>
              <Input
                type="password"
                name="password"
                value={formData.password || ""}
                onChange={handleInputChange}
                placeholder="请输入密码"
                required
              />
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              昵称
            </label>
            <Input
              type="text"
              name="nickname"
              value={formData.nickname || ""}
              onChange={handleInputChange}
              placeholder="请输入昵称（可选）"
            />
          </div>

          <div>
            <label
              htmlFor="user-role"
              className="block text-sm font-medium text-foreground mb-2"
            >
              角色 *
            </label>
            <select
              id="user-role"
              name="role_id"
              value={formData.role_id}
              onChange={handleInputChange}
              className="w-full px-4 py-2 rounded-md border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-foreground bg-white dark:bg-[#2c2c34] dark:border-[#4b4d61]"
              required
              aria-label="用户角色"
            >
              <option value={1}>普通用户</option>
              <option value={2}>版主</option>
              <option value={3}>管理员</option>
            </select>
          </div>

          <div>
            <label
              htmlFor="user-status"
              className="block text-sm font-medium text-foreground mb-2"
            >
              状态 *
            </label>
            <select
              id="user-status"
              name="status"
              value={formData.status}
              onChange={handleInputChange}
              className="w-full px-4 py-2 rounded-md border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-foreground bg-white dark:bg-[#2c2c34] dark:border-[#4b4d61]"
              required
              aria-label="用户状态"
            >
              <option value="active">正常</option>
              <option value="inactive">未激活</option>
              <option value="suspended">已冻结</option>
              <option value="banned">已封禁</option>
            </select>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? "处理中..." : user ? "更新" : "创建"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
