# Excel 模板导入功能使用指南

## 功能概述

Excel 模板导入功能允许用户通过上传 Excel 文件批量提交网盘资源链接，提高资源提交效率。

## 主要特性

### 1. 支持的文件格式

- `.xlsx` (Excel 2007 及以上版本)
- `.xls` (Excel 97-2003 版本)

### 2. 导入限制

- **单次最多导入**: 2000 条数据
- **文件大小限制**: 5MB
- **支持的网盘类型**:
  - 百度网盘 (pan.baidu.com)
  - 阿里云盘 (www.aliyundrive.com)
  - 夸克网盘 (pan.quark.cn)
  - 迅雷云盘 (pan.xunlei.com)

### 3. Excel 模板格式

| 列名         | 说明         | 是否必填 | 示例                             |
| ------------ | ------------ | -------- | -------------------------------- |
| URL          | 网盘资源链接 | 是       | https://pan.baidu.com/s/example1 |
| 标题（可选） | 资源标题     | 否       | 示例资源 1                       |
| 描述（可选） | 资源描述     | 否       | 这是一个示例描述                 |

## 使用步骤

### 1. 下载模板

1. 访问资源提交页面
2. 点击"Excel 导入"标签页
3. 点击"下载模板"按钮
4. 保存 Excel 模板文件到本地

### 2. 填写数据

1. 打开下载的 Excel 模板
2. 在第一列填入网盘资源链接（必填）
3. 在第二列填入资源标题（可选）
4. 在第三列填入资源描述（可选）
5. 保存 Excel 文件

### 3. 上传导入

1. 点击"选择 Excel 文件"按钮
2. 选择填写好的 Excel 文件
3. 系统自动解析并显示预览数据
4. 检查数据有效性（绿色圆点表示有效，红色圆点表示无效）
5. 点击"导入 X 条有效数据"按钮确认导入

### 4. 提交资源

1. 导入成功后，数据会自动填入手动输入区域
2. 检查导入的链接是否正确
3. 点击"提交资源"按钮完成提交

## 数据验证规则

### URL 验证

- ✅ 必须是有效的 URL 格式
- ✅ 必须是支持的网盘域名
- ❌ 不能包含中文字符
- ❌ 不能为空

### 常见错误及解决方案

| 错误信息             | 原因                   | 解决方案                                   |
| -------------------- | ---------------------- | ------------------------------------------ |
| URL 不能为空         | 第一列为空或无内容     | 确保第一列填入有效的网盘链接               |
| 无效的 URL 格式      | URL 格式不正确         | 检查 URL 是否完整，包含 http://或 https:// |
| 不支持的网盘类型     | 网盘域名不在支持列表中 | 使用支持的网盘类型                         |
| URL 不能包含中文字符 | 链接中包含中文         | 移除链接中的中文字符                       |

## 技术实现

### 前端处理

- 使用 `xlsx` 库解析 Excel 文件
- 前端验证数据格式和有效性
- 实时预览解析结果
- 批量数据处理和错误提示

### 性能优化

- 文件大小限制（5MB）
- 数据条数限制（2000 条）
- 异步处理避免页面卡顿
- 内存优化处理大文件

### 用户体验

- 拖拽上传支持
- 实时数据预览
- 详细错误提示
- 一键模板下载

## 注意事项

1. **数据安全**: 文件仅在浏览器本地解析，不会上传到服务器
2. **格式要求**: 严格按照模板格式填写数据
3. **网络稳定**: 确保网络连接稳定，避免提交过程中断
4. **重复检查**: 系统会自动检测重复资源
5. **合规要求**: 请勿提交违规内容或纯引流内容

## 常见问题

### Q: 为什么我的 Excel 文件无法解析？

A: 请检查：

- 文件格式是否为.xlsx 或.xls
- 文件是否损坏
- 第一行是否为标题行
- 数据是否从第二行开始

### Q: 为什么有些链接显示无效？

A: 可能的原因：

- URL 格式不正确
- 包含中文字符
- 不是支持的网盘类型
- 链接为空

### Q: 可以导入多少条数据？

A: 单次最多可导入 2000 条数据，如需导入更多数据，请分批处理。

### Q: 导入的数据可以修改吗？

A: 可以，导入后数据会显示在手动输入区域，您可以进行编辑后再提交。

## 更新日志

### v1.0.0 (2025-01-22)

- 初始版本发布
- 支持 Excel 文件解析和导入
- 数据验证和预览功能
- 模板下载功能
