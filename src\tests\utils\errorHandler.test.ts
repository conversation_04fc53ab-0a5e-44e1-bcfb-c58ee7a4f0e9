/**
 * 错误处理工具测试用例
 */

import { describe, it, expect } from "vitest";
import {
  parseApiError,
  handleNetworkError,
  handleAuthError,
  handleProfileError,
  getUserFriendlyMessage,
  formatErrorMessage,
  validateAvatarFile,
  validateNickname,
  type ApiError,
} from "../../utils/errorHandler";

describe("ErrorHandler", () => {
  describe("parseApiError", () => {
    it("应该解析FastAPI验证错误", () => {
      const response = {
        detail: [
          {
            type: "value_error.missing",
            msg: "field required",
            loc: ["username"],
          },
          {
            type: "value_error.email",
            msg: "invalid email format",
            loc: ["email"],
          },
        ],
      };

      const result = parseApiError(response);

      expect(result.success).toBe(false);
      expect(result.message).toBe("请求参数验证失败");
      expect(result.errors).toHaveLength(2);
      expect(result.errors![0]).toEqual({
        code: "value_error.missing",
        message: "field required",
        field: "username",
      });
    });

    it("应该解析单个错误信息", () => {
      const response = {
        detail: "用户名或密码错误",
      };

      const result = parseApiError(response);

      expect(result.success).toBe(false);
      expect(result.message).toBe("用户名或密码错误");
      expect(result.error).toBe("用户名或密码错误");
    });

    it("应该解析自定义错误格式", () => {
      const response = {
        message: "操作失败",
        error: "OPERATION_FAILED",
      };

      const result = parseApiError(response);

      expect(result.success).toBe(false);
      expect(result.message).toBe("操作失败");
      expect(result.error).toBe("OPERATION_FAILED");
    });

    it("应该处理未知错误格式", () => {
      const response = {
        unknown: "unknown error",
      };

      const result = parseApiError(response);

      expect(result.success).toBe(false);
      expect(result.message).toBe("请求失败");
      expect(result.error).toBe("未知错误");
    });
  });

  describe("handleNetworkError", () => {
    it("应该处理网络连接错误", () => {
      const error = new TypeError("Failed to fetch");

      const result = handleNetworkError(error);

      expect(result.success).toBe(false);
      expect(result.message).toBe("网络连接失败，请检查网络连接");
      expect(result.error).toBe("网络错误");
    });

    it("应该处理请求超时错误", () => {
      const error = new Error("Request timeout");
      error.name = "AbortError";

      const result = handleNetworkError(error);

      expect(result.success).toBe(false);
      expect(result.message).toBe("请求超时，请稍后重试");
      expect(result.error).toBe("请求超时");
    });

    it("应该处理一般网络错误", () => {
      const error = new Error("Network error");

      const result = handleNetworkError(error);

      expect(result.success).toBe(false);
      expect(result.message).toBe("网络错误，请稍后重试");
      expect(result.error).toBe("Network error");
    });
  });

  describe("handleAuthError", () => {
    it("应该处理401未授权错误", () => {
      const result = handleAuthError(401, {});

      expect(result.success).toBe(false);
      expect(result.message).toBe("登录已过期，请重新登录");
      expect(result.error).toBe("未授权");
    });

    it("应该处理403权限不足错误", () => {
      const result = handleAuthError(403, {});

      expect(result.success).toBe(false);
      expect(result.message).toBe("权限不足，无法访问此资源");
      expect(result.error).toBe("权限不足");
    });

    it("应该处理422验证错误", () => {
      const response = {
        detail: [
          {
            type: "value_error.missing",
            msg: "field required",
            loc: ["username"],
          },
        ],
      };

      const result = handleAuthError(422, response);

      expect(result.success).toBe(false);
      expect(result.message).toBe("请求参数验证失败");
      expect(result.errors).toHaveLength(1);
    });

    it("应该处理其他状态码", () => {
      const response = {
        detail: "服务器内部错误",
      };

      const result = handleAuthError(500, response);

      expect(result.success).toBe(false);
      expect(result.message).toBe("服务器内部错误");
    });
  });

  describe("getUserFriendlyMessage", () => {
    it("应该返回用户友好的验证错误消息", () => {
      const error: ApiError = {
        code: "value_error.missing",
        message: "field required",
      };

      const result = getUserFriendlyMessage(error);

      expect(result).toBe("必填字段不能为空");
    });

    it("应该返回用户友好的认证错误消息", () => {
      const error: ApiError = {
        code: "invalid_credentials",
        message: "Invalid username or password",
      };

      const result = getUserFriendlyMessage(error);

      expect(result).toBe("用户名或密码错误");
    });

    it("应该在没有匹配的错误码时返回原始消息", () => {
      const error: ApiError = {
        code: "unknown_error",
        message: "Unknown error occurred",
      };

      const result = getUserFriendlyMessage(error);

      expect(result).toBe("Unknown error occurred");
    });

    it("应该在没有错误码时返回原始消息", () => {
      const error: ApiError = {
        message: "Some error message",
      };

      const result = getUserFriendlyMessage(error);

      expect(result).toBe("Some error message");
    });
  });

  describe("formatErrorMessage", () => {
    it("应该格式化包含多个错误的响应", () => {
      const errorResponse = {
        success: false as const,
        message: "请求参数验证失败",
        errors: [
          {
            code: "value_error.missing",
            message: "field required",
            field: "username",
          },
          {
            code: "value_error.email",
            message: "invalid email format",
            field: "email",
          },
        ],
      };

      const result = formatErrorMessage(errorResponse);

      expect(result).toBe("必填字段不能为空");
    });

    it("应该格式化单个错误的响应", () => {
      const errorResponse = {
        success: false as const,
        message: "用户名或密码错误",
      };

      const result = formatErrorMessage(errorResponse);

      expect(result).toBe("用户名或密码错误");
    });

    it("应该处理空错误数组", () => {
      const errorResponse = {
        success: false as const,
        message: "操作失败",
        errors: [],
      };

      const result = formatErrorMessage(errorResponse);

      expect(result).toBe("操作失败");
    });
  });

  describe("handleProfileError", () => {
    it("应该处理头像文件大小错误", () => {
      const response = {
        detail: "file size too large",
      };

      const result = handleProfileError(400, response);

      expect(result.success).toBe(false);
      expect(result.message).toBe("头像文件过大，请选择小于2MB的图片");
      expect(result.error).toBe("avatar_too_large");
    });

    it("应该处理头像文件格式错误", () => {
      const response = {
        detail: "invalid file format",
      };

      const result = handleProfileError(400, response);

      expect(result.success).toBe(false);
      expect(result.message).toBe("头像格式不支持，请选择JPG、PNG或GIF格式");
      expect(result.error).toBe("avatar_invalid_format");
    });

    it("应该处理昵称长度错误", () => {
      const response = {
        detail: "nickname too short",
      };

      const result = handleProfileError(400, response);

      expect(result.success).toBe(false);
      expect(result.message).toBe("昵称长度不能少于2个字符");
      expect(result.error).toBe("nickname_too_short");
    });

    it("应该处理昵称已被使用错误", () => {
      const response = {
        detail: "nickname already taken",
      };

      const result = handleProfileError(400, response);

      expect(result.success).toBe(false);
      expect(result.message).toBe("该昵称已被使用");
      expect(result.error).toBe("nickname_already_taken");
    });

    it("应该处理邮箱已被使用错误", () => {
      const response = {
        detail: "email already in use",
      };

      const result = handleProfileError(400, response);

      expect(result.success).toBe(false);
      expect(result.message).toBe("该邮箱已被其他用户使用");
      expect(result.error).toBe("email_already_in_use");
    });

    it("应该处理413文件过大错误", () => {
      const result = handleProfileError(413, {});

      expect(result.success).toBe(false);
      expect(result.message).toBe("文件过大，请选择较小的文件");
      expect(result.error).toBe("文件过大");
    });

    it("应该处理429频率限制错误", () => {
      const result = handleProfileError(429, {});

      expect(result.success).toBe(false);
      expect(result.message).toBe("操作过于频繁，请稍后再试");
      expect(result.error).toBe("请求过于频繁");
    });
  });

  describe("validateAvatarFile", () => {
    it("应该验证有效的头像文件", () => {
      const file = new File(["test"], "avatar.jpg", { type: "image/jpeg" });
      Object.defineProperty(file, "size", { value: 1024 * 1024 }); // 1MB

      const result = validateAvatarFile(file);

      expect(result.valid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it("应该拒绝过大的文件", () => {
      const file = new File(["test"], "avatar.jpg", { type: "image/jpeg" });
      Object.defineProperty(file, "size", { value: 3 * 1024 * 1024 }); // 3MB

      const result = validateAvatarFile(file);

      expect(result.valid).toBe(false);
      expect(result.error).toBe("头像文件过大，请选择小于2MB的图片");
    });

    it("应该拒绝不支持的文件类型", () => {
      const file = new File(["test"], "avatar.txt", { type: "text/plain" });
      Object.defineProperty(file, "size", { value: 1024 });

      const result = validateAvatarFile(file);

      expect(result.valid).toBe(false);
      expect(result.error).toBe("头像格式不支持，请选择JPG、PNG或GIF格式");
    });

    it("应该接受PNG文件", () => {
      const file = new File(["test"], "avatar.png", { type: "image/png" });
      Object.defineProperty(file, "size", { value: 1024 });

      const result = validateAvatarFile(file);

      expect(result.valid).toBe(true);
    });

    it("应该接受GIF文件", () => {
      const file = new File(["test"], "avatar.gif", { type: "image/gif" });
      Object.defineProperty(file, "size", { value: 1024 });

      const result = validateAvatarFile(file);

      expect(result.valid).toBe(true);
    });
  });

  describe("validateNickname", () => {
    it("应该验证有效的昵称", () => {
      const result = validateNickname("测试昵称");

      expect(result.valid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it("应该验证英文昵称", () => {
      const result = validateNickname("TestUser");

      expect(result.valid).toBe(true);
    });

    it("应该验证包含数字的昵称", () => {
      const result = validateNickname("User123");

      expect(result.valid).toBe(true);
    });

    it("应该验证包含下划线的昵称", () => {
      const result = validateNickname("test_user");

      expect(result.valid).toBe(true);
    });

    it("应该拒绝过短的昵称", () => {
      const result = validateNickname("a");

      expect(result.valid).toBe(false);
      expect(result.error).toBe("昵称长度不能少于2个字符");
    });

    it("应该拒绝空昵称", () => {
      const result = validateNickname("");

      expect(result.valid).toBe(false);
      expect(result.error).toBe("昵称长度不能少于2个字符");
    });

    it("应该拒绝只有空格的昵称", () => {
      const result = validateNickname("   ");

      expect(result.valid).toBe(false);
      expect(result.error).toBe("昵称长度不能少于2个字符");
    });

    it("应该拒绝过长的昵称", () => {
      const result = validateNickname("a".repeat(21));

      expect(result.valid).toBe(false);
      expect(result.error).toBe("昵称长度不能超过20个字符");
    });

    it("应该拒绝包含特殊字符的昵称", () => {
      const result = validateNickname("test@user");

      expect(result.valid).toBe(false);
      expect(result.error).toBe("昵称只能包含中文、英文、数字和下划线");
    });

    it("应该拒绝包含空格的昵称", () => {
      const result = validateNickname("test user");

      expect(result.valid).toBe(false);
      expect(result.error).toBe("昵称只能包含中文、英文、数字和下划线");
    });

    it("应该拒绝包含符号的昵称", () => {
      const result = validateNickname("test-user");

      expect(result.valid).toBe(false);
      expect(result.error).toBe("昵称只能包含中文、英文、数字和下划线");
    });
  });
});
