/**
 * 用户等级系统相关的类型定义
 */

// 用户等级信息
export interface UserLevel {
  level: number;
  title: string;
  min_points: number;
  max_points: number;
  color: string;
  icon?: string;
  description?: string;
}

// 用户统计信息
export interface UserStats {
  help_requests_count: number;      // 发布的求助数量
  answers_count: number;            // 回答的数量
  best_answers_count: number;       // 被采纳的最佳答案数量
  points: number;                   // 积分
  level: UserLevel;                 // 当前等级
  next_level?: UserLevel;           // 下一等级
  points_to_next_level?: number;    // 距离下一等级所需积分
}

// 积分操作类型
export type PointsActionType = 
  | 'help_request_created'          // 发布求助
  | 'answer_created'                // 回答求助
  | 'answer_adopted'                // 答案被采纳
  | 'help_request_solved'           // 求助被解决
  | 'daily_login'                   // 每日登录
  | 'profile_completed'             // 完善资料
  | 'admin_reward'                  // 管理员奖励
  | 'admin_penalty';                // 管理员惩罚

// 积分记录
export interface PointsRecord {
  id: number;
  user_id: number;
  action_type: PointsActionType;
  points: number;                   // 正数为获得，负数为扣除
  description: string;
  related_id?: number;              // 相关的求助或回答ID
  created_at: string;
}

// 等级配置（可以从后端获取或在前端定义）
export const DEFAULT_USER_LEVELS: UserLevel[] = [
  {
    level: 1,
    title: '新手',
    min_points: 0,
    max_points: 99,
    color: '#9CA3AF',
    icon: '🌱',
    description: '刚刚加入社区的新用户'
  },
  {
    level: 2,
    title: '初学者',
    min_points: 100,
    max_points: 299,
    color: '#10B981',
    icon: '🌿',
    description: '开始参与社区活动'
  },
  {
    level: 3,
    title: '活跃用户',
    min_points: 300,
    max_points: 699,
    color: '#3B82F6',
    icon: '⭐',
    description: '积极参与求助和回答'
  },
  {
    level: 4,
    title: '资深用户',
    min_points: 700,
    max_points: 1499,
    color: '#8B5CF6',
    icon: '💎',
    description: '经验丰富的社区成员'
  },
  {
    level: 5,
    title: '专家',
    min_points: 1500,
    max_points: 2999,
    color: '#F59E0B',
    icon: '👑',
    description: '在某个领域有专业知识'
  },
  {
    level: 6,
    title: '大师',
    min_points: 3000,
    max_points: 9999999,
    color: '#EF4444',
    icon: '🏆',
    description: '社区的顶级贡献者'
  }
];

// 积分规则配置
export const POINTS_RULES = {
  help_request_created: 5,          // 发布求助获得积分
  answer_created: 10,               // 回答求助获得积分
  answer_adopted: 50,               // 答案被采纳获得积分
  help_request_solved: 20,          // 求助被解决获得积分
  daily_login: 2,                   // 每日登录获得积分
  profile_completed: 30,            // 完善资料获得积分
} as const;

// 用户头衔/徽章
export interface UserBadge {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  condition: string;                // 获得条件描述
  is_rare?: boolean;                // 是否为稀有徽章
}

// 预定义徽章
export const DEFAULT_BADGES: UserBadge[] = [
  {
    id: 'first_help_request',
    name: '初次求助',
    description: '发布了第一个求助',
    icon: '🎯',
    color: '#10B981',
    condition: '发布第一个求助'
  },
  {
    id: 'first_answer',
    name: '乐于助人',
    description: '回答了第一个求助',
    icon: '🤝',
    color: '#3B82F6',
    condition: '回答第一个求助'
  },
  {
    id: 'first_best_answer',
    name: '最佳回答',
    description: '获得了第一个最佳答案',
    icon: '🌟',
    color: '#F59E0B',
    condition: '获得第一个最佳答案'
  },
  {
    id: 'helpful_user',
    name: '热心用户',
    description: '获得了10个最佳答案',
    icon: '💝',
    color: '#EF4444',
    condition: '获得10个最佳答案'
  },
  {
    id: 'expert_helper',
    name: '专家助手',
    description: '获得了50个最佳答案',
    icon: '🎖️',
    color: '#8B5CF6',
    condition: '获得50个最佳答案',
    is_rare: true
  },
  {
    id: 'community_legend',
    name: '社区传奇',
    description: '获得了100个最佳答案',
    icon: '👑',
    color: '#F59E0B',
    condition: '获得100个最佳答案',
    is_rare: true
  }
];

// 用户完整信息（包含等级和徽章）
export interface UserProfile extends UserStats {
  badges: UserBadge[];              // 用户拥有的徽章
  recent_points: PointsRecord[];    // 最近的积分记录
}
