/**
 * 登录页面测试用例
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useRouter, useSearchParams } from 'next/navigation';
import LoginPage from '../../../app/auth/login/page';
import { useAuth } from '../../../hooks/useAuth';
import { login } from '../../../services/authService';

// Mock dependencies
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
  useSearchParams: vi.fn(),
}));

vi.mock('../../../hooks/useAuth', () => ({
  useAuth: vi.fn(),
}));

vi.mock('../../../services/authService', () => ({
  login: vi.fn(),
}));

// Mock components
vi.mock('../../../components/layout/PageContainer', () => ({
  PageContainer: ({ children }: { children: React.ReactNode }) => <div data-testid="page-container">{children}</div>,
}));

describe('LoginPage', () => {
  const mockPush = vi.fn();
  const mockReplace = vi.fn();
  const mockSetUser = vi.fn();
  const mockGet = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    
    (useRouter as any).mockReturnValue({
      push: mockPush,
      replace: mockReplace,
    });

    (useSearchParams as any).mockReturnValue({
      get: mockGet,
    });

    (useAuth as any).mockReturnValue({
      isAuthenticated: false,
      setUser: mockSetUser,
    });

    mockGet.mockReturnValue(null);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('应该渲染登录表单', () => {
    render(<LoginPage />);

    expect(screen.getByText('登录账户')).toBeInTheDocument();
    expect(screen.getByText('输入您的用户名和密码以登录')).toBeInTheDocument();
    expect(screen.getByLabelText('用户名或邮箱')).toBeInTheDocument();
    expect(screen.getByLabelText('密码')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /登录/i })).toBeInTheDocument();
  });

  it('应该显示记住我选项和忘记密码链接', () => {
    render(<LoginPage />);

    expect(screen.getByLabelText('记住我')).toBeInTheDocument();
    expect(screen.getByText('忘记密码？')).toBeInTheDocument();
    expect(screen.getByText('立即注册')).toBeInTheDocument();
  });

  it('应该在已登录时显示加载状态', () => {
    (useAuth as any).mockReturnValue({
      isAuthenticated: true,
      setUser: mockSetUser,
    });

    render(<LoginPage />);

    expect(screen.getByText('正在跳转...')).toBeInTheDocument();
  });

  it('应该显示URL参数中的消息', () => {
    mockGet.mockImplementation((key: string) => {
      if (key === 'message') return 'logout';
      return null;
    });

    render(<LoginPage />);

    expect(screen.getByText('已成功登出')).toBeInTheDocument();
  });

  it('应该验证必填字段', async () => {
    render(<LoginPage />);

    const submitButton = screen.getByRole('button', { name: /登录/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('请填写用户名和密码')).toBeInTheDocument();
    });
  });

  it('应该切换密码可见性', () => {
    render(<LoginPage />);

    const passwordInput = screen.getByLabelText('密码') as HTMLInputElement;
    const toggleButton = screen.getByRole('button', { name: '' }); // 密码切换按钮

    expect(passwordInput.type).toBe('password');

    fireEvent.click(toggleButton);
    expect(passwordInput.type).toBe('text');

    fireEvent.click(toggleButton);
    expect(passwordInput.type).toBe('password');
  });

  it('应该处理成功登录', async () => {
    const mockUser = {
      id: 1,
      username: 'testuser',
      email: '<EMAIL>',
    };

    (login as any).mockResolvedValue({
      success: true,
      data: { user: mockUser },
    });

    render(<LoginPage />);

    const usernameInput = screen.getByLabelText('用户名或邮箱');
    const passwordInput = screen.getByLabelText('密码');
    const submitButton = screen.getByRole('button', { name: /登录/i });

    fireEvent.change(usernameInput, { target: { value: 'testuser' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(login).toHaveBeenCalledWith({
        username: 'testuser',
        password: 'password123',
        remember_me: false,
      });
    });

    await waitFor(() => {
      expect(screen.getByText('登录成功，正在跳转...')).toBeInTheDocument();
    });

    expect(mockSetUser).toHaveBeenCalledWith(mockUser);
  });

  it('应该处理登录失败', async () => {
    (login as any).mockResolvedValue({
      success: false,
      message: '用户名或密码错误',
    });

    render(<LoginPage />);

    const usernameInput = screen.getByLabelText('用户名或邮箱');
    const passwordInput = screen.getByLabelText('密码');
    const submitButton = screen.getByRole('button', { name: /登录/i });

    fireEvent.change(usernameInput, { target: { value: 'testuser' } });
    fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('用户名或密码错误')).toBeInTheDocument();
    });
  });

  it('应该处理网络错误', async () => {
    (login as any).mockRejectedValue(new Error('Network error'));

    render(<LoginPage />);

    const usernameInput = screen.getByLabelText('用户名或邮箱');
    const passwordInput = screen.getByLabelText('密码');
    const submitButton = screen.getByRole('button', { name: /登录/i });

    fireEvent.change(usernameInput, { target: { value: 'testuser' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('网络错误，请稍后重试')).toBeInTheDocument();
    });
  });

  it('应该处理记住我选项', () => {
    render(<LoginPage />);

    const rememberCheckbox = screen.getByLabelText('记住我') as HTMLInputElement;
    
    expect(rememberCheckbox.checked).toBe(false);
    
    fireEvent.click(rememberCheckbox);
    expect(rememberCheckbox.checked).toBe(true);
  });

  it('应该在提交时禁用表单', async () => {
    (login as any).mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)));

    render(<LoginPage />);

    const usernameInput = screen.getByLabelText('用户名或邮箱');
    const passwordInput = screen.getByLabelText('密码');
    const submitButton = screen.getByRole('button', { name: /登录/i });

    fireEvent.change(usernameInput, { target: { value: 'testuser' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('登录中...')).toBeInTheDocument();
      expect(submitButton).toBeDisabled();
      expect(usernameInput).toBeDisabled();
      expect(passwordInput).toBeDisabled();
    });
  });

  it('应该正确处理重定向URL', () => {
    mockGet.mockImplementation((key: string) => {
      if (key === 'redirect') return '/profile';
      return null;
    });

    (useAuth as any).mockReturnValue({
      isAuthenticated: true,
      setUser: mockSetUser,
    });

    render(<LoginPage />);

    expect(mockReplace).toHaveBeenCalledWith('/profile');
  });
});
