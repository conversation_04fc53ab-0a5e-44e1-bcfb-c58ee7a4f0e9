/**
 * 增强的API客户端
 * 提供统一的错误处理、重试机制和连接诊断
 */

import { 
  handleNetworkError, 
  detectNetworkErrorType, 
  NetworkErrorType,
  type ErrorResponse 
} from './errorHandler';

// 获取API基础URL
const getApiBaseUrl = () => {
  return process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000";
};

export interface ApiRequestOptions extends RequestInit {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  skipAuth?: boolean;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: any[];
}

/**
 * 增强的fetch包装器，带有重试和错误处理
 */
export async function enhancedFetch<T = any>(
  url: string,
  options: ApiRequestOptions = {}
): Promise<ApiResponse<T>> {
  const {
    timeout = 10000,
    retries = 2,
    retryDelay = 1000,
    skipAuth = false,
    ...fetchOptions
  } = options;

  // 构建完整URL
  const fullUrl = url.startsWith('http') ? url : `${getApiBaseUrl()}${url}`;
  
  // 设置默认headers
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...fetchOptions.headers as Record<string, string>,
  };

  // 添加认证头
  if (!skipAuth && typeof window !== 'undefined') {
    const token = localStorage.getItem('auth_token');
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
  }

  // 创建AbortController用于超时控制
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  let lastError: Error | null = null;

  // 重试逻辑
  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      console.log(`🔄 API请求 (尝试 ${attempt + 1}/${retries + 1}):`, {
        url: fullUrl,
        method: fetchOptions.method || 'GET',
        hasAuth: !!headers.Authorization,
      });

      const response = await fetch(fullUrl, {
        ...fetchOptions,
        headers,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      console.log(`📡 API响应:`, {
        url: fullUrl,
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
      });

      // 解析响应
      let responseData;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      if (!response.ok) {
        // HTTP错误状态码
        const errorMessage = typeof responseData === 'object' 
          ? responseData.detail || responseData.message || `HTTP ${response.status}`
          : `HTTP ${response.status}: ${response.statusText}`;

        return {
          success: false,
          message: errorMessage,
          error: errorMessage,
          data: responseData,
        };
      }

      // 成功响应
      return {
        success: true,
        data: responseData,
        message: typeof responseData === 'object' ? responseData.message : undefined,
      };

    } catch (error) {
      clearTimeout(timeoutId);
      lastError = error as Error;
      
      console.error(`❌ API请求失败 (尝试 ${attempt + 1}/${retries + 1}):`, {
        url: fullUrl,
        error: lastError.message,
        errorType: detectNetworkErrorType(lastError),
      });

      // 如果是最后一次尝试，或者是不应该重试的错误，直接返回
      if (attempt === retries || !shouldRetry(lastError)) {
        break;
      }

      // 等待后重试
      if (attempt < retries) {
        console.log(`⏳ ${retryDelay}ms后重试...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }
  }

  // 所有重试都失败了
  if (lastError) {
    const errorResponse = handleNetworkError(lastError);
    return {
      success: false,
      message: errorResponse.message,
      error: errorResponse.error,
      errors: errorResponse.errors,
    };
  }

  return {
    success: false,
    message: '未知错误',
    error: '请求失败但没有错误信息',
  };
}

/**
 * 判断错误是否应该重试
 */
function shouldRetry(error: Error): boolean {
  const errorType = detectNetworkErrorType(error);
  
  // 这些错误类型应该重试
  const retryableErrors = [
    NetworkErrorType.FETCH_FAILED,
    NetworkErrorType.TIMEOUT,
    NetworkErrorType.CONNECTION_REFUSED,
    NetworkErrorType.UNKNOWN,
  ];
  
  return retryableErrors.includes(errorType);
}

/**
 * GET请求
 */
export async function apiGet<T = any>(
  url: string, 
  options: Omit<ApiRequestOptions, 'method'> = {}
): Promise<ApiResponse<T>> {
  return enhancedFetch<T>(url, { ...options, method: 'GET' });
}

/**
 * POST请求
 */
export async function apiPost<T = any>(
  url: string, 
  data?: any, 
  options: Omit<ApiRequestOptions, 'method' | 'body'> = {}
): Promise<ApiResponse<T>> {
  return enhancedFetch<T>(url, {
    ...options,
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  });
}

/**
 * PUT请求
 */
export async function apiPut<T = any>(
  url: string, 
  data?: any, 
  options: Omit<ApiRequestOptions, 'method' | 'body'> = {}
): Promise<ApiResponse<T>> {
  return enhancedFetch<T>(url, {
    ...options,
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
  });
}

/**
 * DELETE请求
 */
export async function apiDelete<T = any>(
  url: string, 
  options: Omit<ApiRequestOptions, 'method'> = {}
): Promise<ApiResponse<T>> {
  return enhancedFetch<T>(url, { ...options, method: 'DELETE' });
}

/**
 * 测试API连接
 */
export async function testApiConnection(): Promise<{
  success: boolean;
  message: string;
  details: any;
}> {
  try {
    const result = await apiGet('/', { skipAuth: true, timeout: 5000, retries: 0 });
    
    return {
      success: result.success,
      message: result.success ? 'API连接正常' : `API连接失败: ${result.message}`,
      details: {
        apiUrl: getApiBaseUrl(),
        response: result,
      }
    };
  } catch (error) {
    return {
      success: false,
      message: `API连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`,
      details: {
        apiUrl: getApiBaseUrl(),
        error: error instanceof Error ? error.message : '未知错误',
      }
    };
  }
}
