"use client";

import { memo } from "react";
import ResourceCardHeader from "./ResourceCardHeader";
import ResourceCardDescription from "./ResourceCardDescription";
import ResourceCardPreview from "./ResourceCardPreview";
import ResourceCardMetadata from "./ResourceCardMetadata";
import CloudLinksSection from "./CloudLinksSection";
import ResourceCardFeedback from "./ResourceCardFeedback";
import InvalidResourceFeedback from "./InvalidResourceFeedback";
import { useClipboard } from "@/hooks/useClipboard";
import { useCloudLinkHandler } from "@/hooks/useCloudLinkHandler";
import { useResourceFeedback } from "@/hooks/useResourceFeedback";

interface ResourceCardProps {
  id: string;
  name: string;
  description: string;
  type: string;
  size?: string;
  baiduLink?: string;
  quarkLink?: string;
  aliyunLink?: string;
  thunderLink?: string;
  date: string;
  resourceId?: string;
  searchType?: "local" | "online";
  isLocal?: boolean;
  onDelete?: () => void;
  text_content?: string;
  file_type?: string;
}

/**
 * 资源卡片组件
 * 用于展示搜索结果中的单个资源项
 * 使用React.memo优化渲染性能，仅在props变化时重新渲染
 */
const ResourceCard = memo(function ResourceCard({
  id,
  name,
  description,
  type,
  baiduLink,
  quarkLink,
  aliyunLink,
  thunderLink,
  date,
  resourceId,
  searchType = "local",
  isLocal,
  onDelete,
  text_content,
  file_type,
}: ResourceCardProps) {
  // 使用自定义 Hooks
  const { copied, copyToClipboard } = useClipboard();

  const {
    updatedLinks,
    linkUpdated,
    loadingLinks,
    resourceStatus,
    handleOpenLink,
    handleCopyLink,
  } = useCloudLinkHandler({
    id,
    resourceId,
    searchType,
    baiduLink,
    quarkLink,
    aliyunLink,
    thunderLink,
  });

  const {
    showFeedbackModal,
    isRemoving,
    handleReportInvalid,
    closeModal,
    handleFeedbackResult,
    getPanTypeMap,
  } = useResourceFeedback({ onDelete });

  // 处理复制链接的适配器函数
  const handleCopyLinkAdapter = (
    linkType: "baiduLink" | "quarkLink" | "thunderLink" | "aliyunLink"
  ) => {
    handleCopyLink(linkType, copyToClipboard);
  };

  // 检查是否禁用操作
  const isDisabled = isRemoving;

  return (
    <>
      {showFeedbackModal && (
        <InvalidResourceFeedback
          isOpen={showFeedbackModal}
          resourceId={resourceId || id}
          resourceName={name}
          panType={getPanTypeMap(type)}
          onClose={closeModal}
          onFeedbackResult={handleFeedbackResult}
        />
      )}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
        <ResourceCardHeader
          name={name}
          type={type}
          resourceId={resourceId}
          isLocal={isLocal}
        />

        <ResourceCardDescription description={description} />

        <ResourceCardPreview textContent={text_content} />

        <ResourceCardMetadata fileType={file_type} date={date} />

        <CloudLinksSection
          type={type}
          baiduLink={baiduLink}
          quarkLink={quarkLink}
          aliyunLink={aliyunLink}
          thunderLink={thunderLink}
          updatedLinks={updatedLinks}
          loadingLinks={loadingLinks}
          resourceStatus={resourceStatus}
          linkUpdated={linkUpdated}
          copied={copied}
          isDisabled={isDisabled}
          onOpenLink={handleOpenLink}
          onCopyLink={handleCopyLinkAdapter}
        />

        <ResourceCardFeedback
          searchType={searchType}
          onReportInvalid={handleReportInvalid}
        />
      </div>
    </>
  );
});

export default ResourceCard;
