/**
 * 注册页面测试用例
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import RegisterPage from '../../../app/auth/register/page';
import { useAuth } from '../../../hooks/useAuth';
import { register } from '../../../services/authService';

// Mock dependencies
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}));

vi.mock('../../../hooks/useAuth', () => ({
  useAuth: vi.fn(),
}));

vi.mock('../../../services/authService', () => ({
  register: vi.fn(),
}));

// Mock components
vi.mock('../../../components/layout/PageContainer', () => ({
  PageContainer: ({ children }: { children: React.ReactNode }) => <div data-testid="page-container">{children}</div>,
}));

describe('RegisterPage', () => {
  const mockPush = vi.fn();
  const mockReplace = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    
    (useRouter as any).mockReturnValue({
      push: mockPush,
      replace: mockReplace,
    });

    (useAuth as any).mockReturnValue({
      isAuthenticated: false,
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('应该渲染注册表单', () => {
    render(<RegisterPage />);

    expect(screen.getByText('创建账户')).toBeInTheDocument();
    expect(screen.getByText('填写以下信息创建您的账户')).toBeInTheDocument();
    expect(screen.getByLabelText('用户名')).toBeInTheDocument();
    expect(screen.getByLabelText('邮箱地址')).toBeInTheDocument();
    expect(screen.getByLabelText('密码')).toBeInTheDocument();
    expect(screen.getByLabelText('确认密码')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /创建账户/i })).toBeInTheDocument();
  });

  it('应该显示服务条款和隐私政策链接', () => {
    render(<RegisterPage />);

    expect(screen.getByText(/我已阅读并同意/)).toBeInTheDocument();
    expect(screen.getByText('服务条款')).toBeInTheDocument();
    expect(screen.getByText('隐私政策')).toBeInTheDocument();
    expect(screen.getByText('立即登录')).toBeInTheDocument();
  });

  it('应该在已登录时显示加载状态', () => {
    (useAuth as any).mockReturnValue({
      isAuthenticated: true,
    });

    render(<RegisterPage />);

    expect(screen.getByText('正在跳转...')).toBeInTheDocument();
  });

  it('应该验证用户名长度', async () => {
    render(<RegisterPage />);

    const usernameInput = screen.getByLabelText('用户名');
    const submitButton = screen.getByRole('button', { name: /创建账户/i });

    fireEvent.change(usernameInput, { target: { value: 'ab' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('用户名长度不能少于3个字符')).toBeInTheDocument();
    });
  });

  it('应该验证邮箱格式', async () => {
    render(<RegisterPage />);

    const usernameInput = screen.getByLabelText('用户名');
    const emailInput = screen.getByLabelText('邮箱地址');
    const submitButton = screen.getByRole('button', { name: /创建账户/i });

    fireEvent.change(usernameInput, { target: { value: 'testuser' } });
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('请输入有效的邮箱地址')).toBeInTheDocument();
    });
  });

  it('应该验证密码长度', async () => {
    render(<RegisterPage />);

    const usernameInput = screen.getByLabelText('用户名');
    const emailInput = screen.getByLabelText('邮箱地址');
    const passwordInput = screen.getByLabelText('密码');
    const submitButton = screen.getByRole('button', { name: /创建账户/i });

    fireEvent.change(usernameInput, { target: { value: 'testuser' } });
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: '123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('密码长度不能少于8个字符')).toBeInTheDocument();
    });
  });

  it('应该验证密码确认', async () => {
    render(<RegisterPage />);

    const usernameInput = screen.getByLabelText('用户名');
    const emailInput = screen.getByLabelText('邮箱地址');
    const passwordInput = screen.getByLabelText('密码');
    const confirmPasswordInput = screen.getByLabelText('确认密码');
    const submitButton = screen.getByRole('button', { name: /创建账户/i });

    fireEvent.change(usernameInput, { target: { value: 'testuser' } });
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.change(confirmPasswordInput, { target: { value: 'different123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('两次输入的密码不一致')).toBeInTheDocument();
    });
  });

  it('应该验证服务条款同意', async () => {
    render(<RegisterPage />);

    const usernameInput = screen.getByLabelText('用户名');
    const emailInput = screen.getByLabelText('邮箱地址');
    const passwordInput = screen.getByLabelText('密码');
    const confirmPasswordInput = screen.getByLabelText('确认密码');
    const submitButton = screen.getByRole('button', { name: /创建账户/i });

    fireEvent.change(usernameInput, { target: { value: 'testuser' } });
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.change(confirmPasswordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('请同意服务条款和隐私政策')).toBeInTheDocument();
    });
  });

  it('应该切换密码可见性', () => {
    render(<RegisterPage />);

    const passwordInput = screen.getByLabelText('密码') as HTMLInputElement;
    const confirmPasswordInput = screen.getByLabelText('确认密码') as HTMLInputElement;
    const toggleButtons = screen.getAllByRole('button', { name: '' }); // 密码切换按钮

    expect(passwordInput.type).toBe('password');
    expect(confirmPasswordInput.type).toBe('password');

    fireEvent.click(toggleButtons[0]);
    expect(passwordInput.type).toBe('text');

    fireEvent.click(toggleButtons[1]);
    expect(confirmPasswordInput.type).toBe('text');
  });

  it('应该处理成功注册', async () => {
    (register as any).mockResolvedValue({
      success: true,
      message: '注册成功！请查收验证邮件',
    });

    render(<RegisterPage />);

    const usernameInput = screen.getByLabelText('用户名');
    const emailInput = screen.getByLabelText('邮箱地址');
    const passwordInput = screen.getByLabelText('密码');
    const confirmPasswordInput = screen.getByLabelText('确认密码');
    const agreeCheckbox = screen.getByLabelText(/我已阅读并同意/);
    const submitButton = screen.getByRole('button', { name: /创建账户/i });

    fireEvent.change(usernameInput, { target: { value: 'testuser' } });
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.change(confirmPasswordInput, { target: { value: 'password123' } });
    fireEvent.click(agreeCheckbox);
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(register).toHaveBeenCalledWith({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        confirm_password: 'password123',
        agree_terms: true,
      });
    });

    await waitFor(() => {
      expect(screen.getByText('注册成功！请查收验证邮件')).toBeInTheDocument();
    });

    // 验证3秒后跳转
    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/auth/login?message=register_success');
    }, { timeout: 4000 });
  });

  it('应该处理注册失败', async () => {
    (register as any).mockResolvedValue({
      success: false,
      message: '用户名已被使用',
    });

    render(<RegisterPage />);

    const usernameInput = screen.getByLabelText('用户名');
    const emailInput = screen.getByLabelText('邮箱地址');
    const passwordInput = screen.getByLabelText('密码');
    const confirmPasswordInput = screen.getByLabelText('确认密码');
    const agreeCheckbox = screen.getByLabelText(/我已阅读并同意/);
    const submitButton = screen.getByRole('button', { name: /创建账户/i });

    fireEvent.change(usernameInput, { target: { value: 'existinguser' } });
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.change(confirmPasswordInput, { target: { value: 'password123' } });
    fireEvent.click(agreeCheckbox);
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('用户名已被使用')).toBeInTheDocument();
    });
  });

  it('应该处理网络错误', async () => {
    (register as any).mockRejectedValue(new Error('Network error'));

    render(<RegisterPage />);

    const usernameInput = screen.getByLabelText('用户名');
    const emailInput = screen.getByLabelText('邮箱地址');
    const passwordInput = screen.getByLabelText('密码');
    const confirmPasswordInput = screen.getByLabelText('确认密码');
    const agreeCheckbox = screen.getByLabelText(/我已阅读并同意/);
    const submitButton = screen.getByRole('button', { name: /创建账户/i });

    fireEvent.change(usernameInput, { target: { value: 'testuser' } });
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.change(confirmPasswordInput, { target: { value: 'password123' } });
    fireEvent.click(agreeCheckbox);
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('网络错误，请稍后重试')).toBeInTheDocument();
    });
  });

  it('应该在提交时禁用表单', async () => {
    (register as any).mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)));

    render(<RegisterPage />);

    const usernameInput = screen.getByLabelText('用户名');
    const emailInput = screen.getByLabelText('邮箱地址');
    const passwordInput = screen.getByLabelText('密码');
    const confirmPasswordInput = screen.getByLabelText('确认密码');
    const agreeCheckbox = screen.getByLabelText(/我已阅读并同意/);
    const submitButton = screen.getByRole('button', { name: /创建账户/i });

    fireEvent.change(usernameInput, { target: { value: 'testuser' } });
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.change(confirmPasswordInput, { target: { value: 'password123' } });
    fireEvent.click(agreeCheckbox);
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('注册中...')).toBeInTheDocument();
      expect(submitButton).toBeDisabled();
      expect(usernameInput).toBeDisabled();
      expect(emailInput).toBeDisabled();
      expect(passwordInput).toBeDisabled();
      expect(confirmPasswordInput).toBeDisabled();
    });
  });
});
