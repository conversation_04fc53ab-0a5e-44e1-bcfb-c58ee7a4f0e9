'use client';
import React, { createContext, useContext, useState, useCallback, useRef, useEffect } from 'react';
import './toast.css';
import { CheckCircleIcon, ExclamationCircleIcon } from '@heroicons/react/24/outline';

type ToastType = 'success' | 'error' | 'info';

interface Toast {
  id: number;
  message: string;
  type: ToastType;
}

interface ToastContextType {
  showToast: (message: string, type?: ToastType, duration?: number) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [toasts, setToasts] = useState<Toast[]>([]);
  const toastId = useRef(0);
  const [isDarkMode, setIsDarkMode] = useState(false);

  // 检测暗黑模式
  useEffect(() => {
    const checkDarkMode = () => {
      const isDark = document.documentElement.classList.contains('dark');
      setIsDarkMode(isDark);
    };
    
    // 初始检查
    checkDarkMode();
    
    // 创建一个MutationObserver来监听class变化
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'class') {
          checkDarkMode();
        }
      });
    });
    
    // 开始观察document.documentElement的class变化
    observer.observe(document.documentElement, { attributes: true });
    
    return () => {
      observer.disconnect();
    };
  }, []);

  const showToast = useCallback((message: string, type: ToastType = 'info', duration: number = 5000) => {
    const id = toastId.current++;
    setToasts((prev) => [...prev, { id, message, type }]);
    setTimeout(() => {
      setToasts((prev) => prev.filter((t) => t.id !== id));
    }, duration);
  }, []);

  // 根据类型获取样式
  const getToastStyles = (type: ToastType) => {
    switch (type) {
      case 'success':
        return isDarkMode 
          ? 'bg-green-800/95 text-white border-l-4 border-green-500' 
          : 'bg-green-100 text-green-800 border-l-4 border-green-500';
      case 'error':
        return isDarkMode 
          ? 'bg-red-800/95 text-white border-l-4 border-red-500' 
          : 'bg-red-100 text-red-800 border-l-4 border-red-500';
      default:
        return isDarkMode 
          ? 'bg-gray-800/95 text-white' 
          : 'bg-black/90 text-white';
    }
  };

  // 根据类型获取图标
  const getToastIcon = (type: ToastType) => {
    switch (type) {
      case 'success':
        return <CheckCircleIcon className="h-5 w-5 flex-shrink-0" />;
      case 'error':
        return <ExclamationCircleIcon className="h-5 w-5 flex-shrink-0" />;
      default:
        return null;
    }
  };

  return (
    <ToastContext.Provider value={{ showToast }}>
      {children}
      {/* Toast 列表 */}
      <div className="fixed top-6 right-6 z-[9999] flex flex-col gap-2 items-end">
        {toasts.map((toast) => (
          <div
            key={toast.id}
            className={`px-4 py-3 rounded shadow-lg toast-fade-long min-w-[200px] text-sm font-medium flex items-center gap-2 ${getToastStyles(toast.type)}`}
          >
            {getToastIcon(toast.type)}
            <span>{toast.message}</span>
          </div>
        ))}
      </div>
    </ToastContext.Provider>
  );
}; 