/**
 * SEO配置常量
 * 统一管理网站的SEO元数据
 */

import type { Metadata } from "next";

// 基础SEO配置
export const BASE_SEO = {
  siteName: "97盘搜",
  siteUrl: "https://pansoo.cn",
  defaultTitle: "97盘搜 - 网盘资源搜索平台",
  defaultDescription:
    "一个专业的网盘搜索引擎,提供百度网盘、夸克网盘、迅雷网盘、阿里云盘资源搜索,涵盖电影、游戏、短剧、软件、小说、学习资料等",
  defaultKeywords:
    "网盘搜索,盘搜搜,盘搜,考研资料,考公资料，短剧影视,游戏资源,电影资源,百度网盘,夸克网盘,迅雷网盘,阿里云盘,广播剧,有声小说",
  verification: {
    google: undefined,
    yahoo: undefined,
    yandex: undefined,
    other: {
      "msvalidate.01": "70ECAC758022A19ECB41FFEC240B1CCE",
    },
  },
};

// 页面特定SEO配置
export const PAGE_SEO = {
  // 首页
  home: {
    title:
      "97盘搜 - 专业免费网盘资源搜索引擎平台，支持百度网盘夸克网盘阿里云盘迅雷网盘",
    description:
      "97盘搜是专业的网盘资源搜索引擎，免费提供百度网盘、夸克网盘、阿里云盘、迅雷网盘等多平台资源搜索服务，涵盖电影、电视剧、动漫、游戏、软件、小说、学习资料等海量优质资源，一键搜索快速获取，支持精确搜索和模糊匹配，实时更新资源库，让您轻松找到所需资源，享受便捷的网盘搜索体验。",
    keywords:
      "网盘搜索,免费资源搜索,百度网盘,夸克网盘,阿里云盘,迅雷网盘,电影资源,学习资料,97盘搜",
  },

  // 搜索页面
  search: {
    title:
      "资源搜索 - 97盘搜专业免费网盘资源搜索引擎，支持百度夸克阿里迅雷网盘",
    description:
      "97盘搜专业网盘搜索引擎，提供免费的百度网盘、夸克网盘、阿里云盘、迅雷网盘资源搜索服务，涵盖电影、电视剧、动漫、游戏、软件、小说、学习资料等海量资源，支持精确搜索和实时更新，一键直达下载链接，快速便捷获取所需资源，智能推荐相关内容，提供最佳搜索体验。",
    keywords:
      "网盘搜索,免费资源搜索,百度网盘搜索,夸克网盘搜索,阿里云盘搜索,迅雷网盘搜索,电影资源,学习资料,97盘搜",
  },

  // 教程中心
  tutorials: {
    title:
      "教程中心 - 97盘搜网盘使用教程和资源获取指南，百度夸克阿里迅雷网盘详细教程",
    description:
      "97盘搜教程中心提供全面的网盘使用教程，包括百度网盘、夸克网盘、阿里云盘等主流网盘的使用方法，涵盖资源搜索技巧、下载方法、链接失效处理、账号注册等实用指南，帮助您轻松掌握网盘资源获取和管理技能，从新手到高手的完整学习路径，图文并茂详细解说，让您快速上手各种网盘操作。",
    keywords:
      "网盘教程,百度网盘使用教程,夸克网盘教程,阿里云盘教程,网盘搜索技巧,资源下载教程,97盘搜教程",
  },

  // 资源提交
  submit: {
    title:
      "资源提交 - 97盘搜免费分享网盘资源链接平台，支持百度夸克阿里迅雷网盘",
    description:
      "97盘搜资源提交平台，免费提交和分享网盘资源链接，支持百度网盘、夸克网盘、阿里云盘、迅雷网盘等主流网盘平台，帮助更多用户发现优质资源，共同构建丰富的资源分享社区，让知识和资源自由流动，打造最大的网盘资源库，简单操作即可贡献优质内容，与千万用户共享资源。",
    keywords:
      "资源提交,网盘链接提交,百度网盘分享,夸克网盘分享,阿里云盘分享,资源分享,97盘搜提交",
  },

  // 友情链接
  links: {
    title: "友情链接 - 97盘搜合作伙伴推荐，优质网盘服务和导航网站",
    description:
      "97盘搜友情链接页面，推荐优质的网盘服务平台和实用导航网站，包括百度网盘、夸克网盘、阿里云盘、迅雷网盘等主流云存储服务，以及爱搜导航、无瑕导航等精选网站导航平台，为用户提供更丰富的网络资源和便捷的上网体验，建立互利共赢的合作关系。",
    keywords:
      "友情链接,网盘服务,导航网站,百度网盘,夸克网盘,阿里云盘,迅雷网盘,爱搜导航,无瑕导航,97盘搜合作",
  },
};

// 动态SEO生成函数
export function generateSearchSEO(
  query: string,
  searchType: "local" | "online"
) {
  if (!query) {
    return PAGE_SEO.search;
  }

  const searchTypeText = searchType === "local" ? "本地缓存" : "实时联网";
  const title = `${query} - ${searchTypeText}搜索结果 | 97盘搜`;
  const description = `在97盘搜${searchTypeText}搜索"${query}"相关的网盘资源，支持百度网盘、夸克网盘、阿里云盘、迅雷网盘等多平台资源检索，快速找到您需要的资源。`;
  const keywords = `${query},${query}网盘资源,${query}下载,网盘搜索,百度网盘,夸克网盘,阿里云盘,迅雷网盘,97盘搜`;

  return { title, description, keywords };
}

// 教程详情页面SEO配置
export const TUTORIAL_SEO_CONFIG = {
  steamtools: {
    title: "SteamTools使用攻略 - 游戏清单导入和Steam管理工具完整教程 | 97盘搜",
    description:
      "详细的SteamTools使用教程，包括安装指南、游戏清单导入、Steam账户管理、离线登录等功能。学会使用SteamTools轻松管理Steam游戏库，获取免费游戏资源，支持离线模式和批量操作。",
    keywords:
      "SteamTools教程,Steam游戏清单,游戏管理工具,Steam离线登录,游戏资源,Steam工具,97盘搜教程",
  },
  "how-to-use-pan-so": {
    title: "如何使用盘搜网站 - 网盘资源搜索完整指南和使用技巧 | 97盘搜",
    description:
      "全面的盘搜网站使用教程，教您如何高效搜索网盘资源，包括搜索技巧、筛选方法、资源下载、链接获取等实用功能。掌握百度网盘、夸克网盘、阿里云盘等多平台资源搜索方法。",
    keywords:
      "盘搜使用教程,网盘搜索技巧,资源搜索方法,百度网盘搜索,夸克网盘搜索,97盘搜使用指南",
  },
  "resource-sharing-guide": {
    title: "资源分享指南 - 网盘资源分享规范和最佳实践教程 | 97盘搜",
    description:
      "学习如何正确分享网盘资源，包括资源整理、链接生成、分享规范、版权注意事项等。帮助您成为负责任的资源分享者，构建健康的资源分享社区环境。",
    keywords:
      "资源分享教程,网盘分享指南,资源整理方法,分享规范,版权保护,97盘搜分享",
  },
};

// 生成教程详情页面SEO配置
export function generateTutorialSEO(slug: string, tutorialTitle?: string) {
  // 如果有预定义的SEO配置，使用预定义的
  if (TUTORIAL_SEO_CONFIG[slug as keyof typeof TUTORIAL_SEO_CONFIG]) {
    return TUTORIAL_SEO_CONFIG[slug as keyof typeof TUTORIAL_SEO_CONFIG];
  }

  // 否则基于标题生成动态SEO
  const title = tutorialTitle
    ? `${tutorialTitle} - 详细教程指南 | 97盘搜`
    : `教程详情 - 97盘搜教程中心`;

  const description = tutorialTitle
    ? `${tutorialTitle}的详细教程指南，97盘搜教程中心为您提供专业的网盘使用教程和资源获取方法，帮助您更好地使用各种网盘服务和工具。`
    : "97盘搜教程中心提供专业的网盘使用教程和资源获取方法，帮助您更好地使用各种网盘服务和工具。";

  const keywords = tutorialTitle
    ? `${tutorialTitle},网盘教程,使用指南,97盘搜教程,资源获取,网盘使用技巧`
    : "网盘教程,使用指南,97盘搜教程,资源获取,网盘使用技巧";

  return { title, description, keywords };
}

// 生成完整的Metadata对象
export function createMetadata(pageConfig: {
  title: string;
  description: string;
  keywords: string;
  path?: string;
  image?: string;
}): Metadata {
  const { title, description, keywords, path = "" } = pageConfig;
  const url = `${BASE_SEO.siteUrl}${path}`;

  return {
    title,
    description,
    keywords,
    robots: "index, follow",
    openGraph: {
      title,
      description,
      url,
      siteName: BASE_SEO.siteName,
      type: "website",
    },
    twitter: {
      card: "summary",
      title,
      description,
    },
    verification: BASE_SEO.verification,
    metadataBase: new URL(BASE_SEO.siteUrl),
  };
}

// 常用的结构化数据
export const STRUCTURED_DATA = {
  website: {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: BASE_SEO.siteName,
    url: BASE_SEO.siteUrl,
    description: BASE_SEO.defaultDescription,
    potentialAction: {
      "@type": "SearchAction",
      target: `${BASE_SEO.siteUrl}/search?q={search_term_string}`,
      "query-input": "required name=search_term_string",
    },
  },

  organization: {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: BASE_SEO.siteName,
    url: BASE_SEO.siteUrl,
    description: BASE_SEO.defaultDescription,
    logo: `${BASE_SEO.siteUrl}/images/logo.png`,
  },
};

// SEO最佳实践常量
export const SEO_LIMITS = {
  title: {
    min: 30,
    max: 60,
    optimal: 55,
  },
  description: {
    min: 120,
    max: 160,
    optimal: 155,
  },
  keywords: {
    max: 10,
    optimal: 5,
  },
};

// 验证SEO配置的工具函数
export function validateSEO(config: {
  title: string;
  description: string;
  keywords: string;
}) {
  const warnings: string[] = [];

  if (config.title.length < SEO_LIMITS.title.min) {
    warnings.push(`标题过短，建议至少${SEO_LIMITS.title.min}个字符`);
  }
  if (config.title.length > SEO_LIMITS.title.max) {
    warnings.push(`标题过长，建议不超过${SEO_LIMITS.title.max}个字符`);
  }

  if (config.description.length < SEO_LIMITS.description.min) {
    warnings.push(`描述过短，建议至少${SEO_LIMITS.description.min}个字符`);
  }
  if (config.description.length > SEO_LIMITS.description.max) {
    warnings.push(`描述过长，建议不超过${SEO_LIMITS.description.max}个字符`);
  }

  const keywordCount = config.keywords.split(",").length;
  if (keywordCount > SEO_LIMITS.keywords.max) {
    warnings.push(`关键词过多，建议不超过${SEO_LIMITS.keywords.max}个`);
  }

  return warnings;
}
