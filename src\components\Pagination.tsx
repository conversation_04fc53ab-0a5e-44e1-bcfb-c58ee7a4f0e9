"use client";

import { useLoadingStore } from "@/store/loadingStore";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export default function Pagination({
  currentPage,
  totalPages,
  onPageChange,
}: PaginationProps) {
  const { isLoading } = useLoadingStore();

  return (
    <div className="flex justify-center space-x-2 my-4">
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1 || isLoading}
        className="px-3 py-1.5 rounded-md border border-gray-300 dark:border-[#333646] bg-white dark:bg-[#242428] text-sm font-medium text-gray-700 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed dark-text-white"
      >
        上一页
      </button>
      <span className="px-4 py-2 dark:text-white dark-text-white">
        {currentPage} / {totalPages}
      </span>
      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages || isLoading}
        className="px-3 py-1.5 rounded-md border border-gray-300 dark:border-[#333646] bg-white dark:bg-[#242428] text-sm font-medium text-gray-700 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed dark-text-white"
      >
        下一页
      </button>
    </div>
  );
}
