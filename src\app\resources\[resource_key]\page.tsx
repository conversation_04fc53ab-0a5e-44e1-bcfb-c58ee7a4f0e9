import { getResourceByKey } from "@/services/resourceService";
import { Metadata } from "next";
import ResourceDetailClient from "@/components/ResourceDetailClient";
import { notFound } from "next/navigation";

type PageProps = {
  params: any;
  searchParams: any;
};

// 创建一个共享的资源获取函数，利用React cache避免重复调用
const getSharedResourceData = async (resource_key: string) => {
  const resource = await getResourceByKey(resource_key);
  if (!resource) {
    return null;
  }

  return {
    resource,
  };
};

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const awaitedParams = await params;
  const resource_key = awaitedParams.resource_key;

  // 只获取资源基本信息用于metadata，避免获取额外数据
  const resourceData = await getResourceByKey(resource_key);

  if (!resourceData) {
    return {
      title: "资源未找到",
    };
  }

  return {
    title: resourceData.seo_title || resourceData.title,
    description: resourceData.seo_description || "",
    robots: {
      index: true,
      follow: true,
    },
  };
}

export default async function ResourceDetailPage({ params }: PageProps) {
  const awaitedParams = await params;
  const resource_key = awaitedParams.resource_key;

  // 只获取基础资源数据
  const resourceData = await getSharedResourceData(resource_key);

  if (!resourceData) {
    notFound();
  }

  const { resource } = resourceData;

  return <ResourceDetailClient resource={resource} />;
}
