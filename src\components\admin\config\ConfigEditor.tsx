"use client";

import { useState, useCallback, useEffect } from "react";
import {
  XMarkIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  EyeSlashIcon,
} from "@heroicons/react/24/outline";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import {
  ConfigTreeNode,
  ConfigPathRequest,
  ValidationResult,
} from "@/types/config";
import {
  formatConfigValue,
  parseConfigValue,
  getEffectTypeText,
  getEffectTypeColor,
  validateConfigValue,
  updateConfigByPath,
} from "@/services/configService";
import { useToast } from "@/components/ToastProvider";

interface ConfigEditorProps {
  node: ConfigTreeNode | null;
  onClose: () => void;
  onSave?: (path: string, value: any) => void;
  className?: string;
}

export default function ConfigEditor({
  node,
  onClose,
  onSave,
  className = "",
}: ConfigEditorProps) {
  const { showToast } = useToast();
  const [value, setValue] = useState("");
  const [comment, setComment] = useState("");
  const [loading, setLoading] = useState(false);
  const [validation, setValidation] = useState<ValidationResult | null>(null);
  const [showValue, setShowValue] = useState(false);

  // 初始化表单数据
  useEffect(() => {
    if (node) {
      setValue(formatConfigValue(node.value, node.type));
      setComment(node.comment || "");
      setValidation(null);
      setShowValue(!node.sensitive);
    }
  }, [node]);

  // 验证配置值
  const handleValidation = useCallback(
    async (newValue: string) => {
      if (!node) return;

      try {
        const parsedValue = parseConfigValue(newValue, node.type);
        const result = await validateConfigValue(node.path, parsedValue);
        setValidation(result);
      } catch {
        setValidation({
          valid: false,
          message: "验证失败",
        });
      }
    },
    [node]
  );

  // 值变化处理
  const handleValueChange = useCallback(
    (newValue: string) => {
      setValue(newValue);

      // 延迟验证
      const timeoutId = setTimeout(() => {
        handleValidation(newValue);
      }, 500);

      return () => clearTimeout(timeoutId);
    },
    [handleValidation]
  );

  // 保存配置
  const handleSave = useCallback(async () => {
    if (!node) return;

    setLoading(true);
    try {
      const parsedValue = parseConfigValue(value, node.type);

      // 最终验证
      const validationResult = await validateConfigValue(
        node.path,
        parsedValue
      );
      if (!validationResult.valid) {
        showToast(validationResult.message || "配置值验证失败", "error");
        return;
      }

      const request: ConfigPathRequest = {
        value: parsedValue,
        comment: comment.trim() || undefined,
      };

      const response = await updateConfigByPath(node.path, request);

      if (response.status === "success") {
        showToast("配置更新成功", "success");
        onSave?.(node.path, parsedValue);
        onClose();
      } else {
        showToast("配置更新失败", "error");
      }
    } catch (error) {
      console.error("保存配置失败:", error);
      showToast("保存配置失败", "error");
    } finally {
      setLoading(false);
    }
  }, [node, value, comment, showToast, onSave, onClose]);

  // 渲染不同类型的输入控件
  const renderValueInput = () => {
    if (!node) return null;

    const inputProps = {
      value: value,
      onChange: (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      ) => handleValueChange(e.target.value),
      disabled: loading,
      className: `${validation && !validation.valid ? "border-red-500" : ""}`,
    };

    switch (node.type) {
      case "boolean":
        return (
          <div className="space-y-2">
            <label
              htmlFor="config-boolean-select"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              配置值
            </label>
            <select
              id="config-boolean-select"
              value={value}
              onChange={(e) => handleValueChange(e.target.value)}
              disabled={loading}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
              aria-label="选择布尔值配置"
            >
              <option value="true">是 (true)</option>
              <option value="false">否 (false)</option>
            </select>
          </div>
        );

      case "number":
        return (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              配置值
            </label>
            <Input type="number" placeholder="请输入数字" {...inputProps} />
          </div>
        );

      case "object":
      case "array":
        return (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              配置值 (JSON格式)
            </label>
            <textarea
              rows={6}
              placeholder="请输入有效的JSON格式"
              className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 font-mono text-sm ${
                validation && !validation.valid ? "border-red-500" : ""
              }`}
              value={value}
              onChange={(e) => handleValueChange(e.target.value)}
              disabled={loading}
            />
          </div>
        );

      default:
        return (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              配置值
            </label>
            <div className="relative">
              <Input
                type={node.sensitive && !showValue ? "password" : "text"}
                placeholder="请输入配置值"
                {...inputProps}
              />
              {node.sensitive && (
                <button
                  type="button"
                  onClick={() => setShowValue(!showValue)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  {showValue ? (
                    <EyeSlashIcon className="w-4 h-4" />
                  ) : (
                    <EyeIcon className="w-4 h-4" />
                  )}
                </button>
              )}
            </div>
          </div>
        );
    }
  };

  if (!node) return null;

  return (
    <div className={`config-editor ${className}`}>
      {/* 编辑器头部 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3 min-w-0 flex-1">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate">
            编辑配置
          </h3>
          {node.sensitive && (
            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 flex-shrink-0">
              敏感配置
            </span>
          )}
        </div>

        <button
          type="button"
          onClick={onClose}
          className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors flex-shrink-0 self-start sm:self-center"
          title="关闭编辑器"
          aria-label="关闭配置编辑器"
        >
          <XMarkIcon className="w-5 h-5" />
        </button>
      </div>

      {/* 编辑器内容 */}
      <div className="p-4 space-y-4">
        {/* 配置信息 */}
        <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3 space-y-3">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300 flex-shrink-0">
              配置名称:
            </span>
            <span className="text-sm text-gray-900 dark:text-gray-100 break-words">
              {node.display_name}
            </span>
          </div>

          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300 flex-shrink-0 sm:pt-0.5">
              配置路径:
            </span>
            <span className="text-sm text-gray-900 dark:text-gray-100 font-mono break-all leading-relaxed">
              {node.path}
            </span>
          </div>

          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300 flex-shrink-0">
              数据类型:
            </span>
            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
              {node.type}
            </span>
          </div>

          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300 flex-shrink-0">
              生效方式:
            </span>
            <span className={`text-sm ${getEffectTypeColor(node.effect_type)}`}>
              {getEffectTypeText(node.effect_type)}
            </span>
          </div>

          {node.comment && (
            <div className="pt-3 border-t border-gray-200 dark:border-gray-600 space-y-2">
              <span className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                说明:
              </span>
              <div className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed break-words whitespace-pre-wrap max-h-32 overflow-y-auto">
                {node.comment}
              </div>
            </div>
          )}
        </div>

        {/* 配置值输入 */}
        {renderValueInput()}

        {/* 验证结果 */}
        {validation && (
          <div
            className={`flex items-start space-x-2 p-3 rounded-lg ${
              validation.valid
                ? "bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-400"
                : "bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-400"
            }`}
          >
            {validation.valid ? (
              <CheckIcon className="w-5 h-5 flex-shrink-0 mt-0.5" />
            ) : (
              <ExclamationTriangleIcon className="w-5 h-5 flex-shrink-0 mt-0.5" />
            )}
            <div className="flex-1">
              <p className="text-sm font-medium">
                {validation.valid ? "验证通过" : "验证失败"}
              </p>
              {validation.message && (
                <p className="text-sm mt-1">{validation.message}</p>
              )}
              {validation.errors && validation.errors.length > 0 && (
                <ul className="text-sm mt-1 space-y-1">
                  {validation.errors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              )}
            </div>
          </div>
        )}

        {/* 注释输入 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            备注 (可选)
          </label>
          <textarea
            rows={2}
            placeholder="添加配置变更的备注信息..."
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            disabled={loading}
          />
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
          <Button variant="outline" onClick={onClose} disabled={loading}>
            取消
          </Button>

          <Button
            onClick={handleSave}
            disabled={loading || (validation ? !validation.valid : false)}
            className="min-w-[80px]"
          >
            {loading ? "保存中..." : "保存"}
          </Button>
        </div>
      </div>
    </div>
  );
}
