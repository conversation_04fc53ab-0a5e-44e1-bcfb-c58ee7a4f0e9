# 循环依赖问题修复报告

## 问题描述

在浏览器中出现以下错误：
```
ReferenceError: Cannot access 'API_BASE_URL' before initialization
```

这个错误是由于模块循环依赖导致的：
- `authService.ts` 导入了 `tokenManager.ts`
- `tokenManager.ts` 导入了 `authService.ts` 中的 `refreshToken` 函数
- 在模块加载时，`API_BASE_URL` 还没有被初始化就被访问了

## 根本原因

1. **循环依赖**：`authService` ↔ `tokenManager` 形成循环依赖
2. **立即执行**：`tokenManager` 在模块加载时立即执行，导致过早访问未初始化的变量
3. **环境变量访问**：`process.env.NEXT_PUBLIC_API_BASE_URL` 在模块顶层被访问

## 修复方案

### 1. 延迟获取API_BASE_URL

**修改前：**
```typescript
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
```

**修改后：**
```typescript
const getApiBaseUrl = () => {
  return process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
};
```

### 2. 使用动态导入避免循环依赖

**修改前：**
```typescript
import { refreshToken } from "../services/authService";
```

**修改后：**
```typescript
// 在需要时动态导入
const { refreshToken } = await import("../services/authService");
```

### 3. 延迟执行tokenManager操作

**修改前：**
```typescript
import { tokenManager } from "../utils/tokenManager";
tokenManager.reset();
```

**修改后：**
```typescript
// 延迟导入以避免循环依赖
import("../utils/tokenManager").then(({ tokenManager }) => {
  tokenManager.reset();
});
```

## 修改的文件

### 1. `src/services/authService.ts`
- ✅ 将 `API_BASE_URL` 改为 `getApiBaseUrl()` 函数
- ✅ 更新所有API调用使用 `getApiBaseUrl()`
- ✅ 使用动态导入调用 `tokenManager`

### 2. `src/services/profileService.ts`
- ✅ 将 `API_BASE_URL` 改为 `getApiBaseUrl()` 函数
- ✅ 更新所有API调用使用 `getApiBaseUrl()`
- ✅ 移除对 `tokenManager` 的直接导入

### 3. `src/utils/tokenManager.ts`
- ✅ 移除对 `authService` 的直接导入
- ✅ 在 `performRefresh` 方法中使用动态导入

## 修复验证

### 测试步骤
1. 清除浏览器缓存
2. 重新启动开发服务器
3. 访问需要认证的页面
4. 检查控制台是否还有循环依赖错误

### 预期结果
- ✅ 不再出现 `Cannot access 'API_BASE_URL' before initialization` 错误
- ✅ 页面正常加载
- ✅ 认证功能正常工作
- ✅ Token自动刷新功能正常

## 技术细节

### 动态导入的优势
1. **避免循环依赖**：模块在需要时才加载
2. **延迟执行**：避免在模块初始化时执行代码
3. **更好的错误处理**：可以捕获导入错误

### 函数式API_BASE_URL的优势
1. **延迟求值**：在调用时才获取环境变量
2. **默认值支持**：提供fallback值
3. **更好的测试性**：可以在测试中mock

## 最佳实践

### 1. 避免循环依赖
- 使用依赖注入
- 创建共享的工具模块
- 使用事件系统解耦

### 2. 环境变量处理
- 使用函数包装环境变量访问
- 提供合理的默认值
- 在运行时而非模块加载时访问

### 3. 模块设计
- 保持单一职责
- 避免在模块顶层执行副作用
- 使用工厂模式创建实例

## 后续优化建议

### 1. 重构建议
- 考虑将token管理逻辑移到React Context中
- 创建专门的API客户端类
- 使用依赖注入容器

### 2. 监控建议
- 添加模块加载性能监控
- 设置循环依赖检测工具
- 定期审查模块依赖关系

## 总结

通过使用动态导入和延迟求值，成功解决了循环依赖问题。这个修复：

1. **保持了功能完整性**：所有原有功能继续正常工作
2. **提高了代码质量**：消除了循环依赖的代码异味
3. **增强了稳定性**：避免了模块初始化时的竞态条件
4. **改善了开发体验**：消除了令人困惑的错误信息

修复后的代码更加健壮，为后续开发奠定了良好的基础。
