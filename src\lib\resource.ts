// 对内获取数据时，使用您指定的后端服务地址
// 这是用于服务器到服务器的通信
const BACKEND_API_URL = process.env.API_PROXY_TARGET;

// 将这个函数导出，以便其他服务器端代码（如 sitemap.ts）可以直接调用它
export async function getTotalResourceCountFromBackend(): Promise<number> {
  if (!BACKEND_API_URL) {
    console.error("错误：环境变量 API_PROXY_TARGET 未设置。无法获取资源总数。");
    return 0;
  }

  try {
    const response = await fetch(`${BACKEND_API_URL}/api/resources/count`, {
      // Next.js 的服务端缓存策略:
      // revalidate: 36000 表示将结果缓存10小时
      // 这可以有效减轻您后端API的压力
      next: { revalidate: 36000 },
    });

    if (!response.ok) {
      console.error(`获取资源总数失败，后端接口状态: ${response.status}`);
      return 0;
    }

    const data = await response.json();

    // 从后端返回的 { "count": ... } 中提取数值
    return data.count || 0;
  } catch (error) {
    console.error("调用后端获取资源总数接口时发生网络错误:", error);
    // 发生错误时返回0，防止站点地图生成过程完全中断
    return 0;
  }
}

// 站点地图相关接口类型
interface SitemapResource {
  detail_url: string;
  created_at: string;
  title?: string;
  priority?: number;
}

// 获取站点地图资源数据
export async function getResourcesForSitemap(
  pageNumber: number,
  limit: number = 5000
): Promise<SitemapResource[]> {
  if (!BACKEND_API_URL) {
    console.error(
      "错误：环境变量 API_PROXY_TARGET 未设置。无法获取站点地图资源。"
    );
    return [];
  }

  try {
    // 尝试使用站点地图专用API，如果不存在则使用通用搜索API
    const apiUrl = `${BACKEND_API_URL}/api/resources/sitemap?page=${pageNumber}&limit=${limit}`;
    console.log(`正在请求站点地图API: ${apiUrl}`);

    const response = await fetch(apiUrl, {
      next: { revalidate: 3600 }, // 缓存1小时
    });

    console.log(`站点地图API响应状态: ${response.status}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(
        `获取站点地图资源失败，后端接口状态: ${response.status}, 错误信息: ${errorText}`
      );

      // 如果站点地图API不存在，尝试使用备用方案
      if (response.status === 404) {
        console.log("站点地图API不存在，尝试使用备用方案");
        return await getResourcesForSitemapFallback(pageNumber, limit);
      }

      return [];
    }

    const data = await response.json();
    console.log(`站点地图API返回数据:`, data);

    // 后端返回的数据结构: { total, page, limit, pages, resources: [...] }
    if (data.resources && Array.isArray(data.resources)) {
      return data.resources;
    }

    // 备用：如果直接是数组
    return Array.isArray(data) ? data : [];
  } catch (error) {
    console.error("调用后端获取站点地图资源接口时发生网络错误:", error);
    return [];
  }
}

// 备用方案：使用搜索API获取资源数据
async function getResourcesForSitemapFallback(
  pageNumber: number,
  limit: number
): Promise<SitemapResource[]> {
  try {
    // 使用空搜索来获取所有资源
    const apiUrl = `${BACKEND_API_URL}/api/search?keyword=&page=${pageNumber}&limit=${limit}`;
    console.log(`使用备用API: ${apiUrl}`);

    const response = await fetch(apiUrl, {
      next: { revalidate: 3600 },
    });

    if (!response.ok) {
      console.error(`备用API调用失败: ${response.status}`);
      return [];
    }

    const data = await response.json();
    console.log("备用API返回数据:", data);

    // 转换搜索结果为站点地图格式
    if (data.resources && Array.isArray(data.resources)) {
      return data.resources.map((resource: any) => ({
        detail_url: `/resource/${resource.resource_key || resource.id}`,
        created_at:
          resource.created_at ||
          resource.updated_at ||
          new Date().toISOString(),
        title: resource.title,
        priority: 0.6,
      }));
    }

    return [];
  } catch (error) {
    console.error("备用API调用失败:", error);
    return [];
  }
}

// 生成站点地图XML
export function generateSitemapXml(resources: SitemapResource[]): string {
  const PUBLIC_URL =
    process.env.NEXT_PUBLIC_API_BASE_URL || "https://pansoo.cn";

  const urls = resources
    .map((resource) => {
      const lastmod = resource.created_at
        ? new Date(resource.created_at).toISOString()
        : new Date().toISOString();
      const priority = resource.priority || 0.6;

      // 从 detail_url 中提取 resource_key，格式为 /resource/xxx
      const resourceKey = resource.detail_url.replace("/resource/", "");

      return `
    <url>
        <loc>${PUBLIC_URL}/resources/${resourceKey}</loc>
        <lastmod>${lastmod}</lastmod>
        <changefreq>weekly</changefreq>
        <priority>${priority}</priority>
    </url>`;
    })
    .join("");

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    ${urls}
</urlset>`;
}
