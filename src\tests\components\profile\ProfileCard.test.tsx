import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { ProfileCard } from "@/components/profile/ProfileCard";
import * as profileService from "@/services/profileService";

// Mock the profile service
vi.mock("@/services/profileService", () => ({
  getMyProfile: vi.fn(),
}));

// Mock lucide-react icons
vi.mock("lucide-react", () => ({
  User: () => <div data-testid="user-icon" />,
  Mail: () => <div data-testid="mail-icon" />,
  Calendar: () => <div data-testid="calendar-icon" />,
  Award: () => <div data-testid="award-icon" />,
  Settings: () => <div data-testid="settings-icon" />,
  CheckCircle: () => <div data-testid="check-circle-icon" />,
  XCircle: () => <div data-testid="x-circle-icon" />,
}));

const mockProfile = {
  id: 1,
  username: "testuser",
  nickname: "测试用户",
  email: "<EMAIL>",
  avatar: null,
  status: "active",
  email_verified: true,
  role: {
    name: "user",
    display_name: "普通用户",
  },
  title: "新手",
  points: 100,
  created_at: "2024-01-01T00:00:00Z",
  last_login_at: "2024-01-02T00:00:00Z",
};

describe("ProfileCard 组件", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("应该正确渲染加载状态", () => {
    // Mock a pending promise
    const mockGetMyProfile = vi.mocked(profileService.getMyProfile);
    mockGetMyProfile.mockReturnValue(new Promise(() => {})); // Never resolves

    render(<ProfileCard />);

    expect(screen.getByText("加载中...")).toBeInTheDocument();
  });

  it("应该正确渲染用户信息", async () => {
    const mockGetMyProfile = vi.mocked(profileService.getMyProfile);
    mockGetMyProfile.mockResolvedValue({
      success: true,
      data: mockProfile,
      message: "",
    });

    render(<ProfileCard />);

    await waitFor(() => {
      expect(screen.getByText("测试用户")).toBeInTheDocument();
    });

    expect(screen.getByText("@testuser")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
    expect(screen.getByText("普通用户")).toBeInTheDocument();
    expect(screen.getByText("新手")).toBeInTheDocument();
    expect(screen.getByText("100 积分")).toBeInTheDocument();
    expect(screen.getByText("正常")).toBeInTheDocument();
    expect(screen.getByText("已验证")).toBeInTheDocument();
  });

  it("应该正确处理错误状态", async () => {
    const mockGetMyProfile = vi.mocked(profileService.getMyProfile);
    mockGetMyProfile.mockResolvedValue({
      success: false,
      data: null,
      message: "获取用户信息失败",
    });

    render(<ProfileCard />);

    await waitFor(() => {
      expect(screen.getByText("获取用户信息失败")).toBeInTheDocument();
    });

    expect(screen.getByText("重新加载")).toBeInTheDocument();
  });

  it("应该显示编辑按钮", async () => {
    const mockGetMyProfile = vi.mocked(profileService.getMyProfile);
    mockGetMyProfile.mockResolvedValue({
      success: true,
      data: mockProfile,
      message: "",
    });

    const mockOnEditClick = vi.fn();

    render(<ProfileCard onEditClick={mockOnEditClick} showEditButton={true} />);

    await waitFor(() => {
      expect(screen.getByText("编辑资料")).toBeInTheDocument();
    });
  });

  it("应该隐藏编辑按钮", async () => {
    const mockGetMyProfile = vi.mocked(profileService.getMyProfile);
    mockGetMyProfile.mockResolvedValue({
      success: true,
      data: mockProfile,
      message: "",
    });

    render(<ProfileCard showEditButton={false} />);

    await waitFor(() => {
      expect(screen.getByText("测试用户")).toBeInTheDocument();
    });

    expect(screen.queryByText("编辑资料")).not.toBeInTheDocument();
  });

  it("应该正确处理缺少字段的用户数据", async () => {
    const incompleteProfile = {
      id: 1,
      username: undefined,
      nickname: null,
      email: undefined,
      avatar: null,
      status: undefined,
      email_verified: false,
      role: undefined,
      title: undefined,
      points: undefined,
      created_at: undefined,
      last_login_at: null,
    };

    const mockGetMyProfile = vi.mocked(profileService.getMyProfile);
    mockGetMyProfile.mockResolvedValue({
      success: true,
      data: incompleteProfile,
      message: "",
    });

    render(<ProfileCard />);

    await waitFor(() => {
      expect(screen.getByText("未知用户")).toBeInTheDocument();
    });

    expect(screen.getByText("@unknown")).toBeInTheDocument();
    expect(screen.getByText("未设置邮箱")).toBeInTheDocument();
    expect(screen.getByText("未知角色")).toBeInTheDocument();
    expect(screen.getByText("无等级")).toBeInTheDocument();
    expect(screen.getByText("0 积分")).toBeInTheDocument();
    expect(screen.getByText("注册于 未知")).toBeInTheDocument();
  });

  it("应该正确处理空字符串字段", async () => {
    const emptyProfile = {
      id: 1,
      username: "",
      nickname: "",
      email: "",
      avatar: "",
      status: "",
      email_verified: false,
      role: {
        id: "1",
        name: "",
        display_name: "",
      },
      title: "",
      points: 0,
      created_at: "2023-01-01T00:00:00Z",
      last_login_at: null,
    };

    const mockGetMyProfile = vi.mocked(profileService.getMyProfile);
    mockGetMyProfile.mockResolvedValue({
      success: true,
      data: emptyProfile,
      message: "",
    });

    render(<ProfileCard />);

    await waitFor(() => {
      expect(screen.getByText("未知用户")).toBeInTheDocument();
    });

    expect(screen.getByText("@unknown")).toBeInTheDocument();
    expect(screen.getByText("未设置邮箱")).toBeInTheDocument();
    expect(screen.getByText("未知角色")).toBeInTheDocument();
    expect(screen.getByText("无等级")).toBeInTheDocument();
  });

  it("应该正确处理头像回退显示", async () => {
    const profileWithoutNickname = {
      ...mockProfile,
      nickname: null,
      username: "testuser",
    };

    const mockGetMyProfile = vi.mocked(profileService.getMyProfile);
    mockGetMyProfile.mockResolvedValue({
      success: true,
      data: profileWithoutNickname,
      message: "",
    });

    render(<ProfileCard />);

    await waitFor(() => {
      // 应该显示用户名的首字母大写
      expect(screen.getByText("T")).toBeInTheDocument();
    });
  });

  it("应该正确处理完全无效的用户数据", async () => {
    const invalidProfile = {
      id: 1,
      username: null,
      nickname: null,
      email: null,
      avatar: null,
      status: null,
      email_verified: false,
      role: null,
      title: null,
      points: null,
      created_at: null,
      last_login_at: null,
    };

    const mockGetMyProfile = vi.mocked(profileService.getMyProfile);
    mockGetMyProfile.mockResolvedValue({
      success: true,
      data: invalidProfile,
      message: "",
    });

    render(<ProfileCard />);

    await waitFor(() => {
      // 应该显示默认的回退值
      expect(screen.getByText("?")).toBeInTheDocument(); // 头像回退
      expect(screen.getByText("未知用户")).toBeInTheDocument();
      expect(screen.getByText("@unknown")).toBeInTheDocument();
      expect(screen.getByText("未设置邮箱")).toBeInTheDocument();
      expect(screen.getByText("未知角色")).toBeInTheDocument();
      expect(screen.getByText("无等级")).toBeInTheDocument();
      expect(screen.getByText("0 积分")).toBeInTheDocument();
      expect(screen.getByText("注册于 未知")).toBeInTheDocument();
    });
  });
});
