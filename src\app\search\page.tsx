"use client";

import { useState, useEffect, useMemo, Suspense, useCallback } from "react";
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import Fuse from "fuse.js";
import { QuestionMarkCircleIcon } from "@heroicons/react/24/outline";
import { generateSearchSEO } from "@/config/seo";
import SearchBar from "@/components/SearchBar";
import ResourceCard from "@/components/ResourceCard";
import { useToast } from "@/components/ToastProvider";
import SearchTips from "@/components/SearchTips";
import TimeFilter from "@/components/TimeFilter";
import {
  searchPanResources,
  Resource,
  ApiResource,
  SearchResult,
} from "@/services/resourceService";

// 网盘类型映射常量
const PAN_TYPE_MAP = {
  // 字符串到数字的映射（用于API调用）
  STRING_TO_NUMBER: {
    all: 0,
    baidu: 1,
    quark: 2,
    aliyun: 3,
    thunder: 4,
  } as const,
  // 数字到字符串的映射（用于显示）
  NUMBER_TO_STRING: {
    1: "百度网盘",
    2: "夸克网盘",
    3: "阿里云盘",
    4: "迅雷网盘",
  } as const,
} as const;

// 工具函数：获取网盘类型对应的数字
const getPanTypeNumber = (type: string): number => {
  return (
    PAN_TYPE_MAP.STRING_TO_NUMBER[
      type as keyof typeof PAN_TYPE_MAP.STRING_TO_NUMBER
    ] || 0
  );
};

// 工具函数：获取网盘类型对应的显示文本
const getPanTypeText = (panType: number): string => {
  return (
    PAN_TYPE_MAP.NUMBER_TO_STRING[
      panType as keyof typeof PAN_TYPE_MAP.NUMBER_TO_STRING
    ] || "未知网盘"
  );
};

// 搜索过滤器组件
function FilterBar({
  panType,
  onPanTypeChange,
  fileType,
  onFileTypeChange,
  exactMatch,
  onExactMatchChange,
  timeFilter,
  onTimeFilterChange,
}: {
  panType: string;
  onPanTypeChange: (type: string) => void;
  fileType: string;
  onFileTypeChange: (type: string) => void;
  exactMatch: boolean;
  onExactMatchChange: (exact: boolean) => void;
  timeFilter: string;
  onTimeFilterChange: (filter: string) => void;
}) {
  return (
    <div className="filter-bar flex flex-wrap gap-3 mb-6 pb-4 border-b w-full">
      {/* 云盘类型下拉列表 */}
      <div className="flex items-center flex-nowrap mr-1">
        <label
          htmlFor="pan-type-select"
          className="mr-1 text-sm font-medium whitespace-nowrap text-gray-700 dark:text-white"
        >
          云盘类型:
        </label>
        <select
          id="pan-type-select"
          aria-label="选择云盘类型"
          className="bg-white dark:bg-[#2c2c34] border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-white rounded-md px-1 py-1.5 text-sm min-w-[100px]"
          value={panType}
          onChange={(e) => onPanTypeChange(e.target.value)}
        >
          <option value="all" className="text-gray-700 dark:text-white">
            全部网盘
          </option>
          <option value="baidu" className="text-gray-700 dark:text-white">
            百度网盘
          </option>
          <option value="quark" className="text-gray-700 dark:text-white">
            夸克网盘
          </option>
          <option value="aliyun" className="text-gray-700 dark:text-white">
            阿里云盘
          </option>
          <option value="thunder" className="text-gray-700 dark:text-white">
            迅雷网盘
          </option>
        </select>
      </div>

      {/* 文件类型下拉列表 */}
      <div className="flex items-center flex-nowrap mr-1">
        <label
          htmlFor="file-type-select"
          className="mr-1 text-sm font-medium whitespace-nowrap text-gray-700 dark:text-white"
        >
          文件类型:
        </label>
        <select
          id="file-type-select"
          aria-label="选择文件类型"
          className="bg-white dark:bg-[#2c2c34] border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-white rounded-md px-1 py-1.5 text-sm min-w-[100px]"
          value={fileType}
          onChange={(e) => onFileTypeChange(e.target.value)}
        >
          <option value="all" className="text-gray-700 dark:text-white">
            全部类型
          </option>
          <option value="video" className="text-gray-700 dark:text-white">
            视频
          </option>
          <option value="audio" className="text-gray-700 dark:text-white">
            音频
          </option>
          <option value="image" className="text-gray-700 dark:text-white">
            图片
          </option>
          <option value="document" className="text-gray-700 dark:text-white">
            文档
          </option>
          <option value="archive" className="text-gray-700 dark:text-white">
            压缩包
          </option>
          <option value="application" className="text-gray-700 dark:text-white">
            应用
          </option>
        </select>
      </div>

      {/* 时间过滤器 */}
      <TimeFilter value={timeFilter} onChange={onTimeFilterChange} />

      {/* 精确搜索复选框 */}
      <div className="flex items-center flex-nowrap">
        <label className="inline-flex items-center cursor-pointer">
          <input
            id="exact-match-checkbox"
            type="checkbox"
            className="sr-only peer"
            checked={exactMatch}
            onChange={(e) => onExactMatchChange(e.target.checked)}
          />
          <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          <span className="ms-3 text-sm font-medium text-gray-700 dark:text-white">
            精确搜索
          </span>
        </label>
      </div>
      <SearchTips />
    </div>
  );
}

// 分页控件组件
function Pagination({
  currentPage,
  totalPages,
  onPageChange,
}: {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}) {
  const pages = [];

  // 添加页码按钮
  const maxDisplayedPages = 5; // 最多显示的页码数
  const halfMaxPages = Math.floor(maxDisplayedPages / 2);

  let startPage = Math.max(1, currentPage - halfMaxPages);
  const endPage = Math.min(totalPages, startPage + maxDisplayedPages - 1);

  if (endPage - startPage + 1 < maxDisplayedPages) {
    startPage = Math.max(1, endPage - maxDisplayedPages + 1);
  }

  // 添加前一页按钮
  pages.push(
    <button
      key="prev"
      onClick={() => onPageChange(currentPage - 1)}
      disabled={currentPage === 1}
      className="mx-1 px-3 py-2 rounded-md border border-gray-300 dark:border-[#333646] bg-white dark:bg-[#242428] text-sm font-medium text-gray-700 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed dark-text-white"
    >
      上一页
    </button>
  );

  // 添加第一页和省略号
  if (startPage > 1) {
    pages.push(
      <button
        key="1"
        onClick={() => onPageChange(1)}
        className="mx-1 px-3 py-2 rounded-md border border-gray-300 dark:border-[#333646] bg-white dark:bg-[#242428] text-sm font-medium text-gray-700 dark:text-white dark-text-white"
      >
        1
      </button>
    );
    if (startPage > 2) {
      pages.push(
        <span key="dots1" className="mx-1 px-3 py-2 dark-text-white">
          ...
        </span>
      );
    }
  }

  // 添加页码
  for (let i = startPage; i <= endPage; i++) {
    pages.push(
      <button
        key={i}
        onClick={() => onPageChange(i)}
        className={`mx-1 px-3 py-2 rounded-md ${
          i === currentPage
            ? "bg-blue-600 text-white border border-blue-600"
            : "bg-white dark:bg-[#242428] text-gray-700 dark:text-white border border-gray-300 dark:border-[#333646] dark-text-white"
        } text-sm font-medium`}
      >
        {i}
      </button>
    );
  }

  // 添加最后页和省略号
  if (endPage < totalPages) {
    if (endPage < totalPages - 1) {
      pages.push(
        <span key="dots2" className="mx-1 px-3 py-2 dark-text-white">
          ...
        </span>
      );
    }
    pages.push(
      <button
        key={totalPages}
        onClick={() => onPageChange(totalPages)}
        className="mx-1 px-3 py-2 rounded-md border border-gray-300 dark:border-[#333646] bg-white dark:bg-[#242428] text-sm font-medium text-gray-700 dark:text-white dark-text-white"
      >
        {totalPages}
      </button>
    );
  }

  // 添加下一页按钮
  pages.push(
    <button
      key="next"
      onClick={() => onPageChange(currentPage + 1)}
      disabled={currentPage === totalPages}
      className="mx-1 px-3 py-2 rounded-md border border-gray-300 dark:border-[#333646] bg-white dark:bg-[#242428] text-sm font-medium text-gray-700 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed dark-text-white"
    >
      下一页
    </button>
  );

  return <div className="flex justify-center my-6 flex-wrap">{pages}</div>;
}

// 搜索结果组件
function SearchResults(): React.ReactNode {
  const searchParams = useSearchParams();
  const initialQuery = searchParams ? searchParams.get("q") || "" : "";
  const initialSearchType = searchParams
    ? (searchParams.get("type") as "local" | "online") || "local"
    : "local";

  const { showToast } = useToast();
  const [query, setQuery] = useState(initialQuery);
  const [results, setResults] = useState<Resource[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchType, setSearchType] = useState<"local" | "online">(
    initialSearchType
  );
  const pageSize = 30; // 每页显示30条
  const [resourceType, setResourceType] = useState("all");
  const [fileType, setFileType] = useState("all");
  const [exactMatch, setExactMatch] = useState(false);
  const [timeFilter, setTimeFilter] = useState("all");
  const [totalCount, setTotalCount] = useState(0);
  // 标记是否已经搜索过，避免重复搜索
  const [hasSearched, setHasSearched] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);
  // 滚动按钮状态
  const [showScrollButtons, setShowScrollButtons] = useState({
    top: false,
    bottom: false,
  });

  // 动态更新页面SEO
  const dynamicSEO = useMemo(() => {
    return generateSearchSEO(query, searchType);
  }, [query, searchType]);

  // 动态更新页面标题
  useEffect(() => {
    if (typeof document !== "undefined") {
      document.title = dynamicSEO.title;

      // 更新meta description
      const metaDescription = document.querySelector(
        'meta[name="description"]'
      );
      if (metaDescription) {
        metaDescription.setAttribute("content", dynamicSEO.description);
      }
    }
  }, [dynamicSEO]);

  // 监听滚动事件，控制滚动按钮的显示
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const scrollHeight = document.documentElement.scrollHeight;
      const clientHeight = document.documentElement.clientHeight;

      // 当滚动位置不在顶部时显示"回到顶部"按钮
      setShowScrollButtons((prev) => ({
        ...prev,
        top: scrollTop > 100,
      }));

      // 当滚动位置不在底部时显示"到达底部"按钮
      setShowScrollButtons((prev) => ({
        ...prev,
        bottom: scrollTop + clientHeight < scrollHeight - 100,
      }));
    };

    window.addEventListener("scroll", handleScroll);
    // 初始化时执行一次
    handleScroll();

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  // 滚动到顶部
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  // 滚动到底部
  const scrollToBottom = () => {
    window.scrollTo({
      top: document.documentElement.scrollHeight,
      behavior: "smooth",
    });
  };

  // 创建Fuse实例用于本地搜索
  const fuse = useMemo(() => {
    const fuseOptions = {
      keys: ["name", "description", "type"],
      threshold: 0.3,
      includeScore: true,
    };
    // 由于resources.json已被删除，使用空数组
    return new Fuse([] as Resource[], fuseOptions);
  }, []);

  // 过滤结果 - 基于资源类型和文件类型
  const filteredResults = useMemo(() => {
    let filtered = results;

    // 如果选择了特定文件类型，且后端没有进行筛选，则在前端进行筛选
    // 如果已经通过API请求参数进行了筛选，则不需要再次筛选
    if (fileType !== "all" && !results.some((r) => r.file_type)) {
      filtered = filtered.filter((resource) => {
        const fileName = resource.name.toLowerCase();

        // 根据文件类型进行过滤
        switch (fileType) {
          case "video":
            return /\.(mp4|mkv|avi|mov|wmv|flv|m4v|rmvb|3gp|ts|webm)$/i.test(
              fileName
            );
          case "audio":
            return /\.(mp3|wav|flac|aac|ogg|wma|m4a|ape|alac)$/i.test(fileName);
          case "image":
            return /\.(jpg|jpeg|png|gif|bmp|webp|svg|tiff|ico|raw|psd)$/i.test(
              fileName
            );
          case "document":
            return /\.(pdf|doc|docx|xls|xlsx|ppt|pptx|txt|csv|rtf|odt|ods|odp|pages|numbers|key|md|tex)$/i.test(
              fileName
            );
          case "archive":
            return /\.(zip|rar|7z|tar|gz|bz2|xz|iso|dmg)$/i.test(fileName);
          case "application":
            return /\.(exe|msi|apk|app|deb|rpm|pkg|appimage|appx|msix)$/i.test(
              fileName
            );
          default:
            return true;
        }
      });
    }

    return filtered;
  }, [results, fileType]);

  // 计算当前页显示的资源
  const displayedResources = useMemo(() => {
    if (searchType === "online") {
      // 联网搜索直接使用后端返回的数据，已经做了分页
      return filteredResults;
    } else {
      // 本地搜索应该显示所有结果中的当前页
      // 这里改为显示标记了当前页码的资源，以及本地资源（isLocal=true）
      const localResources = filteredResults.filter((r) => r.isLocal);
      const currentPageResources = filteredResults.filter(
        (r) => !r.isLocal && r.page === page
      );

      // 对于本地资源（不是缓存资源），仍然使用传统分页方式
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedLocalResources = localResources.slice(
        startIndex,
        Math.min(endIndex, localResources.length)
      );

      // 合并当前页的本地资源和已经按页获取的缓存资源
      return [...paginatedLocalResources, ...currentPageResources];
    }
  }, [filteredResults, page, pageSize, searchType]);

  // 处理资源类型切换
  const handleTypeChange = (type: string) => {
    setResourceType(type);
    setPage(1); // 切换类型时重置到第一页

    // 根据类型获取对应的 pan_type
    const panType = getPanTypeNumber(type);

    // 重新发起搜索请求
    if (searchType === "online" && query) {
      searchOnline(query, 1, panType, exactMatch, fileType, timeFilter);
    } else if (searchType === "local" && query) {
      searchLocal(query, 1, panType, exactMatch, fileType, timeFilter);
    }
  };

  // 处理文件类型变更
  const handleFileTypeChange = (type: string) => {
    setFileType(type);
    setPage(1); // 重置页码到1

    // 切换文件类型时触发新的搜索请求
    if (query) {
      // 获取当前选中的网盘类型对应的 pan_type
      const panType = getPanTypeNumber(resourceType);

      // 重新搜索，并传递文件类型参数
      if (searchType === "online") {
        searchOnline(query, 1, panType, exactMatch, type, timeFilter);
      } else {
        searchLocal(query, 1, panType, exactMatch, type, timeFilter);
      }

      // 滚动到顶部
      window.scrollTo(0, 0);
    }
  };

  // 处理精确搜索切换
  const handleExactMatchChange = (exact: boolean) => {
    setExactMatch(exact);
    setPage(1); // 重置页码到1

    // 如果当前有搜索词，重新搜索
    if (query) {
      // 获取当前选中的网盘类型对应的 pan_type
      const panType = getPanTypeNumber(resourceType);

      // 重新搜索，并传递精确搜索参数和文件类型参数
      if (searchType === "online") {
        searchOnline(query, 1, panType, exact, fileType, timeFilter);
      } else {
        searchLocal(query, 1, panType, exact, fileType, timeFilter);
      }

      // 滚动到顶部
      window.scrollTo(0, 0);
    }
  };

  // 处理时间过滤器变更
  const handleTimeFilterChange = (filter: string) => {
    setTimeFilter(filter);
    setPage(1); // 重置页码到1

    // 如果当前有搜索词，重新搜索
    if (query) {
      // 获取当前选中的网盘类型对应的 pan_type
      const panType = getPanTypeNumber(resourceType);

      // 重新搜索，并传递时间过滤参数
      if (searchType === "online") {
        searchOnline(query, 1, panType, exactMatch, fileType, filter);
      } else {
        searchLocal(query, 1, panType, exactMatch, fileType, filter);
      }

      // 滚动到顶部
      window.scrollTo(0, 0);
    }
  };

  // 本地搜索函数
  const searchLocal = useCallback(
    async (
      searchQuery: string,
      pageNum: number = 1,
      panType: number = 0,
      isExactMatch: boolean = false,
      fileTypeFilter: string = "all",
      timeFilterParam: string = "all"
    ) => {
      if (!searchQuery.trim()) {
        setResults([]);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setApiError(null);

      try {
        // 是否是首次搜索或关键词变更（页码为1时）
        const isInitialSearch = pageNum === 1;

        // 首次搜索时，获取本地资源；翻页时保持已有的本地资源
        let localResults: Resource[] = [];
        if (isInitialSearch) {
          // 首先获取本地资源
          const localSearchResults = fuse.search(searchQuery);
          localResults = localSearchResults.map((result: any) => ({
            ...result.item,
            isLocal: true,
          }));

          // 重置结果集
          setResults(localResults);
        } else {
          // 翻页时保留之前的本地资源结果
          setResults((currentResults) => {
            const localResults = currentResults.filter((r) => r.isLocal);
            return localResults;
          });
        }

        // 获取缓存的资源

        // 添加精确搜索参数、文件类型参数和时间过滤参数
        const exactMatchParam = isExactMatch ? "&exact=1" : "";
        const fileTypeParam =
          fileTypeFilter !== "all" ? `&file_type=${fileTypeFilter}` : "";
        const timeFilterParamStr =
          timeFilterParam !== "all" ? `&time_filter=${timeFilterParam}` : "";

        // 发送请求获取指定类型的网盘资源，添加精确搜索参数、文件类型参数和时间过滤参数
        const cachedResourcesResponse = await fetch(
          `/api/cached_resources?title=${encodeURIComponent(
            searchQuery
          )}&pan_type=${panType}&limit=${pageSize}&page=${pageNum}${exactMatchParam}${fileTypeParam}${timeFilterParamStr}`
        ).then((r) => r.json());

        if (cachedResourcesResponse.status === "failed") {
          setResults([]);
          setTotalCount(0);
          setApiError(cachedResourcesResponse.message);
          setIsLoading(false);
          return;
        }

        const cachedResources = cachedResourcesResponse.resources || [];

        // 获取后端返回的总数和总页数
        const totalResources = cachedResourcesResponse.total || 0;
        const totalPages = cachedResourcesResponse.pages || 1;

        // 设置总页数和总数
        if (isInitialSearch || pageNum === 1) {
          setTotalPages(totalPages);
          // 更新总资源数
          const localResourceCount = localResults.length;
          setTotalCount(localResourceCount + totalResources);
        }

        // 将资源按类型转换
        const convertedCachedResources = cachedResources.map(
          (item: ApiResource) => {
            const panType = item.pan_type;
            const type = getPanTypeText(panType);

            return {
              id:
                item.share_url?.split("/s/")[1]?.split("?")[0] ||
                `${type
                  .split(" ")[0]
                  .toLowerCase()}-cached-${Date.now()}-${Math.random()}`,
              name: item.title,
              description: "",
              type: type,
              size: "未知大小",
              baiduLink:
                panType === 1
                  ? item.verified_status && item.share_url
                    ? item.share_url
                    : item.original_url
                  : undefined,
              quarkLink:
                panType === 2
                  ? item.verified_status && item.share_url
                    ? item.share_url
                    : item.original_url
                  : undefined,
              aliyunLink:
                panType === 3
                  ? item.verified_status && item.share_url
                    ? item.share_url
                    : item.original_url
                  : undefined,
              thunderLink:
                panType === 4
                  ? item.verified_status && item.share_url
                    ? item.share_url
                    : item.original_url
                  : undefined,
              date: item.updated_at
                ? item.updated_at
                : new Date().toISOString(),
              isLocal: false,
              verified: item.verified_status === "valid" && !!item.share_url,
              page: pageNum,
              resourceId: item.resource_id,
              text_content: item.text_content,
              file_type:
                item.file_type ||
                (fileTypeFilter !== "all" ? fileTypeFilter : undefined),
            };
          }
        );

        // 如果是翻页操作，只需要添加新页的缓存结果，本地资源保持不变
        if (!isInitialSearch) {
          // 使用函数式更新，不依赖外部 results 变量
          setResults((currentResults) => {
            // 获取当前的本地结果
            const localResults = currentResults.filter((r) => r.isLocal);
            // 合并新的缓存结果
            return [...localResults, ...convertedCachedResources];
          });
        } else {
          // 首次搜索，合并本地和缓存结果
          setResults(() => {
            const combinedResults = [
              ...localResults,
              ...convertedCachedResources,
            ];
            return combinedResults;
          });
        }

        setIsLoading(false);
      } catch (error) {
        console.error("本地搜索失败:", error);
        showToast("本地搜索失败，请稍后再试。", "error");
        setIsLoading(false);
      }
    },
    [fuse, pageSize, showToast]
  );

  // 联网搜索函数 - 使用 useCallback 包装
  const searchOnline = useCallback(
    async (
      searchQuery: string,
      pageNum: number = 1,
      panType: number = 0,
      isExactMatch: boolean = false,
      fileTypeFilter: string = "all",
      timeFilterParam: string = "all"
    ) => {
      if (!searchQuery.trim()) {
        setResults([]);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setApiError(null);

      try {
        // 发送搜索请求，添加精确搜索参数、文件类型参数和时间过滤参数
        const allData = await searchPanResources(
          searchQuery,
          panType,
          pageNum,
          pageSize,
          isExactMatch,
          fileTypeFilter,
          timeFilterParam
        ).then((data) => {
          return data;
        });

        if (allData.status === "failed") {
          setResults([]);
          setTotalCount(0);
          setApiError(allData.message);
          setIsLoading(false);
          return;
        }

        const searchResults = allData.results || [];
        const total = allData.total || 0;

        // 转换结果为界面需要的数据结构
        const convertedResults = searchResults.map((item: SearchResult) => {
          const panType = item.pan_type;
          const type = getPanTypeText(panType);

          return {
            id:
              item.share_url?.split("/s/")[1]?.split("?")[0] ||
              `${type
                .split(" ")[0]
                .toLowerCase()}-${Date.now()}-${Math.random()}`,
            name: item.file_name || item.file_name || "未知文件名",
            description: "",
            type: type,
            size: item.file_size || "未知大小",
            baiduLink: panType === 1 ? item.share_url : undefined,
            quarkLink: panType === 2 ? item.share_url : undefined,
            aliyunLink: panType === 3 ? item.share_url : undefined,
            thunderLink: panType === 4 ? item.share_url : undefined,
            date:
              item.updated_at || item.created_at || new Date().toISOString(),
            isLocal: false,
            verified: true,
            page: pageNum,
            resourceId: item.resource_id,
            text_content: item.text_content,
            file_type:
              item.file_type ||
              (fileTypeFilter !== "all" ? fileTypeFilter : undefined),
          };
        });

        // 设置结果
        setResults(convertedResults);
        setTotalCount(total);

        // 计算总页数
        const calculatedTotalPages = Math.ceil(total / pageSize) || 1;
        setTotalPages(calculatedTotalPages);

        setIsLoading(false);
      } catch (error) {
        console.error("联网搜索失败:", error);
        setResults([]);
        showToast("联网搜索失败，请稍后再试。", "error");
        setIsLoading(false);
      }
    },
    [pageSize, showToast]
  );

  // 处理页面变化
  const handlePageChange = useCallback(
    (newPage: number) => {
      setPage(newPage);
      // 切换页面时滚动到顶部
      window.scrollTo(0, 0);

      // 获取当前选中的网盘类型对应的 pan_type
      const panType = getPanTypeNumber(resourceType);

      // 对于联网搜索或本地缓存搜索，当翻页时需要加载相应页的数据
      if (searchType === "online" && query) {
        searchOnline(query, newPage, panType, exactMatch, fileType, timeFilter);
      } else if (searchType === "local" && query) {
        searchLocal(query, newPage, panType, exactMatch, fileType, timeFilter);
      }
    },
    [
      query,
      searchType,
      resourceType,
      exactMatch,
      fileType,
      timeFilter,
      searchLocal,
      searchOnline,
    ]
  );

  // 处理搜索
  const handleSearch = useCallback(
    (newQuery: string, newSearchType: "local" | "online") => {
      setQuery(newQuery);
      setSearchType(newSearchType);
      setPage(1);
      setIsLoading(true);
      setHasSearched(true);

      // 更新 URL 参数
      const url = new URL(window.location.href);
      url.searchParams.set("q", newQuery);
      url.searchParams.set("type", newSearchType);
      window.history.pushState({}, "", url.toString());

      // 获取当前选中的网盘类型对应的 pan_type
      const panType = getPanTypeNumber(resourceType);

      if (newSearchType === "local") {
        searchLocal(newQuery, 1, panType, exactMatch, fileType, timeFilter);
      } else {
        searchOnline(newQuery, 1, panType, exactMatch, fileType, timeFilter);
      }
    },
    [resourceType, exactMatch, fileType, timeFilter, searchLocal, searchOnline]
  );

  // 初始查询 - 修改以防止重复搜索
  useEffect(() => {
    if (!hasSearched && initialQuery) {
      handleSearch(initialQuery, initialSearchType);
    }
  }, [hasSearched, initialQuery, initialSearchType, handleSearch]);

  // 确保页面URL变化时重新搜索，优化以防止重复请求
  useEffect(
    function handleRouteChangeEffect() {
      const handleRouteChange = () => {
        const params = new URLSearchParams(window.location.search);
        const q = params.get("q");
        const type = params.get("type") as "local" | "online";

        if (q) {
          setQuery(q);
          setHasSearched(true);

          // 获取当前选中的网盘类型对应的 pan_type
          const panType = getPanTypeNumber(resourceType);

          if (type === "local") {
            searchLocal(q, 1, panType, exactMatch, fileType, timeFilter);
          } else {
            searchOnline(q, 1, panType, exactMatch, fileType, timeFilter);
          }
        }
      };

      window.addEventListener("popstate", handleRouteChange, { passive: true });

      return () => {
        window.removeEventListener("popstate", handleRouteChange);
      };
    },
    [searchLocal, searchOnline, resourceType, exactMatch, fileType, timeFilter]
  );

  return (
    <div className="container mx-auto px-4 pt-12 pb-12">
      {/* 页面标题 */}
      <div className="text-center mb-4">
        <h1 className="text-3xl font-bold text-[var(--foreground)]">
          资源搜索
        </h1>
      </div>

      {/* 搜索栏 */}
      <div className="mb-8">
        <SearchBar
          onSearch={handleSearch}
          initialValue={query}
          initialSearchType={searchType}
          placeholder="搜索网盘资源..."
        />
      </div>

      {/* 过滤器组件 */}
      <FilterBar
        panType={resourceType}
        onPanTypeChange={handleTypeChange}
        fileType={fileType}
        onFileTypeChange={handleFileTypeChange}
        exactMatch={exactMatch}
        onExactMatchChange={handleExactMatchChange}
        timeFilter={timeFilter}
        onTimeFilterChange={handleTimeFilterChange}
      />

      {/* 搜索状态提示 */}
      <div className="text-center mb-4 min-h-[24px]">
        {isLoading ? (
          <div className="inline-flex items-center text-gray-500 dark:text-gray-400">
            <svg
              className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            <span>搜索中...</span>
          </div>
        ) : apiError ? (
          <span className="text-red-500 font-medium">{apiError}</span>
        ) : (
          query &&
          hasSearched && (
            <span className="text-gray-800 dark-text-white">
              搜索 <span className="font-semibold">{query}</span> 的结果， 共{" "}
              <span className="text-blue-500 font-bold">{totalCount}</span> 条
            </span>
          )
        )}
      </div>

      {/* 搜索结果 */}
      {!isLoading ? (
        displayedResources.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {displayedResources.map((resource) => (
                <ResourceCard
                  key={resource.id}
                  id={resource.id}
                  name={resource.name}
                  description={resource.description}
                  type={resource.type}
                  size={resource.size}
                  baiduLink={resource.baiduLink}
                  quarkLink={resource.quarkLink}
                  aliyunLink={resource.aliyunLink}
                  thunderLink={resource.thunderLink}
                  date={resource.date}
                  isLocal={resource.isLocal}
                  resourceId={resource.resourceId}
                  searchType={searchType}
                  text_content={resource.text_content}
                  file_type={resource.file_type}
                  onDelete={() => {
                    setResults((prev) =>
                      prev.filter((r) => r.id !== resource.id)
                    );
                  }}
                />
              ))}
            </div>

            {/* 求助提示 - 当搜索结果少于30条时显示 */}
            {displayedResources.length < 30 && query && hasSearched && (
              <div className="mt-8 p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="text-center">
                  <QuestionMarkCircleIcon className="h-12 w-12 text-blue-600 mx-auto mb-3" />
                  <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
                    未找到想要的资源？
                  </h3>
                  <p className="text-blue-700 dark:text-blue-300 mb-4">
                    试试向社区求助，让其他用户帮您找到资源
                  </p>
                  <Link
                    href={`/help-requests/create?q=${encodeURIComponent(
                      query
                    )}`}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <QuestionMarkCircleIcon className="h-5 w-5 mr-2" />
                    发布求助
                  </Link>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500 dark:text-white mb-4">
              {query
                ? "没有找到符合条件的资源，请尝试其他关键词或搜索方式"
                : "请输入关键词进行搜索"}
            </p>
            {/* 无结果时的求助提示 */}
            {query && hasSearched && (
              <div className="mt-6 p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 max-w-md mx-auto">
                <QuestionMarkCircleIcon className="h-10 w-10 text-blue-600 mx-auto mb-3" />
                <h3 className="text-base font-medium text-blue-900 dark:text-blue-100 mb-2">
                  向社区求助
                </h3>
                <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
                  让其他用户帮您找到这个资源
                </p>
                <Link
                  href={`/help-requests/create?q=${encodeURIComponent(query)}`}
                  className="inline-flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
                >
                  <QuestionMarkCircleIcon className="h-4 w-4 mr-1" />
                  发布求助
                </Link>
              </div>
            )}
          </div>
        )
      ) : null}

      {/* 分页信息 */}
      {!isLoading && totalPages > 0 && (
        <div className="text-center text-gray-500 dark:text-white mt-4 mb-2">
          当前第 <span className="font-semibold">{page}</span> 页，共{" "}
          <span className="font-semibold">{totalPages}</span> 页
        </div>
      )}

      {/* 分页控件 - 所有类型都显示分页 */}
      {!isLoading && totalPages > 1 && (
        <Pagination
          currentPage={page}
          totalPages={totalPages}
          onPageChange={handlePageChange}
        />
      )}

      {/* 滚动按钮 */}
      {showScrollButtons.top && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-20 right-6 p-2 rounded-lg shadow-md transition-all duration-300 dark-text-white scroll-button-bg"
          aria-label="滚动到页面顶部"
        >
          <svg
            className="h-5 w-5"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 15l7-7 7 7"
            />
          </svg>
        </button>
      )}
      {showScrollButtons.bottom && (
        <button
          onClick={scrollToBottom}
          className="fixed bottom-6 right-6 p-2 rounded-lg shadow-md transition-all duration-300 dark-text-white scroll-button-bg"
          aria-label="滚动到页面底部"
        >
          <svg
            className="h-5 w-5"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </button>
      )}
    </div>
  );
}

export default function Search() {
  return (
    <main className="py-4">
      <div className="container mx-auto">
        <Suspense fallback={<div className="text-center">加载中...</div>}>
          <SearchResults />
        </Suspense>
      </div>
    </main>
  );
}
