#!/usr/bin/env node

/**
 * 验证筛选器界面优化
 * 检查边框移除和时间范围筛选器移除的效果
 */

// eslint-disable-next-line @typescript-eslint/no-require-imports
const fs = require("fs");
// eslint-disable-next-line @typescript-eslint/no-require-imports
const path = require("path");

// 需要检查的文件列表
const filesToCheck = [
  "src/types/help-request.ts",
  "src/app/help-requests/page.tsx",
  "src/app/admin/help-requests/page.tsx",
  "src/components/help-requests/HelpRequestFilters.tsx",
  "src/services/helpRequestService.ts",
];

// 应该被移除的内容
const deprecatedContent = [
  "time_range",
  "border border-border-color", // 在筛选器容器中
  "lg:grid-cols-5", // 管理员页面的5列布局
  "lg:grid-cols-4", // 求助页面的4列布局（应该改为3列）
];

// 应该保留的内容
const requiredContent = ["status", "pan_type", "sort_by", "search"];

console.log("🎨 开始验证筛选器界面优化...\n");

let totalIssues = 0;
let totalFiles = 0;
let optimizedFiles = 0;

// 检查单个文件
function checkFile(filePath) {
  const fullPath = path.join(process.cwd(), filePath);

  if (!fs.existsSync(fullPath)) {
    console.log(`⚠️  文件不存在: ${filePath}`);
    return { issues: 0, optimized: false };
  }

  const content = fs.readFileSync(fullPath, "utf8");
  const lines = content.split("\n");
  let fileIssues = 0;
  let hasOptimization = false;

  console.log(`📁 检查文件: ${filePath}`);

  // 检查是否还有time_range相关代码
  if (content.includes("time_range")) {
    const timeRangeLines = lines
      .map((line, index) => ({ line: line.trim(), number: index + 1 }))
      .filter((item) => item.line.includes("time_range"));

    if (timeRangeLines.length > 0) {
      console.log(`  ❌ 发现 ${timeRangeLines.length} 处time_range残留:`);
      timeRangeLines.forEach((item) => {
        console.log(`     第${item.number}行: ${item.line}`);
      });
      fileIssues += timeRangeLines.length;
    }
  } else {
    console.log(`  ✅ 已移除所有time_range相关代码`);
    hasOptimization = true;
  }

  // 检查筛选器容器的边框
  if (filePath.includes("page.tsx")) {
    // 查找筛选器组件的容器样式
    const filterContainerRegex =
      /function HelpRequestFilters[\s\S]*?return \(\s*<div className="([^"]*)">/;
    const match = content.match(filterContainerRegex);

    if (match) {
      const containerClasses = match[1];
      if (containerClasses.includes("border border-border-color")) {
        console.log(`  ❌ 筛选器容器仍有边框样式: ${containerClasses}`);
        fileIssues++;
      } else if (containerClasses.includes("bg-card-background rounded-lg")) {
        console.log(`  ✅ 筛选器容器边框已移除`);
        hasOptimization = true;
      }
    } else {
      console.log(`  ℹ️  未找到筛选器容器定义`);
    }
  }

  // 检查网格布局调整
  if (filePath === "src/app/help-requests/page.tsx") {
    if (content.includes("lg:grid-cols-3")) {
      console.log(`  ✅ 求助页面布局已调整为3列`);
      hasOptimization = true;
    } else if (content.includes("lg:grid-cols-4")) {
      console.log(`  ❌ 求助页面仍使用4列布局`);
      fileIssues++;
    }
  }

  if (filePath === "src/app/admin/help-requests/page.tsx") {
    if (content.includes("lg:grid-cols-4")) {
      console.log(`  ✅ 管理员页面布局已调整为4列`);
      hasOptimization = true;
    } else if (content.includes("lg:grid-cols-5")) {
      console.log(`  ❌ 管理员页面仍使用5列布局`);
      fileIssues++;
    }
  }

  // 检查必需的筛选器是否保留
  const requiredFilters = ["status", "pan_type", "sort_by"];
  const missingFilters = requiredFilters.filter(
    (filter) => !content.includes(filter)
  );

  if (missingFilters.length > 0) {
    console.log(`  ❌ 缺少必需的筛选器: ${missingFilters.join(", ")}`);
    fileIssues += missingFilters.length;
  } else {
    console.log(`  ✅ 所有必需的筛选器都已保留`);
  }

  // 检查类型定义
  if (filePath.includes("help-request.ts")) {
    if (content.includes("time_range?:")) {
      console.log(`  ❌ 类型定义中仍有time_range字段`);
      fileIssues++;
    } else {
      console.log(`  ✅ 类型定义已移除time_range字段`);
      hasOptimization = true;
    }
  }

  if (fileIssues === 0 && hasOptimization) {
    console.log(`  ✅ 文件优化完成`);
  } else if (fileIssues === 0) {
    console.log(`  ℹ️  文件无需优化`);
  }

  console.log("");
  return { issues: fileIssues, optimized: hasOptimization };
}

// 检查所有文件
filesToCheck.forEach((filePath) => {
  totalFiles++;
  const result = checkFile(filePath);
  totalIssues += result.issues;
  if (result.optimized) {
    optimizedFiles++;
  }
});

// 生成总结报告
console.log("=".repeat(60));
console.log("📊 筛选器界面优化验证报告");
console.log("=".repeat(60));

console.log(`📁 检查文件总数: ${totalFiles}`);
console.log(`✅ 已优化文件数: ${optimizedFiles}`);
console.log(`❌ 发现问题总数: ${totalIssues}`);

if (totalIssues === 0) {
  console.log("\n🎉 恭喜！筛选器界面优化验证通过！");
  console.log("\n✅ 优化成果:");
  console.log("   - 移除了筛选器容器的边框样式");
  console.log("   - 删除了时间范围筛选器");
  console.log("   - 保留了4个核心筛选器：状态、网盘类型、排序方式、搜索");
  console.log("   - 调整了网格布局以适应新的筛选器数量");
  console.log("   - 更新了相关的类型定义和API调用");

  console.log("\n🎨 界面改进:");
  console.log("   - 更简洁的视觉设计（无边框）");
  console.log("   - 更紧凑的布局（减少筛选器数量）");
  console.log("   - 保持了响应式设计");
  console.log("   - 维持了可访问性标准");

  console.log("\n📱 功能验证:");
  console.log("   1. 状态筛选：求助中/已解决/已关闭");
  console.log("   2. 网盘类型筛选：百度网盘/夸克网盘/阿里云盘等");
  console.log("   3. 排序方式筛选：最新/最热/回答数等");
  console.log("   4. 资源搜索框：关键词搜索");

  console.log("\n🧪 测试建议:");
  console.log("   1. 在求助列表页面测试所有筛选器功能");
  console.log("   2. 在管理员页面验证筛选器正常工作");
  console.log("   3. 检查响应式布局在不同设备上的表现");
  console.log("   4. 验证明暗主题下的显示效果");

  process.exit(0);
} else {
  console.log("\n⚠️  发现需要修复的问题！");
  console.log("\n🔧 修复建议:");
  console.log("   1. 移除所有time_range相关的代码");
  console.log("   2. 更新筛选器容器样式，移除边框");
  console.log("   3. 调整网格布局列数");
  console.log("   4. 确保所有必需的筛选器功能正常");

  console.log("\n📋 下一步行动:");
  console.log("   1. 修复上述发现的问题");
  console.log("   2. 重新运行此验证脚本");
  console.log("   3. 进行功能测试验证");
  console.log("   4. 部署到测试环境");

  process.exit(1);
}

// 额外的优化建议
console.log("\n💡 进一步优化建议:");
console.log("   - 考虑添加筛选器状态的本地存储");
console.log("   - 实现筛选器的快捷重置功能");
console.log("   - 添加筛选结果的统计显示");
console.log("   - 考虑添加高级筛选选项");

console.log("\n📚 相关文档:");
console.log("   - 筛选器优化报告: docs/filter-optimization-report.md");
console.log("   - 用户界面设计指南: docs/ui-design-guidelines.md");
console.log("   - 响应式设计规范: docs/responsive-design.md");
