"use client";

import { useState, useRef } from "react";
import * as XLSX from "xlsx";
import { useToast } from "@/components/ToastProvider";

interface ExcelImportProps {
  onImportComplete?: (result: any) => void;
  maxRows?: number;
  isPersonalResource?: boolean;
  fillParseFlag?: boolean;
  isAdmin?: boolean;
}

interface ExcelRow {
  url: string;
  title?: string;
  description?: string;
  isValid: boolean;
  error?: string;
}

export default function ExcelImport({
  onImportComplete,
  maxRows = 2000,
  isPersonalResource = false,
  fillParseFlag = false,
  isAdmin = false,
}: ExcelImportProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [previewData, setPreviewData] = useState<ExcelRow[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { showToast } = useToast();

  // 验证URL格式
  const validateUrl = (url: string): { isValid: boolean; error?: string } => {
    if (!url || typeof url !== "string") {
      return { isValid: false, error: "URL不能为空" };
    }

    const trimmedUrl = url.trim();
    if (!trimmedUrl) {
      return { isValid: false, error: "URL不能为空" };
    }

    // 检查是否包含中文
    if (/[\u4e00-\u9fa5]/.test(trimmedUrl)) {
      return { isValid: false, error: "URL不能包含中文字符" };
    }

    // 检查是否是有效的URL格式
    try {
      new URL(trimmedUrl);
    } catch {
      return { isValid: false, error: "无效的URL格式" };
    }

    // 检查是否是支持的网盘类型
    const supportedDomains = [
      "pan.baidu.com",
      "www.aliyundrive.com",
      "pan.quark.cn",
      "pan.xunlei.com",
    ];

    const isSupported = supportedDomains.some((domain) =>
      trimmedUrl.includes(domain)
    );

    if (!isSupported) {
      return { isValid: false, error: "不支持的网盘类型" };
    }

    return { isValid: true };
  };

  // 处理文件上传
  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 检查文件类型
    if (!file.name.match(/\.(xlsx|xls)$/)) {
      showToast("请选择Excel文件（.xlsx或.xls格式）", "error");
      return;
    }

    // 检查文件大小（限制为5MB）
    if (file.size > 5 * 1024 * 1024) {
      showToast("文件大小不能超过5MB", "error");
      return;
    }

    setIsProcessing(true);
    try {
      const data = await file.arrayBuffer();
      const workbook = XLSX.read(data, { type: "array" });

      // 获取第一个工作表
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];

      // 转换为JSON数据
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      // 处理数据
      const processedData = processExcelData(jsonData as any[][]);

      if (processedData.length === 0) {
        showToast("Excel文件中没有找到有效的URL数据", "error");
        return;
      }

      if (processedData.length > maxRows) {
        showToast(
          `单次最多导入${maxRows}条数据，当前文件包含${processedData.length}条`,
          "error"
        );
        return;
      }

      setPreviewData(processedData);
      setShowPreview(true);
      showToast(`成功解析${processedData.length}条数据`, "success");
    } catch (error) {
      console.error("Excel解析失败:", error);
      showToast("Excel文件解析失败，请检查文件格式", "error");
    } finally {
      setIsProcessing(false);
      // 清空文件输入
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  // 处理Excel数据
  const processExcelData = (data: any[][]): ExcelRow[] => {
    const result: ExcelRow[] = [];

    // 跳过标题行，从第二行开始处理
    for (let i = 1; i < data.length; i++) {
      const row = data[i];
      if (!row || row.length === 0) continue;

      const url = row[0]?.toString().trim();
      if (!url) continue;

      const validation = validateUrl(url);

      result.push({
        url,
        title: row[1]?.toString().trim() || "",
        description: "", // 不再使用描述字段
        isValid: validation.isValid,
        error: validation.error,
      });
    }

    return result;
  };

  // 下载Excel模板
  const downloadTemplate = () => {
    const templateData = [
      ["URL", "标题（如果分享文件夹名称与实际不一致,请务必填写）"],
      ["https://pan.baidu.com/s/example1", "示例资源1"],
      ["https://www.aliyundrive.com/s/example2", "示例资源2"],
      ["https://pan.quark.cn/s/example3", "示例资源3"],
      ["https://pan.xunlei.com/s/example4", "示例资源4"],
    ];

    const worksheet = XLSX.utils.aoa_to_sheet(templateData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "资源模板");

    // 设置列宽
    worksheet["!cols"] = [
      { width: 50 }, // URL列
      { width: 30 }, // 标题列
    ];

    XLSX.writeFile(workbook, "资源导入模板.xlsx");
    showToast("模板下载成功", "success");
  };

  // 确认导入
  const handleConfirmImport = async () => {
    const validData = previewData
      .filter((row) => row.isValid)
      .map((row) => ({
        url: row.url,
        title: row.title || "",
      }));

    if (validData.length === 0) {
      showToast("没有有效的URL可以导入", "error");
      return;
    }

    setIsSubmitting(true);
    try {
      // 构建请求体，包含管理员选项
      const requestBody: any = {
        urls: validData,
      };

      // 如果是管理员，添加特殊选项
      if (isAdmin) {
        requestBody.is_mine = isPersonalResource;
        requestBody.is_parsed = fillParseFlag;
        requestBody.admin_submit = true;
      }

      // 添加认证头
      const token = localStorage.getItem("auth_token");
      const headers: any = { "Content-Type": "application/json" };
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await fetch("/api/submit_resources", {
        method: "POST",
        headers,
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "提交失败");
      }

      showToast(data.message || "Excel导入提交成功！", "success");
      setShowPreview(false);
      setPreviewData([]);

      // 通知父组件导入完成
      if (onImportComplete) {
        onImportComplete(data);
      }
    } catch (error: any) {
      console.error("Excel导入提交失败:", error);
      showToast(error.message || "提交失败，请稍后重试", "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  // 取消预览
  const handleCancelPreview = () => {
    setShowPreview(false);
    setPreviewData([]);
  };

  const validCount = previewData.filter((row) => row.isValid).length;
  const invalidCount = previewData.length - validCount;

  return (
    <div className="space-y-4">
      {/* 文件上传区域 */}
      <div className="border-2 border-dashed border-[var(--border-color)] rounded-lg p-6 text-center">
        <div className="space-y-4">
          <div className="text-4xl">📊</div>
          <div>
            <h3 className="text-lg font-medium mb-2">Excel批量导入</h3>
            <p className="text-[var(--secondary-text)] text-sm mb-4">
              支持.xlsx和.xls格式，单次最多导入{maxRows}条数据
              <br />
              支持URL和标题两列，标题为可选项
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
            <button
              type="button"
              onClick={downloadTemplate}
              className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200"
            >
              下载模板
            </button>

            <input
              ref={fileInputRef}
              type="file"
              accept=".xlsx,.xls"
              onChange={handleFileUpload}
              className="hidden"
              id="excel-upload"
            />
            <label
              htmlFor="excel-upload"
              className={`px-4 py-2 bg-[var(--button-background)] hover:bg-[var(--button-hover)] text-white rounded-lg transition-colors duration-200 cursor-pointer ${
                isProcessing ? "opacity-50 cursor-not-allowed" : ""
              }`}
            >
              {isProcessing ? "解析中..." : "选择Excel文件"}
            </label>
          </div>
        </div>
      </div>

      {/* 预览数据 */}
      {showPreview && previewData.length > 0 && (
        <div className="bg-[var(--card-background)] p-6 rounded-xl shadow-sm border border-[var(--border-color)]">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">数据预览</h3>
            <div className="text-sm text-[var(--secondary-text)]">
              有效: <span className="text-green-600">{validCount}</span> | 无效:{" "}
              <span className="text-red-600">{invalidCount}</span>
            </div>
          </div>

          <div className="max-h-96 overflow-y-auto border border-[var(--border-color)] rounded-lg">
            <table className="min-w-full">
              <thead className="bg-[var(--hover-background)] sticky top-0">
                <tr>
                  <th className="py-2 px-4 text-left">状态</th>
                  <th className="py-2 px-4 text-left">URL</th>
                  <th className="py-2 px-4 text-left">标题</th>
                  <th className="py-2 px-4 text-left">错误信息</th>
                </tr>
              </thead>
              <tbody>
                {previewData.map((row, index) => (
                  <tr
                    key={index}
                    className={
                      index % 2 === 0
                        ? "bg-[var(--background)]"
                        : "bg-[var(--card-background)]"
                    }
                  >
                    <td className="py-2 px-4">
                      <span
                        className={`inline-block w-3 h-3 rounded-full ${
                          row.isValid ? "bg-green-500" : "bg-red-500"
                        }`}
                      />
                    </td>
                    <td className="py-2 px-4 truncate max-w-xs" title={row.url}>
                      {row.url}
                    </td>
                    <td
                      className="py-2 px-4 truncate max-w-xs"
                      title={row.title}
                    >
                      {row.title || "无标题"}
                    </td>
                    <td className="py-2 px-4 text-red-600 text-sm">
                      {row.error || ""}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="flex justify-end gap-3 mt-4">
            <button
              type="button"
              onClick={handleCancelPreview}
              className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200"
            >
              取消
            </button>
            <button
              type="button"
              onClick={handleConfirmImport}
              disabled={validCount === 0 || isSubmitting}
              className="px-4 py-2 bg-[var(--button-background)] hover:bg-[var(--button-hover)] text-white rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? "提交中..." : `提交 ${validCount} 条有效数据`}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
