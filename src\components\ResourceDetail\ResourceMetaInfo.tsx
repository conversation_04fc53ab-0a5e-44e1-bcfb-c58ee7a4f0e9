'use client';

import React, { memo } from 'react';
import { formatDate } from '@/lib/utils';
import { ResourceDetail } from '@/types/resource';

interface ResourceMetaInfoProps {
  resource: ResourceDetail;
}

/**
 * 获取文件类型的中文名称
 */
const getFileTypeName = (fileType: string): string => {
  switch (fileType) {
    case "video":
      return "视频";
    case "audio":
      return "音频";
    case "image":
      return "图片";
    case "document":
      return "文档";
    case "archive":
      return "压缩包";
    case "application":
      return "应用";
    default:
      return "其他";
  }
};

/**
 * 获取网盘类型名称
 */
const getPanTypeName = (panType: number): string => {
  switch (panType) {
    case 1:
      return "百度网盘";
    case 2:
      return "夸克网盘";
    case 3:
      return "阿里云盘";
    case 4:
      return "迅雷网盘";
    default:
      return "未知网盘";
  }
};

/**
 * 资源元信息组件
 * 显示资源的基本信息，如文件大小、类型、更新时间等
 */
const ResourceMetaInfo = memo(function ResourceMetaInfo({
  resource,
}: ResourceMetaInfoProps) {
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
      <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
        <div className="text-sm text-gray-500 dark:text-gray-400">文件大小</div>
        <div className="font-medium text-gray-900 dark:text-white">
          {resource.file_size || "未知"}
        </div>
      </div>
      
      <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
        <div className="text-sm text-gray-500 dark:text-gray-400">文件类型</div>
        <div className="font-medium text-gray-900 dark:text-white">
          {getFileTypeName(resource.file_type)}
        </div>
      </div>
      
      <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
        <div className="text-sm text-gray-500 dark:text-gray-400">网盘类型</div>
        <div className="font-medium text-gray-900 dark:text-white">
          {getPanTypeName(resource.pan_type)}
        </div>
      </div>
      
      <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
        <div className="text-sm text-gray-500 dark:text-gray-400">更新时间</div>
        <div className="font-medium text-gray-900 dark:text-white">
          {formatDate(resource.updated_at)}
        </div>
      </div>
    </div>
  );
});

export default ResourceMetaInfo;
