#!/usr/bin/env node

/**
 * 重置公告显示状态脚本
 * 
 * 此脚本通过添加或更新公告的 version 属性来强制用户再次看到公告。
 * 即使用户之前已经关闭过公告，当 version 更新后，公告会再次显示。
 */

import fs from 'fs';
import path from 'path';
import yaml from 'js-yaml';

// 配置文件路径
const CONFIG_PATH = path.join(__dirname, '../public/notification.yaml');

// 读取当前配置
const readConfig = () => {
  try {
    const fileContent = fs.readFileSync(CONFIG_PATH, 'utf8');
    return yaml.load(fileContent);
  } catch (error) {
    console.error('读取配置失败:', error.message);
    return {
      enabled: false,
      title: '系统公告',
      content: '欢迎使用我们的网盘搜索服务！',
      start_time: '',
      end_time: ''
    };
  }
};

// 写入配置
const writeConfig = (config) => {
  try {
    // 确保enabled为true
    config.enabled = true;
    
    // 添加或更新version，使用当前时间戳
    config.reset_key = new Date().getTime().toString();
    
    const yamlStr = yaml.dump(config, {
      lineWidth: 120,
      indent: 2,
      noRefs: true,
      quotingType: '"'
    });
    
    // 添加注释
    const configWithComments = 
`# 网站公告配置

# 是否启用公告（true/false）
${yamlStr.replace(/enabled:/, 'enabled:')}
# 公告标题
title: ${config.title}

# 公告内容（支持换行和简单格式）
content: |
${config.content.split('\n').map(line => `  ${line}`).join('\n')}

# 公告显示时间（格式：YYYY-MM-DD，为空则永久有效）
start_time: ${config.start_time || ''}
end_time: ${config.end_time || ''}

# 重置键（用于强制用户再次看到公告，每次重置会自动更新）
reset_key: ${config.reset_key}`;

    fs.writeFileSync(CONFIG_PATH, configWithComments, 'utf8');
    console.log('配置已更新!');
  } catch (error) {
    console.error('写入配置失败:', error.message);
  }
};

// 主函数
const main = () => {
  console.log('正在重置公告状态...');
  const config = readConfig();
  
  // 确保启用
  if (!config.enabled) {
    console.log('公告当前处于禁用状态，已自动启用。');
  }
  
  writeConfig(config);
  console.log(`公告已重置! 重置键: ${config.reset_key}`);
  console.log('所有用户将在下次访问网站时看到公告。');
};

main(); 