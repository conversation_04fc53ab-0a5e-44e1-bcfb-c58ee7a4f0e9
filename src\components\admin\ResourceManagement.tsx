"use client";

import { useState, useEffect, useCallback } from "react";
import { useToast } from "@/components/ToastProvider";
import { EyeIcon, TrashIcon } from "@heroicons/react/24/outline";
import SearchFilter, { FilterField } from "@/components/admin/SearchFilter";
import DataTable, { Column } from "@/components/admin/DataTable";
import {
  getAdminResourceList,
  deleteAdminResource,
  batchDeleteAdminResources,
  AdminResource,
  AdminResourceQueryParams,
} from "@/services/resourceService";

export default function ResourceManagement() {
  const { showToast } = useToast();
  const [resources, setResources] = useState<AdminResource[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [panTypeFilter, setPanTypeFilter] = useState("");
  const [fileTypeFilter, setFileTypeFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [isMineFilter, setIsMineFilter] = useState("");
  const [timeFilter, setTimeFilter] = useState("all");
  const [selectedResources, setSelectedResources] = useState<number[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalResources, setTotalResources] = useState(0);
  const pageSize = 20;

  // 当前搜索关键词状态（用于实际搜索）
  const [currentSearchTerm, setCurrentSearchTerm] = useState("");

  // 加载资源列表
  const loadResources = useCallback(async () => {
    try {
      setLoading(true);

      // 检查认证状态
      const token =
        typeof window !== "undefined"
          ? localStorage.getItem("auth_token")
          : null;

      if (!token) {
        showToast("请先登录", "error");
        return;
      }

      const params: AdminResourceQueryParams = {
        page: currentPage,
        size: pageSize,
        sort_by: "updated_at",
        sort_order: "desc",
        time_filter: timeFilter,
      };

      if (panTypeFilter) params.pan_type = panTypeFilter;
      if (fileTypeFilter) params.file_type = fileTypeFilter;
      if (statusFilter) params.status = statusFilter;
      if (isMineFilter) params.is_mine = isMineFilter;
      if (currentSearchTerm) params.keyword = currentSearchTerm;

      const response = await getAdminResourceList(params);

      setResources(response.data.resources);
      setTotalResources(response.data.pagination.total);
    } catch {
      showToast("加载资源列表失败", "error");
    } finally {
      setLoading(false);
    }
  }, [
    currentPage,
    timeFilter,
    panTypeFilter,
    fileTypeFilter,
    statusFilter,
    isMineFilter,
    currentSearchTerm,
    showToast,
  ]);

  // 初始加载和筛选条件变化时重新加载（不包括搜索词）
  useEffect(() => {
    loadResources();
  }, [loadResources]);

  const handleSearch = () => {
    setCurrentSearchTerm(searchTerm);
    setCurrentPage(1);
  };

  const handleBatchDelete = async () => {
    if (selectedResources.length === 0) {
      showToast("请选择要删除的资源", "error");
      return;
    }

    if (!confirm(`确定要删除选中的 ${selectedResources.length} 个资源吗？`)) {
      return;
    }

    try {
      const result = await batchDeleteAdminResources(selectedResources);
      if (result.success) {
        showToast(result.message, "success");
        setSelectedResources([]);
        loadResources();
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast("批量删除失败", "error");
    }
  };

  // 删除单个资源
  const handleDeleteResource = async (resourceId: number, title: string) => {
    if (!confirm(`确定要删除资源 "${title}" 吗？`)) {
      return;
    }

    try {
      const result = await deleteAdminResource(resourceId);
      if (result.success) {
        showToast(result.message, "success");
        loadResources();
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast("删除资源失败", "error");
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("zh-CN");
  };

  // 获取文件类型显示文本
  const getFileTypeText = (fileType: string) => {
    const typeMap: { [key: string]: string } = {
      video: "视频",
      audio: "音频",
      image: "图片",
      document: "文档",
      archive: "压缩包",
      application: "应用程序",
      other: "其他",
    };
    return typeMap[fileType] || fileType;
  };

  // 获取来源显示文本
  const getSourceText = (source: string) => {
    const sourceMap: { [key: string]: string } = {
      admin_upload: "管理员上传",
      user_submit: "用户提交",
    };
    return sourceMap[source] || source;
  };

  // 获取网盘类型显示文本
  const getPanTypeText = (panType: number) => {
    const panTypeMap: { [key: number]: string } = {
      1: "百度网盘",
      2: "夸克网盘",
      3: "阿里云盘",
      4: "迅雷网盘",
    };
    return panTypeMap[panType] || `类型${panType}`;
  };

  const filters: FilterField[] = [
    {
      key: "panType",
      label: "网盘类型",
      type: "select",
      value: panTypeFilter,
      onChange: setPanTypeFilter,
      options: [
        { label: "百度网盘", value: "1" },
        { label: "夸克网盘", value: "2" },
        { label: "阿里云盘", value: "3" },
        { label: "迅雷网盘", value: "4" },
      ],
    },
    {
      key: "fileType",
      label: "文件类型",
      type: "select",
      value: fileTypeFilter,
      onChange: setFileTypeFilter,
      options: [
        { label: "视频", value: "video" },
        { label: "音频", value: "audio" },
        { label: "图片", value: "image" },
        { label: "文档", value: "document" },
        { label: "压缩包", value: "archive" },
        { label: "应用程序", value: "application" },
        { label: "其他", value: "other" },
      ],
    },
    {
      key: "status",
      label: "验证状态",
      type: "select",
      value: statusFilter,
      onChange: setStatusFilter,
      options: [
        { label: "有效", value: "valid" },
        { label: "失效", value: "invalid" },
        { label: "未知", value: "unknown" },
      ],
    },
    {
      key: "isMine",
      label: "我的资源",
      type: "select",
      value: isMineFilter,
      onChange: setIsMineFilter,
      options: [
        { label: "是", value: "true" },
        { label: "否", value: "false" },
      ],
    },
    {
      key: "timeFilter",
      label: "时间筛选",
      type: "select",
      value: timeFilter,
      onChange: setTimeFilter,
      options: [
        { label: "全部时间", value: "all" },
        { label: "最近一周", value: "week" },
        { label: "最近半月", value: "half_month" },
        { label: "最近一月", value: "month" },
        { label: "最近半年", value: "half_year" },
        { label: "最近一年", value: "year" },
      ],
    },
  ];

  // 表格列配置
  const columns: Column<AdminResource>[] = [
    {
      key: "id",
      title: "ID",
      width: "80px",
      render: (id) => (
        <span className="text-sm text-gray-900 dark:text-gray-100 font-medium">
          {id}
        </span>
      ),
    },
    {
      key: "title",
      title: "资源标题",
      width: "250px",
      render: (title) => (
        <div className="max-w-xs">
          <span
            className="text-sm text-gray-900 dark:text-gray-100 truncate block"
            title={title}
          >
            {title}
          </span>
        </div>
      ),
    },
    {
      key: "author",
      title: "作者",
      width: "120px",
      render: (author) => (
        <span className="text-sm text-gray-900 dark:text-gray-100">
          {author || "未知"}
        </span>
      ),
    },
    {
      key: "text_content",
      title: "资源预览",
      width: "200px",
      render: (textContent) => (
        <div className="max-w-xs">
          <span
            className="text-sm text-gray-600 dark:text-gray-300 truncate block"
            title={textContent || "无预览内容"}
          >
            {textContent && textContent.length > 50
              ? `${textContent.substring(0, 50)}...`
              : textContent || "无预览内容"}
          </span>
        </div>
      ),
    },
    {
      key: "pan_type",
      title: "网盘类型",
      width: "100px",
      render: (panType) => (
        <span className="text-sm text-gray-900 dark:text-gray-100">
          {getPanTypeText(panType)}
        </span>
      ),
    },
    {
      key: "file_type",
      title: "文件类型",
      width: "100px",
      render: (fileType) => (
        <span className="text-sm text-gray-900 dark:text-gray-100">
          {fileType ? getFileTypeText(fileType) : "未知"}
        </span>
      ),
    },
    {
      key: "source",
      title: "来源",
      width: "120px",
      render: (source) => (
        <span className="text-sm text-gray-900 dark:text-gray-100">
          {getSourceText(source)}
        </span>
      ),
    },
    {
      key: "updated_at",
      title: "更新时间",
      width: "140px",
      render: (date) => (
        <span className="text-sm text-gray-900 dark:text-gray-100">
          {formatDate(date)}
        </span>
      ),
    },
    {
      key: "actions",
      title: "操作",
      width: "120px",
      render: (_, record) => (
        <div className="flex items-center justify-center gap-2">
          <button
            type="button"
            onClick={() =>
              window.open(`/resources/${record.resource_key}`, "_blank")
            }
            className="p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50/80 rounded-lg transition-all duration-200 backdrop-blur-sm"
            title="查看资源"
          >
            <EyeIcon className="h-4 w-4" />
          </button>
          <button
            type="button"
            onClick={() => handleDeleteResource(record.id, record.title)}
            className="p-2 text-red-600 hover:text-red-700 hover:bg-red-50/80 rounded-lg transition-all duration-200 backdrop-blur-sm"
            title="删除资源"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      ),
    },
  ];

  return (
    <div>
      {/* 搜索和筛选 */}
      <SearchFilter
        searchValue={searchTerm}
        onSearchChange={setSearchTerm}
        onSearch={handleSearch}
        filters={filters}
        actions={
          <button
            type="button"
            onClick={handleBatchDelete}
            disabled={selectedResources.length === 0}
            className="px-4 py-2 text-sm font-medium bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500/50 transition-all duration-200 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            批量删除 ({selectedResources.length})
          </button>
        }
        className="mb-6"
      />

      {/* 资源列表 */}
      <DataTable
        columns={columns}
        data={resources}
        loading={loading}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: totalResources,
          onChange: setCurrentPage,
        }}
        rowSelection={{
          selectedRowKeys: selectedResources,
          onChange: (keys) =>
            setSelectedResources(keys.map((key) => Number(key))),
        }}
        emptyText="暂无资源数据"
      />
    </div>
  );
}
