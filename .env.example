# 环境变量配置示例文件
# 复制此文件为 .env.local 并根据需要修改配置

# API 基础地址
NEXT_PUBLIC_API_BASE_URL=https://your-api-domain.com

# 开发环境 API 代理目标（仅在开发环境使用）
API_PROXY_TARGET=http://127.0.0.1:9999

# ===== 用户认证功能配置 =====

# 是否启用用户注册功能
# true: 启用注册功能
# false: 禁用注册功能（默认）
NEXT_PUBLIC_ENABLE_REGISTRATION=false

# 是否在导航栏显示注册按钮
# true: 显示注册按钮
# false: 隐藏注册按钮（默认）
NEXT_PUBLIC_SHOW_REGISTRATION_IN_NAV=false

# 是否在登录页面显示注册链接
# true: 显示注册链接
# false: 隐藏注册链接（默认）
NEXT_PUBLIC_SHOW_REGISTRATION_IN_LOGIN=false

# 是否在导航栏显示登录按钮
# true: 显示登录按钮（默认）
# false: 隐藏登录按钮
NEXT_PUBLIC_SHOW_LOGIN_IN_NAV=true

# ===== 链接处理配置 =====

# 是否启用 get_share 接口调用
# true: 调用 get_share 接口获取最新链接（默认）
# false: 仅调用 check_resource_status 接口，直接使用返回的 share_url
NEXT_PUBLIC_ENABLE_GET_SHARE_API=true

# 指定哪些网盘类型使用 get_share 接口
# 格式：用逗号分隔的网盘类型列表
# 可选值：baidu, quark, thunder, aliyun
# 默认：baidu,quark（百度网盘和夸克网盘使用 get_share 接口）
NEXT_PUBLIC_GET_SHARE_TYPES=baidu,quark

# ===== 配置说明 =====
#
# === 用户认证功能配置示例 ===
#
# 1. 完全启用注册功能（包括导航栏和登录页面的注册链接）：
#    NEXT_PUBLIC_ENABLE_REGISTRATION=true
#    NEXT_PUBLIC_SHOW_REGISTRATION_IN_NAV=true
#    NEXT_PUBLIC_SHOW_REGISTRATION_IN_LOGIN=true
#    NEXT_PUBLIC_SHOW_LOGIN_IN_NAV=true
#
# 2. 启用注册功能但隐藏导航栏注册按钮（只在登录页面显示）：
#    NEXT_PUBLIC_ENABLE_REGISTRATION=true
#    NEXT_PUBLIC_SHOW_REGISTRATION_IN_NAV=false
#    NEXT_PUBLIC_SHOW_REGISTRATION_IN_LOGIN=true
#    NEXT_PUBLIC_SHOW_LOGIN_IN_NAV=true
#
# 3. 完全禁用注册功能（默认配置）：
#    NEXT_PUBLIC_ENABLE_REGISTRATION=false
#    NEXT_PUBLIC_SHOW_REGISTRATION_IN_NAV=false
#    NEXT_PUBLIC_SHOW_REGISTRATION_IN_LOGIN=false
#    NEXT_PUBLIC_SHOW_LOGIN_IN_NAV=true
#
# 4. 隐藏所有认证按钮（登录和注册都不显示）：
#    NEXT_PUBLIC_ENABLE_REGISTRATION=false
#    NEXT_PUBLIC_SHOW_REGISTRATION_IN_NAV=false
#    NEXT_PUBLIC_SHOW_REGISTRATION_IN_LOGIN=false
#    NEXT_PUBLIC_SHOW_LOGIN_IN_NAV=false
#
# === 链接处理配置示例 ===
#
# 1. 完全禁用 get_share 接口（所有网盘类型都只调用 check_resource_status）：
#    NEXT_PUBLIC_ENABLE_GET_SHARE_API=false
#
# 2. 只有百度网盘使用 get_share 接口：
#    NEXT_PUBLIC_ENABLE_GET_SHARE_API=true
#    NEXT_PUBLIC_GET_SHARE_TYPES=baidu
#
# 3. 所有网盘类型都使用 get_share 接口：
#    NEXT_PUBLIC_ENABLE_GET_SHARE_API=true
#    NEXT_PUBLIC_GET_SHARE_TYPES=baidu,quark,thunder,aliyun
#
# 4. 完全禁用 get_share 接口，所有按钮都直接使用 check_resource_status 返回的链接：
#    NEXT_PUBLIC_ENABLE_GET_SHARE_API=false
#    # NEXT_PUBLIC_GET_SHARE_TYPES 在此情况下会被忽略
