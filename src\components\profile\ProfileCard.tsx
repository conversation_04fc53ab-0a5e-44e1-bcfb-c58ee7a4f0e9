"use client";

import React, { useState, useEffect } from "react";
import { UserProfile } from "@/services/profileService";
import { getMyProfile } from "@/services/profileService";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/Button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  User,
  Mail,
  Calendar,
  Award,
  Settings,
  CheckCircle,
  XCircle,
} from "lucide-react";

interface ProfileCardProps {
  onEditClick?: () => void;
  showEditButton?: boolean;
}

export function ProfileCard({
  onEditClick,
  showEditButton = true,
}: ProfileCardProps) {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await getMyProfile();

      if (result.success && result.data) {
        setProfile(result.data);
      } else {
        setError(result.message || "获取个人信息失败");
      }
    } catch {
      setError("网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800";
      case "inactive":
        return "bg-gray-100 text-gray-800";
      case "suspended":
        return "bg-red-100 text-red-800";
      default:
        return "bg-blue-100 text-blue-800";
    }
  };

  const getStatusText = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "正常";
      case "inactive":
        return "未激活";
      case "suspended":
        return "已暂停";
      default:
        return status;
    }
  };

  if (loading) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <div className="flex items-center space-x-4">
            <Skeleton className="h-16 w-16 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-4 w-48" />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="pt-6">
          <div className="text-center">
            <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={loadProfile} variant="outline">
              重新加载
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!profile) {
    return null;
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Avatar className="h-16 w-16">
              <AvatarImage
                src={profile.avatar || undefined}
                alt={profile.username || "用户头像"}
              />
              <AvatarFallback>
                {profile.nickname?.charAt(0) ||
                  profile.username?.charAt(0)?.toUpperCase() ||
                  "?"}
              </AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-xl">
                {profile.nickname || profile.username || "未知用户"}
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                @{profile.username || "unknown"}
              </p>
              <div className="flex items-center mt-2 space-x-2">
                <Badge className={getStatusColor(profile.status || "unknown")}>
                  {getStatusText(profile.status || "unknown")}
                </Badge>
                {profile.email_verified ? (
                  <Badge variant="outline" className="text-green-600">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    已验证
                  </Badge>
                ) : (
                  <Badge variant="outline" className="text-orange-600">
                    <XCircle className="h-3 w-3 mr-1" />
                    未验证
                  </Badge>
                )}
              </div>
            </div>
          </div>
          {showEditButton && (
            <Button onClick={onEditClick} variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              编辑资料
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{profile.email || "未设置邮箱"}</span>
            </div>

            <div className="flex items-center space-x-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                {profile.role?.display_name || profile.role?.name || "未知角色"}
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <Award className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{profile.title || "无等级"}</span>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                注册于{" "}
                {profile.created_at ? formatDate(profile.created_at) : "未知"}
              </span>
            </div>

            {profile.last_login_at && (
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  最后登录 {formatDate(profile.last_login_at)}
                </span>
              </div>
            )}

            <div className="flex items-center space-x-2">
              <Award className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">
                {profile.points || 0} 积分
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
