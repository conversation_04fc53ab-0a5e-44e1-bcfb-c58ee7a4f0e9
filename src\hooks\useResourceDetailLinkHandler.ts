import { useCallback, useRef } from "react";
import { useLoadingStore } from "@/store/loadingStore";
import { useToast } from "@/components/ToastProvider";
import { processCloudLink, openLinkInNewTab } from "@/utils/cloudLinkUtils";
import { useCloudLinkState } from "./useCloudLinkState";
import { ResourceDetail } from "@/types/resource";

type CloudType = "baidu" | "quark" | "thunder" | "aliyun";

interface UseResourceDetailLinkHandlerProps {
  resource: ResourceDetail | null;
  copyToClipboard: (text: string, message: string) => void;
  linkStatus?: {
    valid: boolean;
    message: string;
    checking: boolean;
  };
}

/**
 * 资源详情界面专用的链接处理Hook
 * 提供与资源卡片一致的缓存机制和错误处理
 */
export const useResourceDetailLinkHandler = ({
  resource,
  copyToClipboard,
  linkStatus,
}: UseResourceDetailLinkHandlerProps) => {
  const {
    updatedLinks,
    linkUpdated,
    loadingLinks,
    resourceStatus,
    updateLinkState,
    setLoadingState,
    setErrorState,
  } = useCloudLinkState();

  const { setLoading } = useLoadingStore();
  const { showToast } = useToast();

  // 防重复点击的引用
  const lastClickTime = useRef<{ [key: string]: number }>({});

  // 获取网盘类型平台映射
  const getPanTypePlatform = useCallback((panType: number): CloudType => {
    switch (panType) {
      case 1:
        return "baidu";
      case 2:
        return "quark";
      case 3:
        return "aliyun";
      case 4:
        return "thunder";
      default:
        return "baidu";
    }
  }, []);

  // 获取原始链接（资源详情界面没有原始链接，使用空字符串）
  const getOriginalLink = useCallback((): string => {
    return "";
  }, []);

  // 处理链接操作（打开或复制）
  const handleLinkAction = useCallback(
    async (action: "copy" | "open") => {
      if (!resource) return;

      // 如果提供了linkStatus且资源无效，则不执行操作
      if (linkStatus && !linkStatus.valid) {
        showToast("资源状态无效，无法执行操作", "error");
        return;
      }

      const platform = getPanTypePlatform(resource.pan_type);
      const resourceKey = resource.resource_key;

      // 防重复点击检查 - 只在缓存链接情况下需要
      if (linkUpdated[platform]) {
        const now = Date.now();
        const clickKey = `${action}_${resourceKey}`;
        if (
          lastClickTime.current[clickKey] &&
          now - lastClickTime.current[clickKey] < 1000
        ) {
          showToast("请勿重复点击", "info");
          return;
        }
        lastClickTime.current[clickKey] = now;
      }

      // 如果已经有错误，直接阻止操作
      if (resourceStatus[platform].error) {
        showToast(resourceStatus[platform].message || "资源不可用", "error");
        return;
      }

      // 如果链接已经更新，使用更新后的链接
      if (linkUpdated[platform]) {
        const linkKey = `${platform}Link` as
          | "baiduLink"
          | "quarkLink"
          | "thunderLink"
          | "aliyunLink";
        const updatedLink = updatedLinks[linkKey];
        if (updatedLink) {
          if (action === "copy") {
            copyToClipboard(updatedLink, "链接已复制到粘贴板");
          } else {
            openLinkInNewTab(updatedLink, showToast);
          }
        } else {
          showToast("链接无效", "error");
        }
        return;
      }

      // 开始加载
      setLoadingState(platform, true);
      setLoading(true, resourceKey);

      try {
        const result = await processCloudLink({
          type: platform,
          idToUse: resourceKey,
          searchType: "local", // 资源详情界面默认使用local
        });

        if (result.success && result.url) {
          // 检查是否需要更新链接
          const originalLink = getOriginalLink();
          updateLinkState(platform, result.url, originalLink);

          if (action === "copy") {
            copyToClipboard(result.url, "链接已复制到粘贴板");
          } else {
            openLinkInNewTab(result.url, showToast);
          }
        } else {
          setErrorState(platform, result.error || "处理链接出错");
          showToast(result.error || "处理链接出错", "error");
        }
      } catch (error: any) {
        const errorMessage = error?.message || "处理链接出错";
        setErrorState(platform, errorMessage);
        showToast(errorMessage, "error");
      } finally {
        setLoadingState(platform, false);
        setLoading(false);
      }
    },
    [
      resource,
      linkStatus,
      linkUpdated,
      resourceStatus,
      updatedLinks,
      getPanTypePlatform,
      getOriginalLink,
      updateLinkState,
      setLoadingState,
      setErrorState,
      setLoading,
      showToast,
      copyToClipboard,
    ]
  );

  // 获取当前资源的加载状态
  const isLinkLoading = resource
    ? loadingLinks[getPanTypePlatform(resource.pan_type)]
    : false;

  // 获取当前资源的错误状态
  const hasError = resource
    ? resourceStatus[getPanTypePlatform(resource.pan_type)].error
    : false;

  // 获取当前资源的错误消息
  const errorMessage = resource
    ? resourceStatus[getPanTypePlatform(resource.pan_type)].message
    : "";

  // 检查链接是否已更新
  const isLinkUpdated = resource
    ? linkUpdated[getPanTypePlatform(resource.pan_type)]
    : false;

  return {
    handleLinkAction,
    isLinkLoading,
    hasError,
    errorMessage,
    isLinkUpdated,
    // 暴露状态以便调试或其他用途
    updatedLinks,
    linkUpdated,
    loadingLinks,
    resourceStatus,
  };
};
