"use client";

import { useRouter } from "next/navigation";
import { useCallback } from "react";
import SearchBar from "@/components/SearchBar";
import { useSearch } from "@/hooks/useSearch";
import { SEARCH_CONFIG } from "@/config/constants";

export default function ResourceNotFound() {
  const router = useRouter();
  const { searchType } = useSearch({
    initialType: SEARCH_CONFIG.defaultType,
  });

  const handleSearch = useCallback(
    (query: string, type: "local" | "online") => {
      if (query.trim()) {
        router.push(`/search?q=${encodeURIComponent(query)}&type=${type}`);
      }
    },
    [router]
  );

  return (
    <div className="dark:bg-gray-950 min-h-screen p-4 md:p-8">
      <div className="w-full max-w-2xl px-4 my-8 mx-auto">
        <SearchBar
          onSearch={handleSearch}
          placeholder="换个关键词试试"
          initialSearchType={searchType}
        />
      </div>
      <div className="text-center text-gray-500">资源不存在或已被删除。</div>
    </div>
  );
}
