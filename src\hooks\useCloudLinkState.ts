import { useState } from "react";

type CloudType = "baidu" | "quark" | "thunder" | "aliyun";

interface UpdatedLinks {
  baiduLink: string;
  quarkLink: string;
  thunderLink: string;
  aliyunLink: string;
}

interface LoadingLinks {
  baidu: boolean;
  quark: boolean;
  thunder: boolean;
  aliyun: boolean;
}

interface ResourceStatus {
  baidu: { error: boolean; message: string };
  quark: { error: boolean; message: string };
  thunder: { error: boolean; message: string };
  aliyun: { error: boolean; message: string };
}

interface LinkUpdated {
  baidu: boolean;
  quark: boolean;
  thunder: boolean;
  aliyun: boolean;
}

/**
 * 自定义Hook：云盘链接状态管理
 * 管理云盘链接相关的所有状态
 */
export const useCloudLinkState = () => {
  const [updatedLinks, setUpdatedLinks] = useState<UpdatedLinks>({
    baiduLink: "",
    quarkLink: "",
    thunderLink: "",
    aliyunLink: "",
  });

  const [linkUpdated, setLinkUpdated] = useState<LinkUpdated>({
    baidu: false,
    quark: false,
    thunder: false,
    aliyun: false,
  });

  const [loadingLinks, setLoadingLinks] = useState<LoadingLinks>({
    baidu: false,
    quark: false,
    thunder: false,
    aliyun: false,
  });

  const [resourceStatus, setResourceStatus] = useState<ResourceStatus>({
    baidu: { error: false, message: "" },
    quark: { error: false, message: "" },
    thunder: { error: false, message: "" },
    aliyun: { error: false, message: "" },
  });

  // 更新链接状态
  const updateLinkState = (
    type: CloudType,
    url: string,
    originalUrl: string
  ) => {
    if (url !== originalUrl) {
      const linkKey = `${type}Link` as keyof UpdatedLinks;
      setUpdatedLinks((prev) => ({
        ...prev,
        [linkKey]: url,
      }));
      setLinkUpdated((prev) => ({ ...prev, [type]: true }));
    }
  };

  // 设置加载状态
  const setLoadingState = (type: CloudType, loading: boolean) => {
    setLoadingLinks((prev) => ({ ...prev, [type]: loading }));
  };

  // 设置错误状态
  const setErrorState = (type: CloudType, error: string) => {
    setResourceStatus((prev) => ({
      ...prev,
      [type]: { ...prev[type], error: true, message: error },
    }));
  };

  return {
    updatedLinks,
    linkUpdated,
    loadingLinks,
    resourceStatus,
    updateLinkState,
    setLoadingState,
    setErrorState,
  };
};
