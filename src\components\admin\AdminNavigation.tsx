"use client";

import { useState } from "react";
import Link from "next/link";
import { useTheme } from "next-themes";
import { useMounted } from "@/hooks/use-mounted";
import { useAuth } from "@/hooks/useAuth";
import {
  SunIcon,
  MoonIcon,
  UserIcon,
  ArrowRightOnRectangleIcon,
} from "@heroicons/react/24/outline";

/**
 * 管理后台专用导航栏组件
 * 完全独立于主站Navigation组件，避免事件冲突
 */
export default function AdminNavigation() {
  const [showUserMenu, setShowUserMenu] = useState(false);
  const { theme, setTheme } = useTheme();
  const mounted = useMounted();
  const { user, logout } = useAuth();

  // 主题切换函数 - 管理后台专用，无需复杂的保护机制
  const toggleTheme = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    setTheme(theme === "dark" ? "light" : "dark");
  };

  // 用户菜单切换
  const toggleUserMenu = () => {
    setShowUserMenu(!showUserMenu);
  };

  // 登出处理
  const handleLogout = async () => {
    await logout();
    setShowUserMenu(false);
  };

  if (!mounted) {
    return null;
  }

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-card-background border-b border-border-color">
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-12 md:h-14">
          {/* 左侧品牌标识 */}
          <div className="flex items-center">
            <Link
              href="/admin"
              className="text-lg md:text-xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 transition-all duration-200"
            >
              97盘搜 · 管理后台
            </Link>
          </div>

          {/* 右侧操作区域 */}
          <div className="flex items-center space-x-2">
            {/* 主题切换按钮 */}
            <button
              type="button"
              onClick={toggleTheme}
              className="p-2 rounded-full hover:bg-hover-background focus:outline-none transition-colors"
              aria-label="切换主题模式"
            >
              {theme === "dark" ? (
                <SunIcon className="h-5 w-5 text-foreground" />
              ) : (
                <MoonIcon className="h-5 w-5 text-foreground" />
              )}
            </button>

            {/* 用户菜单 */}
            <div className="relative">
              <button
                type="button"
                onClick={toggleUserMenu}
                className="flex items-center p-2 rounded-full hover:bg-hover-background focus:outline-none transition-colors"
                aria-label="用户菜单"
              >
                <UserIcon className="h-5 w-5 text-foreground" />
              </button>

              {/* 用户菜单下拉 */}
              {showUserMenu && (
                <>
                  {/* 背景遮罩 */}
                  <div
                    className="fixed inset-0 z-10"
                    onClick={() => setShowUserMenu(false)}
                  />

                  {/* 菜单内容 */}
                  <div className="absolute right-0 mt-2 w-48 bg-card-background border border-border-color rounded-md shadow-lg z-20">
                    <div className="py-1">
                      {/* 用户信息 */}
                      <div className="px-4 py-2 border-b border-border-color">
                        <p className="text-sm font-medium text-foreground">
                          {user?.username || "管理员"}
                        </p>
                        <p className="text-xs text-secondary-text">
                          {user?.email || "<EMAIL>"}
                        </p>
                      </div>

                      {/* 菜单项 */}
                      <Link
                        href="/"
                        className="block px-4 py-2 text-sm text-foreground hover:bg-hover-background transition-colors"
                        onClick={() => setShowUserMenu(false)}
                      >
                        返回主站
                      </Link>

                      <button
                        type="button"
                        onClick={handleLogout}
                        className="w-full text-left px-4 py-2 text-sm text-foreground hover:bg-hover-background transition-colors flex items-center"
                      >
                        <ArrowRightOnRectangleIcon className="h-4 w-4 mr-2" />
                        退出登录
                      </button>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}
