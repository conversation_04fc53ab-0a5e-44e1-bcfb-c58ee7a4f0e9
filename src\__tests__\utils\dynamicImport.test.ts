/**
 * 动态导入工具函数测试
 */

import { 
  dynamicImportWithRetry, 
  safeDynamicImport, 
  isChunkLoadError,
  handleChunkLoadError 
} from '@/utils/dynamicImport';

// Mock console methods
const mockConsoleWarn = jest.spyOn(console, 'warn').mockImplementation();
const mockConsoleError = jest.spyOn(console, 'error').mockImplementation();

describe('dynamicImport utils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterAll(() => {
    mockConsoleWarn.mockRestore();
    mockConsoleError.mockRestore();
  });

  describe('isChunkLoadError', () => {
    it('should identify ChunkLoadError correctly', () => {
      const chunkError = new Error('Loading chunk 123 failed');
      chunkError.name = 'ChunkLoadError';
      
      expect(isChunkLoadError(chunkError)).toBe(true);
    });

    it('should identify chunk loading errors by message', () => {
      const error1 = new Error('Loading chunk 456 failed');
      const error2 = new Error('ChunkLoadError: Failed to load chunk');
      
      expect(isChunkLoadError(error1)).toBe(true);
      expect(isChunkLoadError(error2)).toBe(true);
    });

    it('should return false for non-chunk errors', () => {
      const normalError = new Error('Some other error');
      
      expect(isChunkLoadError(normalError)).toBe(false);
    });
  });

  describe('dynamicImportWithRetry', () => {
    it('should succeed on first try', async () => {
      const mockImport = jest.fn().mockResolvedValue({ test: 'success' });
      
      const result = await dynamicImportWithRetry(mockImport);
      
      expect(result).toEqual({ test: 'success' });
      expect(mockImport).toHaveBeenCalledTimes(1);
    });

    it('should retry on failure and eventually succeed', async () => {
      const mockImport = jest.fn()
        .mockRejectedValueOnce(new Error('First failure'))
        .mockRejectedValueOnce(new Error('Second failure'))
        .mockResolvedValue({ test: 'success' });
      
      const result = await dynamicImportWithRetry(mockImport, { retries: 3 });
      
      expect(result).toEqual({ test: 'success' });
      expect(mockImport).toHaveBeenCalledTimes(3);
    });

    it('should throw error after max retries', async () => {
      const mockImport = jest.fn().mockRejectedValue(new Error('Persistent failure'));
      
      await expect(
        dynamicImportWithRetry(mockImport, { retries: 2 })
      ).rejects.toThrow('Persistent failure');
      
      expect(mockImport).toHaveBeenCalledTimes(2);
    });

    it('should call onError callback', async () => {
      const mockImport = jest.fn().mockRejectedValue(new Error('Test error'));
      const onError = jest.fn();
      
      await expect(
        dynamicImportWithRetry(mockImport, { retries: 1, onError })
      ).rejects.toThrow();
      
      expect(onError).toHaveBeenCalledWith(expect.any(Error), 1);
    });
  });

  describe('safeDynamicImport', () => {
    it('should return result on success', async () => {
      const mockImport = jest.fn().mockResolvedValue({ test: 'success' });
      
      const result = await safeDynamicImport(mockImport);
      
      expect(result).toEqual({ test: 'success' });
    });

    it('should return null on failure', async () => {
      const mockImport = jest.fn().mockRejectedValue(new Error('Test error'));
      
      const result = await safeDynamicImport(mockImport);
      
      expect(result).toBeNull();
      expect(mockConsoleWarn).toHaveBeenCalledWith('动态导入失败:', expect.any(Error));
    });
  });

  describe('handleChunkLoadError', () => {
    it('should handle chunk load errors with fallback', async () => {
      const chunkError = new Error('Loading chunk 123 failed');
      chunkError.name = 'ChunkLoadError';
      
      const mockImport = jest.fn().mockRejectedValue(chunkError);
      const fallback = jest.fn();
      
      await handleChunkLoadError(mockImport, fallback);
      
      expect(fallback).toHaveBeenCalled();
      expect(mockConsoleError).toHaveBeenCalledWith(
        'Chunk加载最终失败，可能需要刷新页面:',
        chunkError
      );
    });

    it('should throw non-chunk errors', async () => {
      const normalError = new Error('Normal error');
      const mockImport = jest.fn().mockRejectedValue(normalError);
      
      await expect(
        handleChunkLoadError(mockImport)
      ).rejects.toThrow('Normal error');
    });
  });
});
