import { useState, useEffect } from "react";
import { useToast } from "@/components/ToastProvider";

interface UseResourceFeedbackProps {
  onDelete?: () => void;
}

/**
 * 自定义Hook：资源反馈管理
 * 管理资源失效反馈相关的状态和逻辑
 */
export const useResourceFeedback = ({ onDelete }: UseResourceFeedbackProps) => {
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);
  const { showToast } = useToast();

  // 处理资源失效反馈
  const handleReportInvalid = () => {
    setShowFeedbackModal(true);
  };

  // 关闭弹窗
  const closeModal = () => {
    setShowFeedbackModal(false);
  };

  // 反馈结果回调
  const handleFeedbackResult = (
    status: string,
    message: string,
    is_deleted?: boolean
  ) => {
    if (status === "success") {
      showToast(message, "success");
    } else {
      showToast(message || "反馈失败", "error");
    }
    // 如果资源被删除，先触发动画，动画结束后再通知父组件
    if (is_deleted) {
      setIsRemoving(true);
      setTimeout(() => {
        if (onDelete) {
          onDelete();
        }
      }, 400); // 动画时长400ms
    }
  };

  // 监听iframe消息
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data && event.data.type === "resource-report-submitted") {
        if (event.data.success) {
          setShowFeedbackModal(false);
        }
      }
    };

    window.addEventListener("message", handleMessage);
    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, []);

  // 获取网盘类型映射
  const getPanTypeMap = (type: string): number => {
    const panTypeMap: Record<string, number> = {
      百度网盘: 1,
      baidu: 1,
      BAIDU: 1,
      夸克网盘: 2,
      quark: 2,
      QUARK: 2,
      阿里云盘: 3,
      aliyun: 3,
      ALIYUN: 3,
      迅雷网盘: 4,
      thunder: 4,
      THUNDER: 4,
    };
    const normalizedType = (type || "").trim();
    return panTypeMap[normalizedType] ?? 2;
  };

  return {
    showFeedbackModal,
    isRemoving,
    handleReportInvalid,
    closeModal,
    handleFeedbackResult,
    getPanTypeMap,
  };
};
