'use client';

import { useState } from 'react';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { SEARCH_CONFIG } from '@/config/constants';

interface SearchBarProps {
    onSearch: (query: string, searchType: 'local' | 'online') => void;
    placeholder?: string;
    initialValue?: string;
    initialSearchType?: 'local' | 'online';
}

export default function SearchBar({
    onSearch,
    placeholder = '搜索资源...',
    initialValue = '',
    initialSearchType = SEARCH_CONFIG.defaultType
}: SearchBarProps) {
    const [query, setQuery] = useState(initialValue);
    const [searchType, setSearchType] = useState<'local' | 'online'>(initialSearchType);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSearch(query, searchType);
    };

    return (
        <form onSubmit={handleSubmit} className="relative w-full max-w-2xl mx-auto">
            <div className="relative shadow-lg rounded-full">
                <Input
                    type="text"
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    placeholder={placeholder}
                    className="w-full py-3.5 px-5 pr-16 rounded-full"
                />
                <Button
                    type="submit"
                    className="absolute right-0 top-0 h-full px-5 rounded-r-full"
                    aria-label="搜索"
                >
                    <MagnifyingGlassIcon className="h-5 w-5" />
                </Button>
            </div>

            <div className="flex justify-center mt-3 mb-2">
                <div className="flex rounded-full overflow-hidden border border-blue-100 dark:border-[#4b4d61] dark:border-opacity-80 shadow-sm bg-white dark:bg-[#242428] relative">
                    <Button
                        type="button"
                        onClick={() => setSearchType('local')}
                        variant={searchType === 'local' ? 'default' : 'ghost'}
                        size="sm"
                        rounded="none"
                        className={`px-4 py-1.5 ${searchType === 'local'
                            ? 'bg-gradient-to-r from-blue-600 to-blue-500 text-white shadow-sm dark:from-blue-500 dark:to-blue-400'
                            : 'bg-white dark:bg-[#2c2c34] text-secondary-text dark:text-[#b0b3b8]'}`}
                    >
                        本地
                    </Button>
                    <div className="w-px h-full dark:bg-[#4b4d61] bg-blue-100 absolute left-1/2 transform -translate-x-1/2"></div>
                    <Button
                        type="button"
                        onClick={() => setSearchType('online')}
                        variant={searchType === 'online' ? 'default' : 'ghost'}
                        size="sm"
                        rounded="none"
                        className={`px-4 py-1.5 ${searchType === 'online'
                            ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-sm dark:from-purple-500 dark:to-blue-500'
                            : 'bg-white dark:bg-[#2c2c34] text-secondary-text dark:text-[#b0b3b8]'}`}
                    >
                        联网
                    </Button>
                </div>
            </div>

            <div className="text-xs text-secondary-text text-center">
                {searchType === 'local' ? '本地模式：优先搜索已缓存资源' : '联网模式：实时搜索网盘资源'}
            </div>
        </form>
    );
} 