"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { ArrowLeftIcon, CheckCircleIcon } from "@heroicons/react/24/outline";
import { useToast } from "@/components/ToastProvider";
import { getHelpRequestDetail } from "@/services/helpRequestService";
import {
  type HelpRequestDetail,
  type HelpRequestAnswer,
} from "@/types/help-request";

// 格式化时间
const formatDateTime = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

// 回答卡片组件 - 完全按照参考图片设计
function AnswerCard({ answer }: { answer: HelpRequestAnswer }) {
  return (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
      <div className="p-4">
        {/* 资源标题 */}
        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 line-clamp-1">
          资源标题：{answer.resource_title}
        </h4>

        {/* 网盘类型标签 */}
        <div className="flex items-center space-x-1.5 mb-4">
          <span className="px-1.5 py-0.5 bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-300 rounded text-[10px] font-medium border border-green-200 dark:border-green-800">
            {answer.cloud_disk_type === "baidu"
              ? "百度网盘"
              : answer.cloud_disk_type === "aliyun"
              ? "阿里云盘"
              : answer.cloud_disk_type === "quark"
              ? "夸克网盘"
              : answer.cloud_disk_type === "xunlei"
              ? "迅雷网盘"
              : answer.cloud_disk_type}
          </span>
        </div>

        {/* 用户信息和统计信息行 */}
        <div className="flex items-start gap-4 mb-4">
          {/* 用户头像和昵称 */}
          <div className="flex-shrink-0 flex flex-col items-center">
            <div className="w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-lg"></div>
            <div className="mt-1 text-[10px] text-gray-500 dark:text-gray-400 text-center max-w-[50px] truncate leading-tight">
              {answer.answerer.title ||
                answer.answerer.nickname ||
                answer.answerer.username}
            </div>
          </div>

          {/* 时间和统计信息 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
              <span>{formatDateTime(answer.created_at)}</span>
              <div className="flex items-center gap-2">
                <span className="text-[10px]">
                  {answer.answerer.points} 积分
                </span>
                {answer.is_accepted && (
                  <div className="flex items-center space-x-1 bg-green-50 dark:bg-green-900/20 px-1.5 py-0.5 rounded text-[10px] font-medium border border-green-200 dark:border-green-800">
                    <CheckCircleIcon className="w-3 h-3 text-green-600 dark:text-green-400" />
                    <span className="text-green-700 dark:text-green-300">
                      已采纳
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 资源链接 */}
        <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-3 border border-gray-200 dark:border-gray-700 mb-3">
          <p className="text-xs text-gray-700 dark:text-gray-300 break-all font-mono">
            {answer.resource_link}
          </p>
        </div>

        {/* 附加信息 */}
        {answer.additional_info && (
          <div className="border-t border-gray-100 dark:border-gray-700 pt-3">
            <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-2 text-sm">
              附加信息：
            </h5>
            <p className="text-xs text-gray-700 dark:text-gray-300 leading-relaxed">
              {answer.additional_info}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

export default function HelpRequestDetailPage() {
  const params = useParams();
  const { showToast } = useToast();
  const [helpRequest, setHelpRequest] = useState<HelpRequestDetail | null>(
    null
  );
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadHelpRequestDetail = async () => {
      if (!params?.id) return;

      setLoading(true);
      try {
        const response = await getHelpRequestDetail(params.id as string);
        if (response.status === "success") {
          setHelpRequest(response.data);
        } else {
          showToast(response.message || "获取求助详情失败", "error");
        }
      } catch (error) {
        console.error("获取求助详情失败:", error);
        showToast("获取求助详情失败", "error");
      } finally {
        setLoading(false);
      }
    };

    loadHelpRequestDetail();
  }, [params?.id, showToast]);

  // 设置页面标题
  useEffect(() => {
    if (helpRequest) {
      // 获取网盘类型文本
      const getDiskTypeText = (diskTypes: string[]) => {
        if (!diskTypes || diskTypes.length === 0) return "";

        const typeMap: { [key: string]: string } = {
          baidu: "百度网盘",
          aliyun: "阿里云盘",
          quark: "夸克网盘",
          xunlei: "迅雷网盘",
        };

        return diskTypes.map((type) => typeMap[type] || type).join("、");
      };

      const diskTypeText = getDiskTypeText(helpRequest.cloud_disk_types || []);
      const title = diskTypeText
        ? `${helpRequest.title} - ${diskTypeText}`
        : helpRequest.title;

      document.title = title;

      // 组件卸载时恢复默认标题
      return () => {
        document.title = "盘搜 - 资源求助平台";
      };
    }
  }, [helpRequest]);

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto p-4">
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-secondary-text">加载中...</span>
        </div>
      </div>
    );
  }

  if (!helpRequest) {
    return (
      <div className="max-w-4xl mx-auto p-4">
        <div className="text-center py-12">
          <p className="text-secondary-text mb-4">求助不存在或已被删除</p>
          <Link
            href="/help-requests"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            返回求助列表
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-6">
      {/* 求助详情卡片 - 完全按照参考图片设计 */}
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg mb-6">
        <div className="p-4">
          {/* 标题 */}
          <h1 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 line-clamp-1">
            {helpRequest.title}
          </h1>

          {/* 标签行 */}
          <div className="flex flex-wrap gap-1.5 mb-4">
            {helpRequest.resource_type && (
              <span className="px-1.5 py-0.5 bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300 rounded text-[10px] font-medium border border-blue-200 dark:border-blue-800">
                {helpRequest.resource_type === "movie"
                  ? "电影"
                  : helpRequest.resource_type === "tv"
                  ? "电视剧"
                  : helpRequest.resource_type === "music"
                  ? "音乐"
                  : helpRequest.resource_type === "software"
                  ? "软件"
                  : helpRequest.resource_type === "game"
                  ? "游戏"
                  : helpRequest.resource_type === "book"
                  ? "书籍"
                  : helpRequest.resource_type === "document"
                  ? "文档"
                  : helpRequest.resource_type === "other"
                  ? "其他"
                  : helpRequest.resource_type}
              </span>
            )}
            {helpRequest.cloud_disk_types?.map((diskType) => (
              <span
                key={diskType}
                className="px-1.5 py-0.5 bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-300 rounded text-[10px] font-medium border border-green-200 dark:border-green-800"
              >
                {diskType === "baidu"
                  ? "百度网盘"
                  : diskType === "aliyun"
                  ? "阿里云盘"
                  : diskType === "quark"
                  ? "夸克网盘"
                  : diskType === "xunlei"
                  ? "迅雷网盘"
                  : diskType}
              </span>
            ))}
          </div>

          {/* 用户信息和统计信息行 */}
          <div className="flex items-start gap-4">
            {/* 用户头像和昵称 */}
            <div className="flex-shrink-0 flex flex-col items-center">
              <div className="w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-lg"></div>
              <div className="mt-1 text-[10px] text-gray-500 dark:text-gray-400 text-center max-w-[50px] truncate leading-tight">
                {helpRequest.requester.title ||
                  helpRequest.requester.nickname ||
                  helpRequest.requester.username}
              </div>
            </div>

            {/* 时间和统计信息 */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                <span>{formatDateTime(helpRequest.created_at)}</span>
                <div className="flex items-center gap-3">
                  <span className="flex items-center gap-1">
                    <svg
                      className="w-3 h-3"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                      <path
                        fillRule="evenodd"
                        d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {helpRequest.view_count}
                  </span>
                  <span className="flex items-center gap-1">
                    <svg
                      className="w-3 h-3"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {helpRequest.answer_count}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* 描述 */}
          {helpRequest.description && (
            <div className="mt-4 pt-3 border-t border-gray-100 dark:border-gray-700">
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed text-sm">
                {helpRequest.description}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* 回答列表 */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          最新回复 ({helpRequest.answers.length})
        </h2>

        {helpRequest.answers.length > 0 ? (
          <div className="space-y-4">
            {helpRequest.answers.map((answer) => (
              <AnswerCard key={answer.id} answer={answer} />
            ))}
          </div>
        ) : (
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-8 text-center">
            <p className="text-gray-500 dark:text-gray-400 text-sm">暂无回答</p>
          </div>
        )}
      </div>
    </div>
  );
}
