import React from "react";
import { render, screen } from "@testing-library/react";
import { describe, it, expect } from "vitest";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";

describe("Avatar 组件", () => {
  it("应该正确渲染 Avatar 容器", () => {
    render(
      <Avatar data-testid="avatar">
        <AvatarFallback>JD</AvatarFallback>
      </Avatar>
    );

    const avatar = screen.getByTestId("avatar");
    expect(avatar).toBeInTheDocument();
    expect(avatar).toHaveClass(
      "relative",
      "flex",
      "h-10",
      "w-10",
      "shrink-0",
      "overflow-hidden",
      "rounded-full"
    );
  });

  it("应该正确渲染 AvatarImage", () => {
    render(
      <Avatar>
        <AvatarImage src="/test-avatar.jpg" alt="Test Avatar" />
        <AvatarFallback>JD</AvatarFallback>
      </Avatar>
    );

    const avatarImage = screen.getByAltText("Test Avatar");
    expect(avatarImage).toBeInTheDocument();
    expect(avatarImage).toHaveAttribute("src", "/test-avatar.jpg");
    expect(avatarImage).toHaveClass("aspect-square", "h-full", "w-full");
  });

  it("应该正确渲染 AvatarFallback", () => {
    render(
      <Avatar>
        <AvatarFallback>JD</AvatarFallback>
      </Avatar>
    );

    const fallback = screen.getByText("JD");
    expect(fallback).toBeInTheDocument();
    expect(fallback).toHaveClass(
      "flex",
      "h-full",
      "w-full",
      "items-center",
      "justify-center",
      "rounded-full",
      "bg-muted"
    );
  });

  it("应该支持自定义 className", () => {
    render(
      <Avatar className="custom-avatar" data-testid="avatar">
        <AvatarFallback className="custom-fallback">JD</AvatarFallback>
      </Avatar>
    );

    const avatar = screen.getByTestId("avatar");
    const fallback = screen.getByText("JD");

    expect(avatar).toHaveClass("custom-avatar");
    expect(fallback).toHaveClass("custom-fallback");
  });

  it("应该正确处理图片加载失败的情况", () => {
    render(
      <Avatar>
        <AvatarImage src="/non-existent.jpg" alt="Test Avatar" />
        <AvatarFallback>JD</AvatarFallback>
      </Avatar>
    );

    // 当图片加载失败时，应该显示 fallback
    const fallback = screen.getByText("JD");
    expect(fallback).toBeInTheDocument();
  });

  it("应该支持不同尺寸", () => {
    render(
      <Avatar className="h-16 w-16" data-testid="large-avatar">
        <AvatarFallback>JD</AvatarFallback>
      </Avatar>
    );

    const avatar = screen.getByTestId("large-avatar");
    expect(avatar).toHaveClass("h-16", "w-16");
  });
});
