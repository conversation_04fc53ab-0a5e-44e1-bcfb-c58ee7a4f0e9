#!/usr/bin/env node

/**
 * 公告配置更新脚本
 * 使用方法：
 * 1. 启用公告：node scripts/update-notification.js enable "公告标题" "公告内容" 2023-10-01 2024-12-31
 * 2. 禁用公告：node scripts/update-notification.js disable
 */

import fs from "fs";
import path from "path";
import yaml from "js-yaml";

// 配置文件路径
const CONFIG_PATH = path.join(__dirname, "../public/notification.yaml");

// 读取当前配置
const readConfig = () => {
  try {
    const fileContent = fs.readFileSync(CONFIG_PATH, "utf8");
    return yaml.load(fileContent);
  } catch (error) {
    console.error("读取配置失败:", error.message);
    return {
      enabled: false,
      title: "系统公告",
      content: "欢迎使用我们的网盘搜索服务！",
      start_time: "",
      end_time: "",
    };
  }
};

// 写入配置
const writeConfig = (config) => {
  try {
    const yamlStr = yaml.dump(config, {
      lineWidth: 120,
      indent: 2,
      noRefs: true,
      quotingType: '"',
    });

    // 添加注释
    const configWithComments = `# 网站公告配置

# 是否启用公告（true/false）
${yamlStr.replace(/enabled:/, "enabled:")}
# 公告标题
title: ${config.title}

# 公告内容（支持换行和简单格式）
content: |
${config.content
  .split("\n")
  .map((line) => `  ${line}`)
  .join("\n")}

# 公告显示时间（格式：YYYY-MM-DD，为空则永久有效）
start_time: ${config.start_time || ""}
end_time: ${config.end_time || ""}`;

    fs.writeFileSync(CONFIG_PATH, configWithComments, "utf8");
    console.log("配置已更新!");
  } catch (error) {
    console.error("写入配置失败:", error.message);
  }
};

// 主函数
const main = () => {
  const args = process.argv.slice(2);
  const command = args[0]?.toLowerCase();

  if (!command) {
    console.log("请提供命令: enable 或 disable");
    return;
  }

  const config = readConfig();

  if (command === "disable") {
    config.enabled = false;
    writeConfig(config);
    console.log("公告已禁用!");
    return;
  }

  if (command === "enable") {
    config.enabled = true;

    // 更新标题和内容（如果提供了）
    if (args[1]) config.title = args[1];
    if (args[2]) config.content = args[2];

    // 更新时间范围（如果提供了）
    if (args[3]) config.start_time = args[3];
    if (args[4]) config.end_time = args[4];

    writeConfig(config);
    console.log("公告已启用!");
    console.log(`标题: ${config.title}`);
    console.log(
      `内容: ${config.content.substring(0, 50)}${
        config.content.length > 50 ? "..." : ""
      }`
    );
    console.log(
      `有效期: ${config.start_time || "无起始日期"} 至 ${
        config.end_time || "无结束日期"
      }`
    );
    return;
  }

  console.log("未知命令! 请使用 enable 或 disable");
};

main();
