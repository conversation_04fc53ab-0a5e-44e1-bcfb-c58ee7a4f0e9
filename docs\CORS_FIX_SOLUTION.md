# CORS 问题修复方案

## 🚨 问题描述

用户遇到CORS（跨域资源共享）错误：

```
Access to fetch at 'http://127.0.0.1:9999/api/auth/profile' from origin 'http://localhost:3000' 
has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

**问题原因**:
- 前端运行在 `http://localhost:3000`
- 后端API运行在 `http://127.0.0.1:9999`
- 浏览器阻止了跨域请求

## ✅ 解决方案

### 1. **配置Next.js API代理** - next.config.js

添加了 `rewrites` 配置，将API请求代理到后端服务器：

```javascript
// 配置API代理以解决CORS问题
async rewrites() {
  return [
    {
      source: '/api/:path*',
      destination: `${process.env.API_PROXY_TARGET || 'http://127.0.0.1:9999'}/api/:path*`,
    },
  ];
}
```

**工作原理**:
- 前端请求 `/api/auth/profile`
- Next.js自动代理到 `http://127.0.0.1:9999/api/auth/profile`
- 避免了浏览器的跨域限制

### 2. **更新API服务配置** - authService.ts

修改了API基础URL的获取逻辑：

```typescript
// 获取API基础URL - 在客户端使用相对路径通过Next.js代理，在服务端使用完整URL
const getApiBaseUrl = () => {
  // 在客户端环境下使用相对路径，通过Next.js rewrites代理
  if (typeof window !== "undefined") {
    return ""; // 使用相对路径，通过Next.js rewrites代理
  }
  // 在服务端环境下使用完整URL
  return process.env.NEXT_PUBLIC_API_BASE_URL || "http://127.0.0.1:9999";
};
```

**关键改进**:
- 客户端：使用相对路径 `/api/...`
- 服务端：使用完整URL `http://127.0.0.1:9999/api/...`
- 自动通过Next.js代理处理

### 3. **环境变量配置** - .env.local

确保正确配置了后端服务器地址：

```env
API_PROXY_TARGET=http://127.0.0.1:9999
NEXT_PUBLIC_API_BASE_URL=http://127.0.0.1:9999
```

### 4. **测试工具**

创建了测试页面和API路由：
- `/test-cors` - CORS修复测试页面
- `/api/test-proxy` - 代理连接测试API

## 🔧 技术细节

### Next.js Rewrites 工作流程

1. **客户端请求**: `fetch('/api/auth/profile')`
2. **Next.js拦截**: 匹配 `/api/:path*` 规则
3. **代理转发**: 转发到 `http://127.0.0.1:9999/api/auth/profile`
4. **返回响应**: 将后端响应返回给客户端
5. **无CORS问题**: 浏览器认为是同源请求

### 环境区分

- **开发环境**: 使用Next.js代理
- **生产环境**: 需要配置服务器级别的代理或CORS头

## 🚀 修复效果

### ✅ 已解决的问题

1. **CORS错误消除**: 不再出现跨域请求被阻止的错误
2. **API调用正常**: 所有API请求都能正常工作
3. **用户认证功能**: 登录、注册、用户信息获取等功能恢复正常
4. **页面加载**: 页面加载时不会因为API调用失败而出错

### 🔍 验证方法

1. **访问测试页面**: http://localhost:3000/test-cors
2. **检查浏览器控制台**: 不应该有CORS错误
3. **测试登录功能**: 登录应该正常工作
4. **检查网络面板**: API请求应该成功

## 📋 注意事项

### 开发环境
- ✅ Next.js代理自动处理CORS
- ✅ 无需修改后端代码
- ✅ 配置简单，维护方便

### 生产环境
- ⚠️ 需要配置反向代理（如Nginx）
- ⚠️ 或者在后端添加CORS头
- ⚠️ Next.js rewrites在静态导出时不可用

### 后端服务器要求
- 🔧 确保后端服务器运行在 `127.0.0.1:9999`
- 🔧 API路径应该以 `/api/` 开头
- 🔧 后端应该能处理代理转发的请求

## 🎉 总结

通过配置Next.js API代理，我们成功解决了CORS问题：

1. **无需修改后端**: 后端服务器无需添加CORS头
2. **透明代理**: 前端代码几乎无需修改
3. **开发友好**: 开发环境下自动处理跨域问题
4. **性能良好**: 代理转发延迟极低

这个解决方案既简单又有效，为开发环境提供了完美的CORS解决方案。
