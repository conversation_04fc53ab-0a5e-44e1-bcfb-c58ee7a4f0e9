/**
 * 动态导入工具函数 - 处理ChunkLoadError和网络错误
 */

export interface DynamicImportOptions {
  retries?: number;
  retryDelay?: number;
  onError?: (error: Error, attempt: number) => void;
  onSuccess?: () => void;
}

/**
 * 带重试机制的动态导入
 * @param importFn 导入函数
 * @param options 配置选项
 * @returns Promise<T>
 */
export async function dynamicImportWithRetry<T>(
  importFn: () => Promise<T>,
  options: DynamicImportOptions = {}
): Promise<T> {
  const {
    retries = 3,
    retryDelay = 100,
    onError,
    onSuccess
  } = options;

  let lastError: Error;

  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const result = await importFn();
      onSuccess?.();
      return result;
    } catch (error) {
      lastError = error as Error;
      onError?.(lastError, attempt);

      // 如果是最后一次尝试，抛出错误
      if (attempt === retries) {
        throw lastError;
      }

      // 等待后重试，延迟时间递增
      await new Promise(resolve => 
        setTimeout(resolve, retryDelay * attempt)
      );
    }
  }

  throw lastError!;
}

/**
 * 安全的动态导入 - 不会抛出错误，只会记录警告
 * @param importFn 导入函数
 * @param options 配置选项
 * @returns Promise<T | null>
 */
export async function safeDynamicImport<T>(
  importFn: () => Promise<T>,
  options: DynamicImportOptions = {}
): Promise<T | null> {
  try {
    return await dynamicImportWithRetry(importFn, options);
  } catch (error) {
    console.warn('动态导入失败:', error);
    return null;
  }
}

/**
 * 检查是否为ChunkLoadError
 * @param error 错误对象
 * @returns boolean
 */
export function isChunkLoadError(error: Error): boolean {
  return error.name === 'ChunkLoadError' || 
         error.message.includes('Loading chunk') ||
         error.message.includes('ChunkLoadError');
}

/**
 * 处理ChunkLoadError的专用函数
 * @param importFn 导入函数
 * @param fallback 失败时的回调函数
 * @returns Promise<T | void>
 */
export async function handleChunkLoadError<T>(
  importFn: () => Promise<T>,
  fallback?: () => void
): Promise<T | void> {
  try {
    return await dynamicImportWithRetry(importFn, {
      retries: 5,
      retryDelay: 200,
      onError: (error, attempt) => {
        if (isChunkLoadError(error)) {
          console.warn(`Chunk加载失败 (尝试 ${attempt}/5):`, error.message);
        }
      }
    });
  } catch (error) {
    if (isChunkLoadError(error as Error)) {
      console.error('Chunk加载最终失败，可能需要刷新页面:', error);
      fallback?.();
    } else {
      throw error;
    }
  }
}
