"use client";

import { ReactNode, useState } from "react";
import {
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  ChevronDownIcon,
  ChevronUpIcon,
} from "@heroicons/react/24/outline";

export interface FilterOption {
  label: string;
  value: string;
}

export interface FilterField {
  key: string;
  label: string;
  type: "select" | "input" | "date";
  options?: FilterOption[];
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
}

export interface SearchFilterProps {
  searchValue: string;
  onSearchChange: (value: string) => void;
  onSearch: () => void;
  filters?: FilterField[];
  actions?: ReactNode;
  className?: string;
}

export default function SearchFilter({
  searchValue,
  onSearchChange,
  onSearch,
  filters = [],
  actions,
  className = "",
}: SearchFilterProps) {
  const [isFiltersExpanded, setIsFiltersExpanded] = useState(false);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      onSearch();
    }
  };

  // 检查是否有活跃的筛选条件
  const hasActiveFilters = filters.some((filter) => filter.value);

  return (
    <div
      className={`bg-white/80 backdrop-blur-xl border border-gray-200/50 rounded-xl shadow-sm ${className}`}
    >
      {/* 主搜索栏 - 始终可见 */}
      <div className="px-4 sm:px-6 py-4">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* 搜索框区域 */}
          <div className="flex-1 flex flex-col sm:flex-row gap-3">
            <div className="relative flex-1">
              <input
                type="text"
                placeholder="输入关键词搜索..."
                value={searchValue}
                onChange={(e) => onSearchChange(e.target.value)}
                onKeyPress={handleKeyPress}
                className="w-full px-4 py-2.5 text-sm border border-gray-300/60 bg-white/90 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500 transition-all duration-200 shadow-sm pr-10"
              />
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />
              </div>
            </div>

            {/* 搜索按钮 */}
            <button
              type="button"
              onClick={onSearch}
              className="px-4 py-2.5 text-sm font-medium bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500/50 transition-all duration-200 shadow-sm whitespace-nowrap"
            >
              搜索
            </button>
          </div>

          {/* 筛选器控制按钮 - 移动端 */}
          {filters.length > 0 && (
            <div className="flex items-center gap-3 sm:hidden">
              <button
                type="button"
                onClick={() => setIsFiltersExpanded(!isFiltersExpanded)}
                className={`flex items-center gap-2 px-3 py-2.5 text-sm font-medium rounded-lg border transition-all duration-200 ${
                  hasActiveFilters
                    ? "bg-blue-50 border-blue-200 text-blue-700"
                    : "bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100"
                }`}
              >
                <AdjustmentsHorizontalIcon className="h-4 w-4" />
                <span>筛选</span>
                {hasActiveFilters && (
                  <span className="inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-blue-600 rounded-full">
                    {filters.filter((f) => f.value).length}
                  </span>
                )}
                {isFiltersExpanded ? (
                  <ChevronUpIcon className="h-4 w-4" />
                ) : (
                  <ChevronDownIcon className="h-4 w-4" />
                )}
              </button>

              {/* 操作按钮 - 移动端 */}
              {actions && (
                <div className="flex items-center gap-2">{actions}</div>
              )}
            </div>
          )}

          {/* 操作按钮 - 桌面端 */}
          {actions && (
            <div className="hidden sm:flex items-center gap-2">{actions}</div>
          )}
        </div>
      </div>

      {/* 筛选条件区域 */}
      {filters.length > 0 && (
        <div
          className={`border-t border-gray-200/50 transition-all duration-300 overflow-hidden ${
            isFiltersExpanded ? "block sm:block" : "hidden sm:block"
          }`}
        >
          <div className="px-4 sm:px-6 py-4">
            {/* 桌面端 - 水平布局 */}
            <div className="hidden sm:flex sm:flex-wrap sm:items-center sm:gap-4">
              {filters.map((filter) => (
                <div
                  key={filter.key}
                  className="flex items-center gap-2 min-w-0"
                >
                  <label
                    htmlFor={`filter-${filter.key}`}
                    className="text-sm font-medium text-gray-700 whitespace-nowrap"
                  >
                    {filter.label}
                  </label>
                  {filter.type === "select" ? (
                    <select
                      id={`filter-${filter.key}`}
                      value={filter.value}
                      onChange={(e) => filter.onChange(e.target.value)}
                      className="min-w-[120px] px-3 py-2 text-sm border border-gray-300/60 bg-white/90 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500 transition-all duration-200 shadow-sm"
                      aria-label={filter.label}
                    >
                      <option value="">
                        {filter.placeholder || `全部${filter.label}`}
                      </option>
                      {filter.options?.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  ) : filter.type === "date" ? (
                    <input
                      id={`filter-${filter.key}`}
                      type="date"
                      value={filter.value}
                      onChange={(e) => filter.onChange(e.target.value)}
                      placeholder={filter.placeholder}
                      className="min-w-[140px] px-3 py-2 text-sm border border-gray-300/60 bg-white/90 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500 transition-all duration-200 shadow-sm"
                    />
                  ) : (
                    <input
                      id={`filter-${filter.key}`}
                      type="text"
                      value={filter.value}
                      onChange={(e) => filter.onChange(e.target.value)}
                      placeholder={filter.placeholder}
                      className="min-w-[120px] px-3 py-2 text-sm border border-gray-300/60 bg-white/90 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500 transition-all duration-200 shadow-sm"
                    />
                  )}
                </div>
              ))}
            </div>

            {/* 移动端 - 垂直布局 */}
            <div className="sm:hidden space-y-4">
              {filters.map((filter) => (
                <div key={filter.key} className="space-y-2">
                  <label
                    htmlFor={`mobile-filter-${filter.key}`}
                    className="block text-sm font-medium text-gray-700"
                  >
                    {filter.label}
                  </label>
                  {filter.type === "select" ? (
                    <select
                      id={`mobile-filter-${filter.key}`}
                      value={filter.value}
                      onChange={(e) => filter.onChange(e.target.value)}
                      className="w-full px-3 py-2.5 text-sm border border-gray-300/60 bg-white/90 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500 transition-all duration-200 shadow-sm"
                      aria-label={filter.label}
                    >
                      <option value="">
                        {filter.placeholder || `全部${filter.label}`}
                      </option>
                      {filter.options?.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  ) : filter.type === "date" ? (
                    <input
                      id={`mobile-filter-${filter.key}`}
                      type="date"
                      value={filter.value}
                      onChange={(e) => filter.onChange(e.target.value)}
                      placeholder={filter.placeholder}
                      className="w-full px-3 py-2.5 text-sm border border-gray-300/60 bg-white/90 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500 transition-all duration-200 shadow-sm"
                    />
                  ) : (
                    <input
                      id={`mobile-filter-${filter.key}`}
                      type="text"
                      value={filter.value}
                      onChange={(e) => filter.onChange(e.target.value)}
                      placeholder={filter.placeholder}
                      className="w-full px-3 py-2.5 text-sm border border-gray-300/60 bg-white/90 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500 transition-all duration-200 shadow-sm"
                    />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
