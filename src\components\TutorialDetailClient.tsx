"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import MarkdownRenderer from "@/components/MarkdownRenderer";

interface Tutorial {
  id: string;
  title: string;
  slug: string;
  date: string;
  filepath: string;
}

interface TutorialDetailClientProps {
  tutorial: Tutorial;
}

export default function TutorialDetailClient({ tutorial }: TutorialDetailClientProps) {
  const [content, setContent] = useState("");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchContent = async () => {
      try {
        const response = await fetch(`/${tutorial.filepath}`);
        if (response.ok) {
          const text = await response.text();
          setContent(text);
        } else {
          setContent("无法加载教程内容");
        }
      } catch (error) {
        console.error("加载教程内容时出错:", error);
        setContent("加载教程内容时出错");
      } finally {
        setLoading(false);
      }
    };

    fetchContent();
  }, [tutorial.filepath]);

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <Link
          href="/tutorials"
          className="inline-flex items-center text-blue-600 hover:underline"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          返回教程列表
        </Link>
      </div>

      <article className="bg-white rounded-lg shadow-md p-6 md:p-8">
        <header className="mb-8 pb-4 border-b border-gray-100">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            {tutorial.title}
          </h1>
          <p className="text-gray-500">发布于: {tutorial.date}</p>
        </header>

        {loading ? (
          <div className="text-center py-10">
            <p className="text-gray-500 text-lg">加载中...</p>
          </div>
        ) : (
          <MarkdownRenderer content={content} />
        )}
      </article>
    </div>
  );
}
