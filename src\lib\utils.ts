import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * 合并className工具函数
 * 结合clsx和tailwind-merge功能，用于合并和处理类名
 */
export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs));
} 

/**
 * 格式化日期时间
 * 将ISO格式的日期时间字符串(如:2025-03-26T07:40:37)转换为更友好的格式(如:2025-03-26 07:40:37)
 * @param dateStr ISO格式的日期时间字符串
 * @returns 格式化后的日期时间字符串，只包含年月日时分秒
 */
export function formatDate(dateStr?: string): string {
    if (!dateStr) return '';
    
    // 尝试解析日期
    try {
        // 如果已经是格式化的字符串但包含毫秒，去除毫秒部分
        if (dateStr.includes(' ') && !dateStr.includes('T')) {
            // 移除毫秒部分
            const parts = dateStr.split('.');
            if (parts.length > 1) {
                return parts[0]; // 只保留年月日时分秒部分
            }
            return dateStr;
        }
        
        // 将ISO格式(2025-03-26T07:40:37)转换为友好格式(2025-03-26 07:40:37)，并移除时区和毫秒信息
        if (dateStr.includes('T')) {
            // 移除+00:00等时区信息和毫秒
            const withoutTimezone = dateStr.replace(/(\+|-)\d{2}:\d{2}$/, '');
            const withoutMilli = withoutTimezone.split('.')[0]; // 去除毫秒部分
            return withoutMilli.replace('T', ' ');
        }
        
        // 处理其他可能的日期格式
        const date = new Date(dateStr);
        if (!isNaN(date.getTime())) {
            // 获取ISO字符串后去掉毫秒和时区信息
            return date.toISOString().replace('T', ' ').split('.')[0];
        }
        
        // 如果是带毫秒的格式，去除毫秒部分
        if (dateStr.includes('.')) {
            return dateStr.split('.')[0];
        }
        
        return dateStr;
    } catch (e) {
        console.error('日期格式化错误:', e);
        return dateStr;
    }
} 