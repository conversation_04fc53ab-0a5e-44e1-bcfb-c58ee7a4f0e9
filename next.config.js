/** @type {import('next').NextConfig} */
const nextConfig = {
  // 优化webpack配置以减少ChunkLoadError
  webpack: (config, { dev, isServer }) => {
    // 在开发环境下优化chunk加载
    if (dev && !isServer) {
      // 减少代码分割，避免过多的动态chunk
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          ...config.optimization.splitChunks,
          cacheGroups: {
            ...config.optimization.splitChunks.cacheGroups,
            // 将常用的工具函数打包到一个chunk中
            utils: {
              name: "utils",
              chunks: "all",
              test: /[\\/]src[\\/]utils[\\/]/,
              priority: 10,
              enforce: true,
            },
            // 将服务层打包到一个chunk中
            services: {
              name: "services",
              chunks: "all",
              test: /[\\/]src[\\/]services[\\/]/,
              priority: 10,
              enforce: true,
            },
          },
        },
      };
    }

    return config;
  },

  // 配置API代理以解决CORS问题
  async rewrites() {
    return [
      {
        source: "/api/:path*",
        destination: `${
          process.env.API_PROXY_TARGET || "http://127.0.0.1:9999"
        }/api/:path*`,
      },
    ];
  },
};

module.exports = nextConfig;
