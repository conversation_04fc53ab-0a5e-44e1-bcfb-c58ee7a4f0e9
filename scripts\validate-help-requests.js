#!/usr/bin/env node

/**
 * 验证资源求助功能的完整性
 * 检查所有文件是否存在且格式正确
 */

// eslint-disable-next-line @typescript-eslint/no-require-imports
const fs = require("fs");
// eslint-disable-next-line @typescript-eslint/no-require-imports
const path = require("path");

// 需要检查的文件列表
const requiredFiles = [
  // 类型定义
  "src/types/help-request.ts",
  "src/types/user-level.ts",

  // 服务层
  "src/services/helpRequestService.ts",

  // 页面文件
  "src/app/help-requests/layout.tsx",
  "src/app/help-requests/page.tsx",
  "src/app/help-requests/create/page.tsx",
  "src/app/help-requests/my/page.tsx",
  "src/app/help-requests/[id]/page.tsx",

  // 管理员页面
  "src/app/admin/help-requests/page.tsx",

  // 组件文件
  "src/components/help-requests/HelpRequestCard.tsx",
  "src/components/help-requests/UserBadge.tsx",
  "src/components/help-requests/HelpRequestFilters.tsx",
  "src/components/help-requests/HelpRequestAuthGuard.tsx",
  "src/components/help-requests/HelpRequestStructuredData.tsx",

  // 配置文件
  "src/config/help-request-seo.ts",

  // 测试文件
  "src/tests/help-requests.test.ts",
  "src/tests/help-requests-integration.test.ts",
  "src/tests/help-requests-eslint-fixes.test.ts",

  // 文档
  "docs/help-requests-feature.md",
];

// 需要检查的关键内容
const contentChecks = [
  {
    file: "src/types/help-request.ts",
    patterns: [
      "export interface HelpRequest",
      "export interface HelpAnswer",
      "export const PAN_TYPE_MAP",
    ],
  },
  {
    file: "src/services/helpRequestService.ts",
    patterns: [
      "export async function getHelpRequests",
      "export async function createHelpRequest",
      "export async function getAdminHelpRequests",
    ],
  },
  {
    file: "src/app/help-requests/page.tsx",
    patterns: [
      "export default function HelpRequestsPage",
      "HelpRequestCard",
      "HelpRequestFilters",
    ],
  },
  {
    file: "src/components/admin/AdminSidebar.tsx",
    patterns: ["QuestionMarkCircleIcon", "/admin/help-requests", "求助管理"],
  },
];

console.log("🔍 开始验证资源求助功能...\n");

let allValid = true;
let checkedFiles = 0;
let missingFiles = 0;

// 检查文件是否存在
console.log("📁 检查文件存在性...");
requiredFiles.forEach((filePath) => {
  const fullPath = path.join(process.cwd(), filePath);
  if (fs.existsSync(fullPath)) {
    console.log(`✅ ${filePath}`);
    checkedFiles++;
  } else {
    console.log(`❌ ${filePath} - 文件不存在`);
    allValid = false;
    missingFiles++;
  }
});

console.log(
  `\n📊 文件检查结果: ${checkedFiles}/${requiredFiles.length} 个文件存在`
);
if (missingFiles > 0) {
  console.log(`⚠️  缺失 ${missingFiles} 个文件`);
}

// 检查文件内容
console.log("\n🔍 检查关键内容...");
let contentIssues = 0;

contentChecks.forEach((check) => {
  const fullPath = path.join(process.cwd(), check.file);
  if (fs.existsSync(fullPath)) {
    const content = fs.readFileSync(fullPath, "utf8");
    let fileValid = true;

    check.patterns.forEach((pattern) => {
      if (!content.includes(pattern)) {
        console.log(`❌ ${check.file} - 缺少: ${pattern}`);
        fileValid = false;
        contentIssues++;
        allValid = false;
      }
    });

    if (fileValid) {
      console.log(`✅ ${check.file} - 内容检查通过`);
    }
  } else {
    console.log(`⚠️  ${check.file} - 文件不存在，跳过内容检查`);
  }
});

// 检查导航集成
console.log("\n🧭 检查导航集成...");
const navigationFile = path.join(
  process.cwd(),
  "src/components/Navigation.tsx"
);
if (fs.existsSync(navigationFile)) {
  const navContent = fs.readFileSync(navigationFile, "utf8");
  if (
    navContent.includes("/help-requests") &&
    navContent.includes("资源求助")
  ) {
    console.log("✅ 导航栏集成正确");
  } else {
    console.log("❌ 导航栏集成缺失");
    allValid = false;
  }
} else {
  console.log("⚠️  Navigation.tsx 文件不存在");
}

// 检查搜索页面集成
console.log("\n🔍 检查搜索页面集成...");
const searchFile = path.join(process.cwd(), "src/app/search/page.tsx");
if (fs.existsSync(searchFile)) {
  const searchContent = fs.readFileSync(searchFile, "utf8");
  if (
    searchContent.includes("/help-requests/create") &&
    searchContent.includes("QuestionMarkCircleIcon")
  ) {
    console.log("✅ 搜索页面集成正确");
  } else {
    console.log("❌ 搜索页面集成缺失");
    allValid = false;
  }
} else {
  console.log("⚠️  search/page.tsx 文件不存在");
}

// 检查管理员侧边栏集成
console.log("\n👨‍💼 检查管理员侧边栏集成...");
const adminSidebarFile = path.join(
  process.cwd(),
  "src/components/admin/AdminSidebar.tsx"
);
if (fs.existsSync(adminSidebarFile)) {
  const sidebarContent = fs.readFileSync(adminSidebarFile, "utf8");
  if (
    sidebarContent.includes("/admin/help-requests") &&
    sidebarContent.includes("求助管理")
  ) {
    console.log("✅ 管理员侧边栏集成正确");
  } else {
    console.log("❌ 管理员侧边栏集成缺失");
    allValid = false;
  }
} else {
  console.log("⚠️  AdminSidebar.tsx 文件不存在");
}

// 检查TypeScript类型
console.log("\n🔧 检查TypeScript类型...");
const typeFiles = ["src/types/help-request.ts", "src/types/user-level.ts"];

typeFiles.forEach((typeFile) => {
  const fullPath = path.join(process.cwd(), typeFile);
  if (fs.existsSync(fullPath)) {
    const content = fs.readFileSync(fullPath, "utf8");

    // 检查是否有基本的导出
    if (
      content.includes("export interface") ||
      content.includes("export type") ||
      content.includes("export const")
    ) {
      console.log(`✅ ${typeFile} - 类型定义正确`);
    } else {
      console.log(`❌ ${typeFile} - 缺少类型导出`);
      allValid = false;
    }
  }
});

// 最终结果
console.log("\n" + "=".repeat(50));
if (allValid) {
  console.log("🎉 验证通过！资源求助功能已正确实现");
  console.log("\n✅ 所有必需文件存在");
  console.log("✅ 关键内容检查通过");
  console.log("✅ 导航集成正确");
  console.log("✅ 搜索页面集成正确");
  console.log("✅ 管理员功能集成正确");
  console.log("✅ TypeScript类型定义正确");

  console.log("\n🚀 功能已准备好部署！");

  console.log("\n📋 下一步操作:");
  console.log("1. 运行 npm run build 检查构建");
  console.log("2. 运行 npm test 执行测试");
  console.log("3. 检查后端API是否已实现");
  console.log("4. 进行功能测试");
  console.log("5. 部署到生产环境");

  process.exit(0);
} else {
  console.log("❌ 验证失败！发现以下问题:");
  if (missingFiles > 0) {
    console.log(`   - ${missingFiles} 个文件缺失`);
  }
  if (contentIssues > 0) {
    console.log(`   - ${contentIssues} 个内容问题`);
  }

  console.log("\n🔧 请修复上述问题后重新验证");
  process.exit(1);
}
