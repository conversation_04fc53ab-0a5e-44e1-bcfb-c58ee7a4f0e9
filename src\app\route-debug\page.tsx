'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

/**
 * 路由调试页面 - 帮助诊断登录跳转问题
 */
export default function RouteDebugPage() {
  const pathname = usePathname();

  const routes = [
    { path: '/login', description: '旧版登录页面' },
    { path: '/auth/login', description: '新版登录页面（导航链接指向）' },
    { path: '/register', description: '旧版注册页面' },
    { path: '/auth/register', description: '新版注册页面（导航链接指向）' },
    { path: '/forgot-password', description: '旧版忘记密码页面' },
    { path: '/auth/forgot-password', description: '新版忘记密码页面' },
  ];

  const testRoute = async (path: string) => {
    try {
      const response = await fetch(path);
      return {
        status: response.status,
        ok: response.ok,
        statusText: response.statusText
      };
    } catch (error) {
      return {
        status: 0,
        ok: false,
        statusText: 'Network Error',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            🔍 路由调试页面
          </h1>

          <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
            <h2 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
              ⚠️ 发现的问题
            </h2>
            <ul className="text-yellow-700 dark:text-yellow-300 text-sm space-y-1">
              <li>• 存在重复的路由：/login 和 /auth/login</li>
              <li>• 导航组件指向 /auth/login，但可能有用户访问 /login</li>
              <li>• 这可能导致路由冲突和跳转问题</li>
            </ul>
          </div>

          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              📍 当前路径: {pathname}
            </h2>
          </div>

          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              🧪 路由测试
            </h2>
            
            <div className="grid gap-4">
              {routes.map((route) => (
                <div
                  key={route.path}
                  className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-md"
                >
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 dark:text-white">
                      {route.path}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {route.description}
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Link
                      href={route.path}
                      className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors"
                    >
                      访问
                    </Link>
                    <button
                      onClick={async () => {
                        const result = await testRoute(route.path);
                        alert(`${route.path}\n状态: ${result.status}\n成功: ${result.ok}\n${result.error || ''}`);
                      }}
                      className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 transition-colors"
                    >
                      测试
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
            <h2 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">
              💡 建议的解决方案
            </h2>
            <ol className="text-blue-700 dark:text-blue-300 text-sm space-y-2">
              <li>1. <strong>统一路由结构</strong>：删除重复的路由，只保留一套</li>
              <li>2. <strong>添加重定向</strong>：将旧路由重定向到新路由</li>
              <li>3. <strong>更新所有链接</strong>：确保所有内部链接指向正确的路由</li>
              <li>4. <strong>测试验证</strong>：确保所有登录入口都能正常工作</li>
            </ol>
          </div>

          <div className="mt-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
            <h2 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-2">
              🔧 快速修复步骤
            </h2>
            <div className="text-green-700 dark:text-green-300 text-sm space-y-1">
              <p><strong>选项1：保留 /auth/* 路由</strong></p>
              <ul className="ml-4 space-y-1">
                <li>• 删除 /login、/register、/forgot-password 页面</li>
                <li>• 添加重定向规则</li>
              </ul>
              
              <p className="mt-3"><strong>选项2：保留根级路由</strong></p>
              <ul className="ml-4 space-y-1">
                <li>• 删除 /auth/* 页面</li>
                <li>• 更新导航组件中的链接</li>
              </ul>
            </div>
          </div>

          <div className="mt-6 flex space-x-4">
            <Link
              href="/"
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
            >
              返回首页
            </Link>
            <Link
              href="/test-cors"
              className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors"
            >
              CORS测试
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
