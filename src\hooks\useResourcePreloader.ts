"use client";

import { useCallback, useRef } from "react";
import {
  getResourceByKey,
  getValidResources,
  checkResourceStatus,
} from "@/services/resourceService";
import { globalCacheManager } from "@/lib/cacheManager";
import { useResourceDetailPerformance } from "./usePerformanceMonitor";

interface PreloadConfig {
  enableSimilarResources: boolean;
  enableUserResources: boolean;
  enableResourceStatus: boolean;
  maxConcurrentRequests: number;
  preloadDelay: number; // 延迟预加载时间（毫秒）
}

/**
 * 资源预加载Hook
 * 智能预加载相关资源，提高用户体验
 */
export const useResourcePreloader = (config: Partial<PreloadConfig> = {}) => {
  const defaultConfig: PreloadConfig = {
    enableSimilarResources: true,
    enableUserResources: true,
    enableResourceStatus: true,
    maxConcurrentRequests: 3,
    preloadDelay: 1000, // 1秒后开始预加载
  };

  const finalConfig = { ...defaultConfig, ...config };
  const { measureApiCall } = useResourceDetailPerformance();
  const preloadQueueRef = useRef<Array<() => Promise<void>>>([]);
  const activeRequestsRef = useRef(0);

  // 执行预加载队列
  const processPreloadQueue = useCallback(async () => {
    while (
      preloadQueueRef.current.length > 0 &&
      activeRequestsRef.current < finalConfig.maxConcurrentRequests
    ) {
      const task = preloadQueueRef.current.shift();
      if (task) {
        activeRequestsRef.current++;
        try {
          await task();
        } catch {
          // 忽略预加载任务失败
        } finally {
          activeRequestsRef.current--;
        }
      }
    }
  }, [finalConfig.maxConcurrentRequests]);

  // 添加预加载任务
  const addPreloadTask = useCallback(
    (task: () => Promise<void>) => {
      preloadQueueRef.current.push(task);
      // 延迟执行，避免阻塞主要内容加载
      setTimeout(processPreloadQueue, finalConfig.preloadDelay);
    },
    [processPreloadQueue, finalConfig.preloadDelay]
  );

  // 预加载资源详情
  const preloadResourceDetail = useCallback(
    (resourceKey: string) => {
      const cacheKey = `resource_detail_${resourceKey}`;

      // 检查是否已缓存
      if (globalCacheManager.get(cacheKey)) {
        return;
      }

      addPreloadTask(async () => {
        try {
          const resource = await measureApiCall(
            "preload_resource_detail",
            () => getResourceByKey(resourceKey),
            { resourceKey, context: "preload" }
          );

          if (resource) {
            globalCacheManager.set(cacheKey, resource, 10 * 60 * 1000); // 10分钟缓存
          }
        } catch (error) {
          console.warn(`Failed to preload resource ${resourceKey}:`, error);
        }
      });
    },
    [addPreloadTask, measureApiCall]
  );

  // 预加载相似资源
  const preloadSimilarResources = useCallback(
    (title: string, panType: number) => {
      if (!finalConfig.enableSimilarResources) return;

      const cacheKey = `similar_resources_${title}_${panType}`;

      if (globalCacheManager.get(cacheKey)) {
        return;
      }

      addPreloadTask(async () => {
        try {
          const resources = await measureApiCall(
            "preload_similar_resources",
            () => getValidResources(title, panType, 10, 1),
            { title, panType, context: "preload" }
          );

          globalCacheManager.set(cacheKey, resources, 15 * 60 * 1000); // 15分钟缓存
        } catch (error) {
          console.warn(
            `Failed to preload similar resources for ${title}:`,
            error
          );
        }
      });
    },
    [finalConfig.enableSimilarResources, addPreloadTask, measureApiCall]
  );

  // 预加载用户资源
  const preloadUserResources = useCallback(
    (author: string) => {
      if (!finalConfig.enableUserResources || !author) return;

      const cacheKey = `user_resources_${author}`;

      if (globalCacheManager.get(cacheKey)) {
        return;
      }

      addPreloadTask(async () => {
        try {
          const resources = await measureApiCall(
            "preload_user_resources",
            () => getValidResources("", null, 10, 1, author, "created_at"),
            { author, context: "preload" }
          );

          globalCacheManager.set(cacheKey, resources, 15 * 60 * 1000); // 15分钟缓存
        } catch (error) {
          console.warn(
            `Failed to preload user resources for ${author}:`,
            error
          );
        }
      });
    },
    [finalConfig.enableUserResources, addPreloadTask, measureApiCall]
  );

  // 预加载资源状态
  const preloadResourceStatus = useCallback(
    (resourceKey: string, panType: number) => {
      if (!finalConfig.enableResourceStatus) return;

      const cacheKey = `resource_status_${resourceKey}_${panType}`;

      if (globalCacheManager.get(cacheKey)) {
        return;
      }

      addPreloadTask(async () => {
        try {
          const status = await measureApiCall(
            "preload_resource_status",
            () => checkResourceStatus(resourceKey, panType),
            { resourceKey, panType, context: "preload" }
          );

          // 只缓存有效的状态
          if (status.valid) {
            globalCacheManager.set(cacheKey, status, 2 * 60 * 1000); // 2分钟缓存
          }
        } catch (error) {
          console.warn(
            `Failed to preload resource status for ${resourceKey}:`,
            error
          );
        }
      });
    },
    [finalConfig.enableResourceStatus, addPreloadTask, measureApiCall]
  );

  // 批量预加载相关资源
  const preloadRelatedResources = useCallback(
    (resourceKey: string, title: string, panType: number, author?: string) => {
      // 预加载相似资源
      preloadSimilarResources(title, panType);

      // 预加载用户资源
      if (author) {
        preloadUserResources(author);
      }

      // 预加载资源状态
      preloadResourceStatus(resourceKey, panType);
    },
    [preloadSimilarResources, preloadUserResources, preloadResourceStatus]
  );

  // 预加载链接中的资源（鼠标悬停时）
  const preloadOnHover = useCallback(
    (resourceKey: string) => {
      preloadResourceDetail(resourceKey);
    },
    [preloadResourceDetail]
  );

  // 预加载可见区域的资源
  const preloadVisibleResources = useCallback(
    (resourceKeys: string[]) => {
      resourceKeys.forEach((key) => {
        preloadResourceDetail(key);
      });
    },
    [preloadResourceDetail]
  );

  // 清理预加载队列
  const clearPreloadQueue = useCallback(() => {
    preloadQueueRef.current = [];
  }, []);

  // 获取预加载统计
  const getPreloadStats = useCallback(() => {
    return {
      queueLength: preloadQueueRef.current.length,
      activeRequests: activeRequestsRef.current,
      maxConcurrentRequests: finalConfig.maxConcurrentRequests,
    };
  }, [finalConfig.maxConcurrentRequests]);

  return {
    preloadResourceDetail,
    preloadSimilarResources,
    preloadUserResources,
    preloadResourceStatus,
    preloadRelatedResources,
    preloadOnHover,
    preloadVisibleResources,
    clearPreloadQueue,
    getPreloadStats,
  };
};

/**
 * 专门用于资源详情页面的预加载Hook
 */
export const useResourceDetailPreloader = () => {
  return useResourcePreloader({
    enableSimilarResources: true,
    enableUserResources: true,
    enableResourceStatus: true,
    maxConcurrentRequests: 2, // 资源详情页面使用较少的并发请求
    preloadDelay: 500, // 更快的预加载
  });
};
