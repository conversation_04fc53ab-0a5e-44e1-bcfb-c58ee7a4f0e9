"use client";

import { createContext, useContext, ReactNode } from "react";
import { useAdminSidebar } from "@/hooks/useAdminSidebar";

interface AdminContextType {
  sidebar: ReturnType<typeof useAdminSidebar>;
}

const AdminContext = createContext<AdminContextType | undefined>(undefined);

interface AdminProviderProps {
  children: ReactNode;
}

/**
 * 管理后台状态管理Provider
 * 提供侧边栏状态管理和其他管理后台专用状态
 */
export function AdminProvider({ children }: AdminProviderProps) {
  const sidebar = useAdminSidebar();

  const value: AdminContextType = {
    sidebar,
  };

  return (
    <AdminContext.Provider value={value}>{children}</AdminContext.Provider>
  );
}

/**
 * 使用管理后台Context的Hook
 */
export function useAdmin() {
  const context = useContext(AdminContext);
  if (context === undefined) {
    throw new Error("useAdmin must be used within an AdminProvider");
  }
  return context;
}

/**
 * 使用管理后台侧边栏状态的Hook
 */
export function useAdminSidebarContext() {
  const { sidebar } = useAdmin();
  return sidebar;
}
