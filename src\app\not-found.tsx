"use client";

import { useRouter } from "next/navigation";
import { useCallback } from "react";
import { Button } from "@/components/ui/Button";
import SearchBar from "@/components/SearchBar";
import { useSearch } from "@/hooks/useSearch";
import { SEARCH_CONFIG } from "@/config/constants";
import { PageContainer } from "@/components/layout/PageContainer";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";

export default function NotFound() {
  const router = useRouter();
  const { searchType } = useSearch({
    initialType: SEARCH_CONFIG.defaultType,
  });

  const handleSearch = useCallback(
    (query: string, type: "local" | "online") => {
      if (query.trim()) {
        router.push(`/search?q=${encodeURIComponent(query)}&type=${type}`);
      }
    },
    [router]
  );

  return (
    <PageContainer>
      <div className="flex flex-col items-center justify-center min-h-[60vh] text-center">
        <h1 className="text-6xl md:text-9xl font-extrabold text-gray-800 dark:text-gray-100 tracking-wider">
          404
        </h1>
        <p className="mt-4 text-lg md:text-2xl font-semibold text-gray-600 dark:text-gray-300">
          哎呀！页面飞到外太空去了
        </p>
        <p className="mt-2 text-base text-gray-500 dark:text-gray-400">
          您要找的页面不存在或已被删除。
        </p>
        <div className="mt-8 w-full max-w-lg">
          <SearchBar
            onSearch={handleSearch}
            placeholder="或者...换个关键词试试？"
            initialSearchType={searchType}
          />
        </div>
        <div className="mt-8 flex items-center gap-4">
          <Button onClick={() => router.back()} variant="outline">
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            返回上一页
          </Button>
          <Button onClick={() => router.push("/")}>返回首页</Button>
        </div>
      </div>
    </PageContainer>
  );
}
