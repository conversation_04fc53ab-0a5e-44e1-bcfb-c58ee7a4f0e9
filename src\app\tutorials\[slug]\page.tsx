import { Metadata } from "next";
import { notFound } from "next/navigation";
import TutorialDetailClient from "@/components/TutorialDetailClient";
import tutorials from "@/data/tutorials.json";
import { generateTutorialSEO, createMetadata } from "@/config/seo";

interface PageProps {
  params: Promise<{ slug: string }>;
}

// 生成动态SEO元数据
export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const awaitedParams = await params;
  const slug = awaitedParams.slug;

  const tutorial = tutorials.find((item) => item.slug === slug);

  if (!tutorial) {
    return {
      title: "教程未找到 - 97盘搜",
      description: "抱歉，您所查找的教程不存在或已被移除。",
    };
  }

  const seoConfig = generateTutorialSEO(slug, tutorial.title);

  return createMetadata({
    ...seoConfig,
    path: `/tutorials/${slug}`,
  });
}

export default async function TutorialPage({ params }: PageProps) {
  const awaitedParams = await params;
  const slug = awaitedParams.slug;

  const tutorial = tutorials.find((item) => item.slug === slug);

  if (!tutorial) {
    notFound();
  }

  return <TutorialDetailClient tutorial={tutorial} />;
}
