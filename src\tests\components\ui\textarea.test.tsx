/**
 * Textarea 组件测试用例
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { Textarea } from '@/components/ui/textarea';

describe('Textarea 组件', () => {
  it('应该正确渲染基本的 textarea', () => {
    render(<Textarea placeholder="请输入内容" data-testid="textarea" />);
    
    const textarea = screen.getByTestId('textarea');
    expect(textarea).toBeInTheDocument();
    expect(textarea).toHaveAttribute('placeholder', '请输入内容');
  });

  it('应该支持受控模式', () => {
    const handleChange = vi.fn();
    render(
      <Textarea 
        value="测试内容" 
        onChange={handleChange} 
        data-testid="textarea" 
      />
    );
    
    const textarea = screen.getByTestId('textarea');
    expect(textarea).toHaveValue('测试内容');

    fireEvent.change(textarea, { target: { value: '新内容' } });
    expect(handleChange).toHaveBeenCalled();
  });

  it('应该支持非受控模式', () => {
    render(<Textarea defaultValue="默认内容" data-testid="textarea" />);
    
    const textarea = screen.getByTestId('textarea');
    expect(textarea).toHaveValue('默认内容');

    fireEvent.change(textarea, { target: { value: '修改后的内容' } });
    expect(textarea).toHaveValue('修改后的内容');
  });

  it('应该支持禁用状态', () => {
    render(<Textarea disabled data-testid="textarea" />);
    
    const textarea = screen.getByTestId('textarea');
    expect(textarea).toBeDisabled();
    expect(textarea).toHaveClass('disabled:cursor-not-allowed', 'disabled:opacity-50');
  });

  it('应该支持自定义样式类名', () => {
    render(<Textarea className="custom-class" data-testid="textarea" />);
    
    const textarea = screen.getByTestId('textarea');
    expect(textarea).toHaveClass('custom-class');
  });

  it('应该支持 ref 转发', () => {
    const ref = React.createRef<HTMLTextAreaElement>();
    render(<Textarea ref={ref} data-testid="textarea" />);
    
    expect(ref.current).toBeInstanceOf(HTMLTextAreaElement);
  });

  it('应该支持其他 HTML 属性', () => {
    render(
      <Textarea 
        rows={5}
        cols={30}
        maxLength={100}
        data-testid="textarea"
        aria-label="测试文本区域"
      />
    );
    
    const textarea = screen.getByTestId('textarea');
    expect(textarea).toHaveAttribute('rows', '5');
    expect(textarea).toHaveAttribute('cols', '30');
    expect(textarea).toHaveAttribute('maxLength', '100');
    expect(textarea).toHaveAttribute('aria-label', '测试文本区域');
  });

  it('应该有正确的默认样式类', () => {
    render(<Textarea data-testid="textarea" />);
    
    const textarea = screen.getByTestId('textarea');
    expect(textarea).toHaveClass(
      'flex',
      'min-h-[80px]',
      'w-full',
      'rounded-md',
      'border',
      'border-gray-200',
      'bg-white',
      'px-3',
      'py-2',
      'text-sm'
    );
  });

  it('应该支持焦点状态', () => {
    render(<Textarea data-testid="textarea" />);
    
    const textarea = screen.getByTestId('textarea');
    textarea.focus();
    
    expect(textarea).toHaveFocus();
  });
});
