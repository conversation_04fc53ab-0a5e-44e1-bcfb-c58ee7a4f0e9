'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

/**
 * 专门处理ChunkLoadError的错误边界组件
 */
export class ChunkErrorBoundary extends Component<Props, State> {
  private reloadAttempts = 0;
  private maxReloadAttempts = 2;

  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // 检查是否为ChunkLoadError
    const isChunkError = 
      error.name === 'ChunkLoadError' ||
      error.message.includes('Loading chunk') ||
      error.message.includes('ChunkLoadError');

    return {
      hasError: isChunkError,
      error: isChunkError ? error : undefined,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 只处理ChunkLoadError
    const isChunkError = 
      error.name === 'ChunkLoadError' ||
      error.message.includes('Loading chunk') ||
      error.message.includes('ChunkLoadError');

    if (isChunkError) {
      console.warn('ChunkErrorBoundary捕获到ChunkLoadError:', error);
      
      this.setState({
        hasError: true,
        error,
        errorInfo,
      });

      // 尝试自动刷新页面
      if (this.reloadAttempts < this.maxReloadAttempts) {
        this.reloadAttempts++;
        console.log(`尝试自动刷新页面 (${this.reloadAttempts}/${this.maxReloadAttempts})`);
        
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      }
    } else {
      // 对于非ChunkLoadError，重新抛出错误让其他错误边界处理
      throw error;
    }
  }

  handleManualReload = () => {
    window.location.reload();
  };

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认的错误UI
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
          <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 dark:bg-red-900 rounded-full mb-4">
              <svg
                className="w-6 h-6 text-red-600 dark:text-red-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white text-center mb-2">
              页面资源加载失败
            </h2>
            
            <p className="text-gray-600 dark:text-gray-300 text-center mb-6">
              页面的某些资源无法正常加载，这可能是由于网络问题或缓存问题导致的。
            </p>

            <div className="space-y-3">
              <button
                onClick={this.handleManualReload}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
              >
                刷新页面
              </button>
              
              <button
                onClick={this.handleRetry}
                className="w-full bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-white font-medium py-2 px-4 rounded-md transition-colors"
              >
                重试
              </button>
            </div>

            <div className="mt-4 text-xs text-gray-500 dark:text-gray-400 text-center">
              如果问题持续存在，请尝试清除浏览器缓存
            </div>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-4 text-xs">
                <summary className="cursor-pointer text-gray-500 dark:text-gray-400">
                  错误详情 (开发环境)
                </summary>
                <pre className="mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded text-red-600 dark:text-red-400 overflow-auto">
                  {this.state.error.toString()}
                  {this.state.errorInfo?.componentStack}
                </pre>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ChunkErrorBoundary;
