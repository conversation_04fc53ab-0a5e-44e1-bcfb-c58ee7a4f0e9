import React from 'react';

interface WebsiteStructuredDataProps {
    siteUrl: string;
    siteName: string;
}

export const WebsiteStructuredData: React.FC<WebsiteStructuredDataProps> = ({ siteUrl, siteName }) => {
    const structuredData = {
        '@context': 'https://schema.org',
        '@type': 'WebSite',
        url: siteUrl,
        name: siteName,
        description: '97盘搜,专注网盘资源检索,涵盖电影、电视剧、动漫、游戏、小说、广播剧、学习资料等资源,提供网盘链接下载.',
        potentialAction: {
            '@type': 'SearchAction',
            target: `${siteUrl}/search?q={search_term_string}`,
            'query-input': 'required name=search_term_string'
        }
    };

    return (
        <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
    );
};

interface BreadcrumbStructuredDataProps {
    items: {
        name: string;
        url: string;
    }[];
}

export const BreadcrumbStructuredData: React.FC<BreadcrumbStructuredDataProps> = ({ items }) => {
    const structuredData = {
        '@context': 'https://schema.org',
        '@type': 'BreadcrumbList',
        itemListElement: items.map((item, index) => ({
            '@type': 'ListItem',
            position: index + 1,
            name: item.name,
            item: item.url
        }))
    };

    return (
        <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
    );
};

export default WebsiteStructuredData; 