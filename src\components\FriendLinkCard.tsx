"use client";

import { memo } from "react";
import Image from "next/image";
import { ArrowTopRightOnSquareIcon } from "@heroicons/react/24/outline";

interface FriendLinkCardProps {
  name: string;
  description: string;
  url: string;
  icon?: string;
}

/**
 * 友情链接卡片组件
 * 用于展示单个友情链接的信息
 */
const FriendLinkCard = memo(function FriendLinkCard({
  name,
  description,
  url,
  icon,
}: FriendLinkCardProps) {
  const handleClick = () => {
    window.open(url, "_blank", "noopener,noreferrer");
  };

  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 hover:shadow-lg hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 cursor-pointer group">
      <div onClick={handleClick} className="h-full flex flex-col">
        {/* 头部：图标和标题 */}
        <div className="flex items-center mb-4">
          {icon && (
            <div className="w-10 h-10 mr-3 flex-shrink-0">
              <Image
                src={icon}
                alt={`${name} 图标`}
                width={40}
                height={40}
                className="object-contain rounded"
                onError={(e) => {
                  // 图标加载失败时隐藏
                  e.currentTarget.style.display = "none";
                }}
              />
            </div>
          )}
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors truncate">
              {name}
            </h3>
          </div>
          <ArrowTopRightOnSquareIcon className="w-4 h-4 ml-2 text-gray-400 dark:text-gray-500 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors flex-shrink-0" />
        </div>

        {/* 描述 */}
        <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed flex-grow mb-4">
          {description}
        </p>

        {/* 底部：链接地址 */}
        <div className="pt-3 border-t border-gray-100 dark:border-gray-700">
          <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
            {url}
          </p>
        </div>
      </div>
    </div>
  );
});

export default FriendLinkCard;
