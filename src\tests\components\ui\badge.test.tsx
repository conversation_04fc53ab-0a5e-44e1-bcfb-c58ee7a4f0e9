import React from "react";
import { render, screen } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import { Badge } from "@/components/ui/badge";

describe("Badge 组件", () => {
  it("应该正确渲染默认 Badge", () => {
    render(<Badge>默认徽章</Badge>);

    const badge = screen.getByText("默认徽章");
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveClass(
      "inline-flex",
      "items-center",
      "rounded-full",
      "border",
      "px-2.5",
      "py-0.5",
      "text-xs",
      "font-semibold",
      "transition-colors"
    );
  });

  it("应该正确渲染 default 变体", () => {
    render(<Badge variant="default">默认徽章</Badge>);

    const badge = screen.getByText("默认徽章");
    expect(badge).toHaveClass(
      "border-transparent",
      "bg-primary",
      "text-primary-foreground"
    );
  });

  it("应该正确渲染 secondary 变体", () => {
    render(<Badge variant="secondary">次要徽章</Badge>);

    const badge = screen.getByText("次要徽章");
    expect(badge).toHaveClass(
      "border-transparent",
      "bg-secondary",
      "text-secondary-foreground"
    );
  });

  it("应该正确渲染 destructive 变体", () => {
    render(<Badge variant="destructive">危险徽章</Badge>);

    const badge = screen.getByText("危险徽章");
    expect(badge).toHaveClass(
      "border-transparent",
      "bg-destructive",
      "text-destructive-foreground"
    );
  });

  it("应该正确渲染 outline 变体", () => {
    render(<Badge variant="outline">轮廓徽章</Badge>);

    const badge = screen.getByText("轮廓徽章");
    expect(badge).toHaveClass("text-foreground");
  });

  it("应该支持自定义 className", () => {
    render(<Badge className="custom-badge">自定义徽章</Badge>);

    const badge = screen.getByText("自定义徽章");
    expect(badge).toHaveClass("custom-badge");
  });

  it("应该支持点击事件", () => {
    const handleClick = vi.fn();
    render(<Badge onClick={handleClick}>可点击徽章</Badge>);

    const badge = screen.getByText("可点击徽章");
    badge.click();
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it("应该支持其他 HTML 属性", () => {
    render(
      <Badge data-testid="test-badge" title="测试徽章">
        测试徽章
      </Badge>
    );

    const badge = screen.getByTestId("test-badge");
    expect(badge).toHaveAttribute("title", "测试徽章");
  });

  it("应该正确处理子元素", () => {
    render(
      <Badge>
        <span>图标</span>
        文本内容
      </Badge>
    );

    expect(screen.getByText("图标")).toBeInTheDocument();
    expect(screen.getByText("文本内容")).toBeInTheDocument();
  });
});
