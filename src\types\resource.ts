export interface ResourceDetail {
  id: number;
  resource_key: string;
  pan_type: number;
  original_url: string;
  title: string;
  is_parsed: boolean;
  author: string;
  author_avatar: string;
  is_mine: boolean;
  verified_status: string;
  share_url: string;
  share_pwd: string;
  created_at: string;
  updated_at: string;
  expiry_date: string;
  file_type: string;
  file_size: string;
  access_count: number;
  text_content: string;
  seo_title: string;
  seo_description: string;
} 