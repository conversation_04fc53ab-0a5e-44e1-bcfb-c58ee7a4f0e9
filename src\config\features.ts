/**
 * 功能配置文件
 * 用于控制应用中各种功能的开启和关闭
 */

export interface FeatureConfig {
  /** 是否启用用户注册功能 */
  enableRegistration: boolean;
  /** 是否在导航栏显示注册按钮 */
  showRegistrationInNav: boolean;
  /** 是否在登录页面显示注册链接 */
  showRegistrationInLogin: boolean;
  /** 是否在导航栏显示登录按钮 */
  showLoginInNav: boolean;
}

/**
 * 默认功能配置
 * 可以通过环境变量或其他方式覆盖这些设置
 */
export const defaultFeatureConfig: FeatureConfig = {
  enableRegistration: false, // 默认关闭注册功能
  showRegistrationInNav: false, // 默认不在导航栏显示注册按钮
  showRegistrationInLogin: false, // 默认不在登录页面显示注册链接
  showLoginInNav: true, // 默认在导航栏显示登录按钮
};

/**
 * 获取当前功能配置
 * 优先级：环境变量 > 默认配置
 */
export function getFeatureConfig(): FeatureConfig {
  return {
    enableRegistration:
      process.env.NEXT_PUBLIC_ENABLE_REGISTRATION === "true" ||
      defaultFeatureConfig.enableRegistration,
    showRegistrationInNav:
      process.env.NEXT_PUBLIC_SHOW_REGISTRATION_IN_NAV === "true" ||
      defaultFeatureConfig.showRegistrationInNav,
    showRegistrationInLogin:
      process.env.NEXT_PUBLIC_SHOW_REGISTRATION_IN_LOGIN === "true" ||
      defaultFeatureConfig.showRegistrationInLogin,
    showLoginInNav:
      process.env.NEXT_PUBLIC_SHOW_LOGIN_IN_NAV !== "false" &&
      defaultFeatureConfig.showLoginInNav,
  };
}

/**
 * 检查是否启用注册功能
 */
export function isRegistrationEnabled(): boolean {
  return getFeatureConfig().enableRegistration;
}

/**
 * 检查是否在导航栏显示注册按钮
 */
export function shouldShowRegistrationInNav(): boolean {
  return getFeatureConfig().showRegistrationInNav;
}

/**
 * 检查是否在登录页面显示注册链接
 */
export function shouldShowRegistrationInLogin(): boolean {
  return getFeatureConfig().showRegistrationInLogin;
}

/**
 * 检查是否在导航栏显示登录按钮
 */
export function shouldShowLoginInNav(): boolean {
  return getFeatureConfig().showLoginInNav;
}
