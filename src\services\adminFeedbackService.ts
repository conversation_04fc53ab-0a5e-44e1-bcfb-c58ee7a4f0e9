const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

export interface AdminFeedback {
  id: number;
  resource_id: string;
  resource_title?: string;
  pan_type: number;
  invalid_type: number;
  description?: string;
  contact_info?: string;
  is_verified: boolean;
  created_at: string;
  updated_at: string;
}

export interface AdminFeedbackListResponse {
  success: boolean;
  message: string;
  data: {
    feedbacks: AdminFeedback[];
    pagination: {
      page: number;
      size: number;
      total: number;
      pages: number;
    };
  };
  error?: string;
}

export interface AdminFeedbackDetailResponse {
  success: boolean;
  message: string;
  data: AdminFeedback;
  error?: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  error?: string;
}

/**
 * 获取管理员反馈列表
 */
export async function getAdminFeedbackList(
  page: number = 1,
  size: number = 20,
  invalidType?: number,
  panType?: number,
  isVerified?: boolean,
  keyword?: string,
  startDate?: string,
  endDate?: string
): Promise<AdminFeedbackListResponse> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return {
        success: false,
        message: "未登录",
        data: {
          feedbacks: [],
          pagination: { page: 1, size: 20, total: 0, pages: 0 },
        },
        error: "需要管理员权限",
      };
    }

    const params = new URLSearchParams();
    params.append("page", page.toString());
    params.append("size", size.toString());

    if (invalidType !== undefined)
      params.append("invalid_type", invalidType.toString());
    if (panType !== undefined) params.append("pan_type", panType.toString());
    if (isVerified !== undefined)
      params.append("is_verified", isVerified.toString());
    if (keyword) params.append("keyword", keyword);
    if (startDate) params.append("start_date", startDate);
    if (endDate) params.append("end_date", endDate);

    const response = await fetch(
      `${API_BASE_URL}/api/admin/feedback?${params}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.message || "获取反馈列表失败",
        data: {
          feedbacks: [],
          pagination: { page: 1, size: 20, total: 0, pages: 0 },
        },
        error: data.error,
      };
    }

    return {
      success: true,
      message: data.message || "获取反馈列表成功",
      data: data.data || data,
    };
  } catch (error) {
    console.error("获取管理员反馈列表失败:", error);
    return {
      success: false,
      message: "网络错误，请稍后重试",
      data: {
        feedbacks: [],
        pagination: { page: 1, size: 20, total: 0, pages: 0 },
      },
      error: error instanceof Error ? error.message : "未知错误",
    };
  }
}

/**
 * 获取管理员反馈详情
 */
export async function getAdminFeedbackDetail(
  feedbackId: number
): Promise<AdminFeedbackDetailResponse> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return {
        success: false,
        message: "未登录",
        data: {} as AdminFeedback,
        error: "需要管理员权限",
      };
    }

    const response = await fetch(
      `${API_BASE_URL}/api/admin/feedback/${feedbackId}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.message || "获取反馈详情失败",
        data: {} as AdminFeedback,
        error: data.error,
      };
    }

    return {
      success: true,
      message: data.message || "获取反馈详情成功",
      data: data.data || data,
    };
  } catch (error) {
    console.error("获取管理员反馈详情失败:", error);
    return {
      success: false,
      message: "网络错误，请稍后重试",
      data: {} as AdminFeedback,
      error: error instanceof Error ? error.message : "未知错误",
    };
  }
}

/**
 * 标记反馈为已验证
 */
export async function markFeedbackAsVerified(
  feedbackId: number
): Promise<AuthResponse> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return {
        success: false,
        message: "未登录",
        error: "需要管理员权限",
      };
    }

    const response = await fetch(
      `${API_BASE_URL}/api/admin/feedback/${feedbackId}/verify`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.message || "标记反馈失败",
        error: data.error,
      };
    }

    return {
      success: true,
      message: data.message || "反馈标记成功",
    };
  } catch (error) {
    console.error("标记反馈失败:", error);
    return {
      success: false,
      message: "网络错误，请稍后重试",
      error: error instanceof Error ? error.message : "未知错误",
    };
  }
}

/**
 * 删除反馈
 */
export async function deleteFeedback(
  feedbackId: number
): Promise<AuthResponse> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return {
        success: false,
        message: "未登录",
        error: "需要管理员权限",
      };
    }

    const response = await fetch(
      `${API_BASE_URL}/api/admin/feedback/${feedbackId}`,
      {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.message || "删除反馈失败",
        error: data.error,
      };
    }

    return {
      success: true,
      message: data.message || "反馈删除成功",
    };
  } catch (error) {
    console.error("删除反馈失败:", error);
    return {
      success: false,
      message: "网络错误，请稍后重试",
      error: error instanceof Error ? error.message : "未知错误",
    };
  }
}
