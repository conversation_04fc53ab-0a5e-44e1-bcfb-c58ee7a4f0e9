import { NextResponse } from "next/server";
import { getResourcesForSitemap, generateSitemapXml } from "@/lib/resource";

export const dynamic = "force-dynamic";

export async function GET(
  _request: Request,
  context: { params: Promise<{ page: string }> }
) {
  try {
    const params = await context.params;
    const pageNumber = parseInt(params.page, 10);

    console.log(`生成站点地图，页码: ${pageNumber}`);

    if (isNaN(pageNumber) || pageNumber < 1) {
      console.error(`无效的页码: ${params.page}`);
      return new Response("无效的页码", { status: 400 });
    }

    const resources = await getResourcesForSitemap(pageNumber);
    console.log(`获取到 ${resources.length} 个资源`);

    const sitemapXml = generateSitemapXml(resources);
    console.log(`生成的站点地图长度: ${sitemapXml.length} 字符`);

    return new NextResponse(sitemapXml, {
      headers: { "Content-Type": "application/xml" },
    });
  } catch (error) {
    console.error("生成资源站点地图时发生错误:", error);
    // 发生错误时返回空的站点地图
    const emptyXml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"></urlset>`;

    return new NextResponse(emptyXml, {
      headers: { "Content-Type": "application/xml" },
    });
  }
}
