/**
 * 个人资料页面测试用例
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import ProfilePage from '../../../app/profile/page';
import { useAuth } from '../../../hooks/useAuth';
import { getMyProfile, getProfileStatistics } from '../../../services/profileService';

// Mock dependencies
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}));

vi.mock('../../../hooks/useAuth', () => ({
  useAuth: vi.fn(),
}));

vi.mock('../../../services/profileService', () => ({
  getMyProfile: vi.fn(),
  getProfileStatistics: vi.fn(),
}));

// Mock components
vi.mock('../../../components/layout/PageContainer', () => ({
  PageContainer: ({ children }: { children: React.ReactNode }) => <div data-testid="page-container">{children}</div>,
}));

vi.mock('../../../components/AuthGuard', () => ({
  AuthGuard: ({ children }: { children: React.ReactNode }) => <div data-testid="auth-guard">{children}</div>,
}));

vi.mock('../../../components/profile/ProfileCard', () => ({
  ProfileCard: ({ onEditClick, showEditButton }: { onEditClick: () => void; showEditButton: boolean }) => (
    <div data-testid="profile-card">
      {showEditButton && (
        <button onClick={onEditClick} data-testid="edit-profile-button">
          编辑资料
        </button>
      )}
    </div>
  ),
}));

describe('ProfilePage', () => {
  const mockPush = vi.fn();
  const mockUser = {
    id: 1,
    username: 'testuser',
    email: '<EMAIL>',
    nickname: 'Test User',
    avatar: 'https://example.com/avatar.jpg',
    points: 150,
  };

  const mockProfile = {
    ...mockUser,
    status: 'active',
    email_verified: true,
    role: { id: '1', name: 'user' },
    title: '活跃用户',
    created_at: '2023-01-01T00:00:00Z',
  };

  const mockStats = {
    total_help_requests: 5,
    total_help_answers: 10,
    accepted_answers: 3,
    total_points: 150,
    current_title: '活跃用户',
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    (useRouter as any).mockReturnValue({
      push: mockPush,
    });

    (useAuth as any).mockReturnValue({
      user: mockUser,
    });

    (getMyProfile as any).mockResolvedValue({
      success: true,
      data: mockProfile,
    });

    (getProfileStatistics as any).mockResolvedValue({
      success: true,
      data: mockStats,
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('应该渲染个人资料页面', async () => {
    render(<ProfilePage />);

    await waitFor(() => {
      expect(screen.getByText('个人资料')).toBeInTheDocument();
      expect(screen.getByText('查看和管理您的个人信息')).toBeInTheDocument();
    });
  });

  it('应该显示加载状态', () => {
    (getMyProfile as any).mockImplementation(() => new Promise(() => {})); // 永不resolve

    render(<ProfilePage />);

    expect(screen.getByText('加载中...')).toBeInTheDocument();
  });

  it('应该显示错误信息', async () => {
    (getMyProfile as any).mockResolvedValue({
      success: false,
      message: '获取个人信息失败',
    });

    render(<ProfilePage />);

    await waitFor(() => {
      expect(screen.getByText('获取个人信息失败')).toBeInTheDocument();
      expect(screen.getByText('重试')).toBeInTheDocument();
    });
  });

  it('应该显示个人资料卡片', async () => {
    render(<ProfilePage />);

    await waitFor(() => {
      expect(screen.getByTestId('profile-card')).toBeInTheDocument();
    });
  });

  it('应该显示快捷操作按钮', async () => {
    render(<ProfilePage />);

    await waitFor(() => {
      expect(screen.getByText('快捷操作')).toBeInTheDocument();
      expect(screen.getByText('编辑资料')).toBeInTheDocument();
      expect(screen.getByText('积分历史')).toBeInTheDocument();
      expect(screen.getByText('我的求助')).toBeInTheDocument();
      expect(screen.getByText('我的回答')).toBeInTheDocument();
    });
  });

  it('应该显示活动统计', async () => {
    render(<ProfilePage />);

    await waitFor(() => {
      expect(screen.getByText('活动统计')).toBeInTheDocument();
      expect(screen.getByText('总积分')).toBeInTheDocument();
      expect(screen.getByText('150')).toBeInTheDocument();
      expect(screen.getByText('发起求助')).toBeInTheDocument();
      expect(screen.getByText('5')).toBeInTheDocument();
      expect(screen.getByText('提供回答')).toBeInTheDocument();
      expect(screen.getByText('10')).toBeInTheDocument();
      expect(screen.getByText('被采纳回答')).toBeInTheDocument();
      expect(screen.getByText('3')).toBeInTheDocument();
    });
  });

  it('应该计算采纳率', async () => {
    render(<ProfilePage />);

    await waitFor(() => {
      // 3/10 * 100 = 30%
      expect(screen.getByText('采纳率：30%')).toBeInTheDocument();
    });
  });

  it('应该处理零回答的采纳率', async () => {
    const statsWithZeroAnswers = {
      ...mockStats,
      total_help_answers: 0,
      accepted_answers: 0,
    };

    (getProfileStatistics as any).mockResolvedValue({
      success: true,
      data: statsWithZeroAnswers,
    });

    render(<ProfilePage />);

    await waitFor(() => {
      expect(screen.getByText('采纳率：0%')).toBeInTheDocument();
    });
  });

  it('应该处理编辑资料按钮点击', async () => {
    render(<ProfilePage />);

    await waitFor(() => {
      const editButton = screen.getByTestId('edit-profile-button');
      fireEvent.click(editButton);
      expect(mockPush).toHaveBeenCalledWith('/profile/edit');
    });
  });

  it('应该处理快捷操作按钮点击', async () => {
    render(<ProfilePage />);

    await waitFor(() => {
      const editButton = screen.getByRole('button', { name: '编辑资料' });
      fireEvent.click(editButton);
      expect(mockPush).toHaveBeenCalledWith('/profile/edit');

      const pointsButton = screen.getByRole('button', { name: '积分历史' });
      fireEvent.click(pointsButton);
      expect(mockPush).toHaveBeenCalledWith('/profile/points');

      const requestsButton = screen.getByRole('button', { name: '我的求助' });
      fireEvent.click(requestsButton);
      expect(mockPush).toHaveBeenCalledWith('/profile/help-requests');

      const answersButton = screen.getByRole('button', { name: '我的回答' });
      fireEvent.click(answersButton);
      expect(mockPush).toHaveBeenCalledWith('/profile/help-answers');
    });
  });

  it('应该处理统计卡片点击', async () => {
    render(<ProfilePage />);

    await waitFor(() => {
      // 点击总积分卡片
      const pointsCard = screen.getByText('总积分').closest('div');
      if (pointsCard) {
        fireEvent.click(pointsCard);
        expect(mockPush).toHaveBeenCalledWith('/profile/points');
      }
    });
  });

  it('应该显示最近活动部分', async () => {
    render(<ProfilePage />);

    await waitFor(() => {
      expect(screen.getByText('最近活动')).toBeInTheDocument();
      expect(screen.getByText('查看您最近的活动记录')).toBeInTheDocument();
      expect(screen.getByText('暂无最近活动记录')).toBeInTheDocument();
      expect(screen.getByText('开始使用平台功能来查看活动记录')).toBeInTheDocument();
    });
  });

  it('应该处理重试按钮', async () => {
    (getMyProfile as any).mockResolvedValueOnce({
      success: false,
      message: '获取个人信息失败',
    }).mockResolvedValueOnce({
      success: true,
      data: mockProfile,
    });

    render(<ProfilePage />);

    await waitFor(() => {
      expect(screen.getByText('获取个人信息失败')).toBeInTheDocument();
    });

    const retryButton = screen.getByText('重试');
    fireEvent.click(retryButton);

    await waitFor(() => {
      expect(screen.getByText('个人资料')).toBeInTheDocument();
      expect(screen.queryByText('获取个人信息失败')).not.toBeInTheDocument();
    });
  });

  it('应该处理统计信息获取失败', async () => {
    (getProfileStatistics as any).mockResolvedValue({
      success: false,
      message: '获取统计信息失败',
    });

    render(<ProfilePage />);

    await waitFor(() => {
      // 页面应该正常显示，只是没有统计信息
      expect(screen.getByText('个人资料')).toBeInTheDocument();
      expect(screen.queryByText('活动统计')).not.toBeInTheDocument();
    });
  });

  it('应该处理网络错误', async () => {
    (getMyProfile as any).mockRejectedValue(new Error('Network error'));

    render(<ProfilePage />);

    await waitFor(() => {
      expect(screen.getByText('网络错误，请稍后重试')).toBeInTheDocument();
    });
  });
});
