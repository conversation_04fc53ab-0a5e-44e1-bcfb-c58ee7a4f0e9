import PageHeader from "@/components/PageHeader";

export default function PrivacyPage() {
  return (
    <div className="min-h-screen bg-background">
      <PageHeader 
        title="隐私政策"
        description="97盘搜隐私保护政策"
      />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-card-background rounded-lg shadow-lg p-8 border border-border-color">
            <div className="prose prose-gray dark:prose-invert max-w-none">
              <h2 className="text-xl font-semibold text-foreground mb-4">1. 信息收集</h2>
              <p className="text-secondary-text mb-4">
                我们收集以下类型的信息：
              </p>
              <ul className="list-disc list-inside text-secondary-text mb-4 space-y-2">
                <li>账户信息：用户名、邮箱地址</li>
                <li>使用数据：搜索记录、访问日志</li>
                <li>技术信息：IP地址、浏览器类型、设备信息</li>
              </ul>

              <h2 className="text-xl font-semibold text-foreground mb-4">2. 信息使用</h2>
              <p className="text-secondary-text mb-4">
                我们使用收集的信息用于：
              </p>
              <ul className="list-disc list-inside text-secondary-text mb-4 space-y-2">
                <li>提供和改进我们的服务</li>
                <li>处理用户请求和支持</li>
                <li>发送重要通知和更新</li>
                <li>分析服务使用情况</li>
                <li>防止欺诈和滥用</li>
              </ul>

              <h2 className="text-xl font-semibold text-foreground mb-4">3. 信息分享</h2>
              <p className="text-secondary-text mb-4">
                我们不会向第三方出售、交易或转让您的个人信息，除非：
              </p>
              <ul className="list-disc list-inside text-secondary-text mb-4 space-y-2">
                <li>获得您的明确同意</li>
                <li>法律要求或法院命令</li>
                <li>保护我们的权利和安全</li>
                <li>与可信的服务提供商合作（在严格的保密协议下）</li>
              </ul>

              <h2 className="text-xl font-semibold text-foreground mb-4">4. 数据安全</h2>
              <p className="text-secondary-text mb-4">
                我们采取适当的安全措施来保护您的个人信息：
              </p>
              <ul className="list-disc list-inside text-secondary-text mb-4 space-y-2">
                <li>使用加密技术保护数据传输</li>
                <li>限制员工访问个人信息</li>
                <li>定期更新安全措施</li>
                <li>监控异常活动</li>
              </ul>

              <h2 className="text-xl font-semibold text-foreground mb-4">5. Cookie使用</h2>
              <p className="text-secondary-text mb-4">
                我们使用Cookie和类似技术来：
              </p>
              <ul className="list-disc list-inside text-secondary-text mb-4 space-y-2">
                <li>记住您的登录状态</li>
                <li>保存您的偏好设置</li>
                <li>分析网站使用情况</li>
                <li>改善用户体验</li>
              </ul>

              <h2 className="text-xl font-semibold text-foreground mb-4">6. 您的权利</h2>
              <p className="text-secondary-text mb-4">
                您有权：
              </p>
              <ul className="list-disc list-inside text-secondary-text mb-4 space-y-2">
                <li>访问您的个人信息</li>
                <li>更正不准确的信息</li>
                <li>删除您的账户和数据</li>
                <li>限制信息处理</li>
                <li>数据可携带性</li>
              </ul>

              <h2 className="text-xl font-semibold text-foreground mb-4">7. 数据保留</h2>
              <p className="text-secondary-text mb-4">
                我们只在必要的时间内保留您的个人信息。账户删除后，我们将在合理时间内删除相关数据，除非法律要求我们保留。
              </p>

              <h2 className="text-xl font-semibold text-foreground mb-4">8. 政策更新</h2>
              <p className="text-secondary-text mb-4">
                我们可能会不时更新此隐私政策。重大变更将通过网站通知或邮件通知您。
              </p>

              <h2 className="text-xl font-semibold text-foreground mb-4">9. 联系我们</h2>
              <p className="text-secondary-text mb-4">
                如果您对此隐私政策有任何疑问或关注，请通过官方QQ群联系我们。
              </p>

              <div className="mt-8 pt-4 border-t border-border-color">
                <p className="text-sm text-secondary-text">
                  最后更新时间：2024年1月1日
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
