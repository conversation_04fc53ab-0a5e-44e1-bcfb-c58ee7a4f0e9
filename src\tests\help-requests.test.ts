/**
 * 资源求助功能测试
 * 测试求助相关的核心功能
 */

import { describe, it, expect } from '@jest/globals';
import {
  HelpRequest,
  HelpAnswer,
  CreateHelpRequestData,
  CreateAnswerData,
  PAN_TYPE_MAP,
  RESOURCE_TYPES,
} from '@/types/help-request';
import { DEFAULT_USER_LEVELS, POINTS_RULES } from '@/types/user-level';

describe('资源求助类型定义', () => {
  it('应该正确定义网盘类型映射', () => {
    expect(PAN_TYPE_MAP[1]).toBe('百度网盘');
    expect(PAN_TYPE_MAP[2]).toBe('夸克网盘');
    expect(PAN_TYPE_MAP[3]).toBe('阿里云盘');
    expect(PAN_TYPE_MAP[4]).toBe('迅雷网盘');
  });

  it('应该正确定义资源类型', () => {
    expect(RESOURCE_TYPES).toHaveLength(9);
    expect(RESOURCE_TYPES[0]).toEqual({ value: 'video', label: '视频' });
    expect(RESOURCE_TYPES[8]).toEqual({ value: 'other', label: '其他' });
  });

  it('应该正确验证求助数据结构', () => {
    const helpRequest: HelpRequest = {
      id: 1,
      title: '测试求助',
      description: '这是一个测试求助',
      resource_types: ['video'],
      pan_types: [1, 2],
      status: 'open',
      user_id: 1,
      user: {
        id: 1,
        username: 'testuser',
        level: 1,
      },
      answers_count: 0,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      view_count: 0,
    };

    expect(helpRequest.title).toBe('测试求助');
    expect(helpRequest.pan_types).toContain(1);
    expect(helpRequest.pan_types).toContain(2);
    expect(helpRequest.status).toBe('open');
  });

  it('应该正确验证回答数据结构', () => {
    const answer: HelpAnswer = {
      id: 1,
      help_request_id: 1,
      user_id: 2,
      user: {
        id: 2,
        username: 'helper',
        level: 2,
      },
      resource_link: 'https://pan.baidu.com/s/test',
      pan_type: 1,
      description: '这是资源描述',
      is_parsed: false,
      is_best: false,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    };

    expect(answer.resource_link).toBe('https://pan.baidu.com/s/test');
    expect(answer.pan_type).toBe(1);
    expect(answer.is_best).toBe(false);
  });
});

describe('用户等级系统', () => {
  it('应该正确定义用户等级', () => {
    expect(DEFAULT_USER_LEVELS).toHaveLength(6);
    expect(DEFAULT_USER_LEVELS[0].title).toBe('新手');
    expect(DEFAULT_USER_LEVELS[5].title).toBe('大师');
  });

  it('应该正确定义积分规则', () => {
    expect(POINTS_RULES.help_request_created).toBe(5);
    expect(POINTS_RULES.answer_created).toBe(10);
    expect(POINTS_RULES.answer_adopted).toBe(50);
    expect(POINTS_RULES.help_request_solved).toBe(20);
  });

  it('应该正确计算用户等级', () => {
    const getUserLevel = (points: number) => {
      return DEFAULT_USER_LEVELS.find(level => 
        points >= level.min_points && points <= level.max_points
      ) || DEFAULT_USER_LEVELS[0];
    };

    expect(getUserLevel(0).title).toBe('新手');
    expect(getUserLevel(150).title).toBe('初学者');
    expect(getUserLevel(500).title).toBe('活跃用户');
    expect(getUserLevel(1000).title).toBe('资深用户');
    expect(getUserLevel(2000).title).toBe('专家');
    expect(getUserLevel(5000).title).toBe('大师');
  });
});

describe('表单验证', () => {
  it('应该验证求助创建数据', () => {
    const validData: CreateHelpRequestData = {
      title: '寻找某个资源',
      description: '详细描述',
      resource_types: ['video'],
      pan_types: [1, 2],
      tags: ['测试'],
    };

    expect(validData.title.length).toBeGreaterThan(0);
    expect(validData.pan_types.length).toBeGreaterThan(0);
  });

  it('应该验证回答创建数据', () => {
    const validData: CreateAnswerData = {
      help_request_id: 1,
      resource_link: 'https://pan.baidu.com/s/test',
      pan_type: 1,
      description: '资源描述',
      is_parsed: false,
    };

    expect(validData.resource_link).toMatch(/^https?:\/\//);
    expect(validData.pan_type).toBeGreaterThan(0);
  });

  it('应该检测无效的求助数据', () => {
    const invalidData = {
      title: '', // 空标题
      pan_types: [], // 空网盘类型
    };

    expect(invalidData.title.length).toBe(0);
    expect(invalidData.pan_types.length).toBe(0);
  });
});

describe('权限控制', () => {
  it('应该正确检查查看权限', () => {
    // 查看权限对所有人开放
    const canView = true; // 游客也可以查看
    expect(canView).toBe(true);
  });

  it('应该正确检查创建权限', () => {
    const isAuthenticated = true;
    const canCreate = isAuthenticated;
    expect(canCreate).toBe(true);

    const isGuest = false;
    const guestCanCreate = isGuest;
    expect(guestCanCreate).toBe(false);
  });

  it('应该正确检查采纳权限', () => {
    const helpRequest: HelpRequest = {
      id: 1,
      title: '测试',
      pan_types: [1],
      status: 'open',
      user_id: 1,
      user: { id: 1, username: 'owner', level: 1 },
      answers_count: 0,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      view_count: 0,
    };

    const currentUserId = 1;
    const canAdopt = currentUserId === helpRequest.user_id && helpRequest.status === 'open';
    expect(canAdopt).toBe(true);

    const otherUserId = 2;
    const otherCanAdopt = otherUserId === helpRequest.user_id;
    expect(otherCanAdopt).toBe(false);
  });

  it('应该正确检查删除权限', () => {
    const helpRequest: HelpRequest = {
      id: 1,
      title: '测试',
      pan_types: [1],
      status: 'open',
      user_id: 1,
      user: { id: 1, username: 'owner', level: 1 },
      answers_count: 0,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      view_count: 0,
    };

    // 求助者可以删除
    const ownerId = 1;
    const ownerCanDelete = ownerId === helpRequest.user_id;
    expect(ownerCanDelete).toBe(true);

    // 管理员可以删除
    const isAdmin = true;
    const adminCanDelete = isAdmin;
    expect(adminCanDelete).toBe(true);

    // 其他用户不能删除
    const otherUserId = 2;
    const isNotAdmin = false;
    const otherCanDelete = otherUserId === helpRequest.user_id || isNotAdmin;
    expect(otherCanDelete).toBe(false);
  });
});

describe('SEO优化', () => {
  it('应该生成正确的页面标题', () => {
    const helpRequest: HelpRequest = {
      id: 1,
      title: '寻找某个视频资源',
      pan_types: [1, 2],
      status: 'open',
      user_id: 1,
      user: { id: 1, username: 'user', level: 1 },
      answers_count: 0,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      view_count: 0,
    };

    const expectedTitle = `${helpRequest.title} - 资源求助 - 97盘搜`;
    expect(expectedTitle).toBe('寻找某个视频资源 - 资源求助 - 97盘搜');
  });

  it('应该生成正确的描述', () => {
    const helpRequest: HelpRequest = {
      id: 1,
      title: '寻找某个视频资源',
      description: '这是一个详细的资源描述',
      pan_types: [1, 2],
      status: 'open',
      user_id: 1,
      user: { id: 1, username: 'user', level: 1 },
      answers_count: 0,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      view_count: 0,
    };

    const expectedDescription = `求助：${helpRequest.title}。${helpRequest.description}`;
    expect(expectedDescription).toBe('求助：寻找某个视频资源。这是一个详细的资源描述');
  });
});

// 模拟API响应测试
describe('API响应处理', () => {
  it('应该正确处理成功响应', () => {
    const successResponse = {
      success: true,
      data: {
        help_requests: [],
        total: 0,
        page: 1,
        limit: 20,
        total_pages: 0,
      },
      message: '获取成功',
    };

    expect(successResponse.success).toBe(true);
    expect(successResponse.data.help_requests).toEqual([]);
  });

  it('应该正确处理错误响应', () => {
    const errorResponse = {
      success: false,
      data: {
        help_requests: [],
        total: 0,
        page: 1,
        limit: 20,
        total_pages: 0,
      },
      error: '获取失败',
    };

    expect(errorResponse.success).toBe(false);
    expect(errorResponse.error).toBe('获取失败');
  });
});
