"use client";

import { useState, useCallback, useRef } from "react";
import { ResourceDetail } from "@/types/resource";

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

interface CacheConfig {
  defaultTTL: number; // 默认缓存时间（毫秒）
  maxSize: number; // 最大缓存条目数
}

/**
 * 客户端资源缓存Hook
 * 提供内存级别的缓存机制，减少重复的API调用
 */
export const useResourceCache = <T = any>(
  config: Partial<CacheConfig> = {}
) => {
  const defaultConfig: CacheConfig = {
    defaultTTL: 5 * 60 * 1000, // 5分钟
    maxSize: 100,
  };

  const finalConfig = { ...defaultConfig, ...config };
  const cacheRef = useRef<Map<string, CacheEntry<T>>>(new Map());
  const [cacheStats, setCacheStats] = useState({ hits: 0, misses: 0 });

  // 清理过期缓存
  const cleanExpiredEntries = useCallback(() => {
    const now = Date.now();
    const cache = cacheRef.current;

    for (const [key, entry] of cache.entries()) {
      if (now > entry.expiresAt) {
        cache.delete(key);
      }
    }
  }, []);

  // 限制缓存大小
  const enforceMaxSize = useCallback(() => {
    const cache = cacheRef.current;
    if (cache.size <= finalConfig.maxSize) return;

    // 删除最旧的条目
    const entries = Array.from(cache.entries());
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

    const toDelete = entries.slice(0, cache.size - finalConfig.maxSize);
    toDelete.forEach(([key]) => cache.delete(key));
  }, [finalConfig.maxSize]);

  // 获取缓存数据
  const get = useCallback(
    (key: string): T | null => {
      cleanExpiredEntries();

      const entry = cacheRef.current.get(key);
      if (!entry) {
        setCacheStats((prev) => ({ ...prev, misses: prev.misses + 1 }));
        return null;
      }

      const now = Date.now();
      if (now > entry.expiresAt) {
        cacheRef.current.delete(key);
        setCacheStats((prev) => ({ ...prev, misses: prev.misses + 1 }));
        return null;
      }

      setCacheStats((prev) => ({ ...prev, hits: prev.hits + 1 }));
      return entry.data;
    },
    [cleanExpiredEntries]
  );

  // 设置缓存数据
  const set = useCallback(
    (key: string, data: T, ttl?: number): void => {
      const now = Date.now();
      const timeToLive = ttl || finalConfig.defaultTTL;

      cacheRef.current.set(key, {
        data,
        timestamp: now,
        expiresAt: now + timeToLive,
      });

      enforceMaxSize();
    },
    [finalConfig.defaultTTL, enforceMaxSize]
  );

  // 删除缓存条目
  const remove = useCallback((key: string): boolean => {
    return cacheRef.current.delete(key);
  }, []);

  // 清空所有缓存
  const clear = useCallback((): void => {
    cacheRef.current.clear();
    setCacheStats({ hits: 0, misses: 0 });
  }, []);

  // 获取缓存统计信息
  const getStats = useCallback(() => {
    cleanExpiredEntries();
    return {
      ...cacheStats,
      size: cacheRef.current.size,
      hitRate:
        cacheStats.hits + cacheStats.misses > 0
          ? (
              (cacheStats.hits / (cacheStats.hits + cacheStats.misses)) *
              100
            ).toFixed(2) + "%"
          : "0%",
    };
  }, [cacheStats, cleanExpiredEntries]);

  // 检查是否存在有效缓存
  const has = useCallback(
    (key: string): boolean => {
      return get(key) !== null;
    },
    [get]
  );

  return {
    get,
    set,
    remove,
    clear,
    has,
    getStats,
  };
};

/**
 * 专门用于资源详情的缓存Hook
 */
export const useResourceDetailCache = () => {
  return useResourceCache<ResourceDetail>({
    defaultTTL: 10 * 60 * 1000, // 资源详情缓存10分钟
    maxSize: 50,
  });
};

/**
 * 专门用于资源状态的缓存Hook
 */
export const useResourceStatusCache = () => {
  return useResourceCache<{ valid: boolean; message: string }>({
    defaultTTL: 2 * 60 * 1000, // 资源状态缓存2分钟
    maxSize: 100,
  });
};

/**
 * 专门用于搜索结果的缓存Hook
 */
export const useSearchResultsCache = () => {
  return useResourceCache<any[]>({
    defaultTTL: 5 * 60 * 1000, // 搜索结果缓存5分钟
    maxSize: 30,
  });
};

/**
 * 高级资源缓存Hook
 * 使用多层缓存策略
 */
export const useAdvancedResourceCache = () => {
  const [cacheStats, setCacheStats] = useState({
    hits: 0,
    misses: 0,
    memoryUsage: "0%",
  });

  const updateStats = useCallback(() => {
    // 这里可以从全局缓存管理器获取统计信息
    setCacheStats((prev) => ({ ...prev, memoryUsage: "0%" }));
  }, []);

  const { get, set, remove, clear, has, getStats } = useResourceCache();

  const getCachedData = useCallback(
    async <T>(
      key: string,
      fetcher: () => Promise<T>,
      ttl: number = 5 * 60 * 1000
    ): Promise<T> => {
      // 首先尝试从缓存获取
      const cached = get(key);
      if (cached) {
        setCacheStats((prev) => ({ ...prev, hits: prev.hits + 1 }));
        return cached;
      }

      // 缓存未命中，获取新数据
      setCacheStats((prev) => ({ ...prev, misses: prev.misses + 1 }));
      const data = await fetcher();

      // 缓存新数据
      set(key, data, ttl);
      updateStats();

      return data;
    },
    [get, set, updateStats]
  );

  return {
    getCachedData,
    get,
    set,
    remove,
    clear,
    has,
    getStats: () => ({ ...getStats(), ...cacheStats }),
  };
};
