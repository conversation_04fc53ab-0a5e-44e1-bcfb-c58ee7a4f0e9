/**
 * UI 组件导入测试
 * 验证新创建的 UI 组件能够正确导入
 */

import { describe, it, expect } from "vitest";

describe("UI 组件导入测试", () => {
  it("应该能够正确导入 Avatar 组件", async () => {
    const { Avatar, AvatarImage, AvatarFallback } = await import(
      "@/components/ui/avatar"
    );

    expect(Avatar).toBeDefined();
    expect(AvatarImage).toBeDefined();
    expect(AvatarFallback).toBeDefined();
  });

  it("应该能够正确导入 Badge 组件", async () => {
    const { Badge } = await import("@/components/ui/badge");

    expect(Badge).toBeDefined();
  });

  it("应该能够正确导入 Skeleton 组件", async () => {
    const { Skeleton } = await import("@/components/ui/skeleton");

    expect(Skeleton).toBeDefined();
  });

  it("应该能够正确导入 Button 组件", async () => {
    const { Button } = await import("@/components/ui/Button");

    expect(Button).toBeDefined();
  });

  it("应该能够正确导入 Card 组件", async () => {
    const { Card, CardContent, CardHeader, CardTitle } = await import(
      "@/components/ui/card"
    );

    expect(Card).toBeDefined();
    expect(CardContent).toBeDefined();
    expect(CardHeader).toBeDefined();
    expect(CardTitle).toBeDefined();
  });

  it("应该能够正确导入 Progress 组件", async () => {
    const { Progress } = await import("@/components/ui/progress");

    expect(Progress).toBeDefined();
  });

  it("应该能够正确导入 Textarea 组件", async () => {
    const { Textarea } = await import("@/components/ui/textarea");

    expect(Textarea).toBeDefined();
  });

  it("应该能够正确导入 Tabs 组件", async () => {
    const { Tabs, TabsList, TabsTrigger, TabsContent } = await import(
      "@/components/ui/tabs"
    );

    expect(Tabs).toBeDefined();
    expect(TabsList).toBeDefined();
    expect(TabsTrigger).toBeDefined();
    expect(TabsContent).toBeDefined();
  });

  it("应该能够正确导入 Separator 组件", async () => {
    const { Separator } = await import("@/components/ui/separator");

    expect(Separator).toBeDefined();
  });

  it("应该能够正确导入 Form 组件", async () => {
    const {
      Form,
      FormItem,
      FormLabel,
      FormControl,
      FormDescription,
      FormField,
      FormMessage,
    } = await import("@/components/ui/form");

    expect(Form).toBeDefined();
    expect(FormItem).toBeDefined();
    expect(FormLabel).toBeDefined();
    expect(FormControl).toBeDefined();
    expect(FormDescription).toBeDefined();
    expect(FormField).toBeDefined();
    expect(FormMessage).toBeDefined();
  });
});
