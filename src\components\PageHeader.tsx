'use client';

interface PageHeaderProps {
  title: string;
  description?: string;
}

export default function PageHeader({ title, description }: PageHeaderProps) {
  return (
    <div className="text-center mb-6">
      <h1 className="text-3xl font-bold text-[var(--foreground)]">{title}</h1>
      {description && (
        <p className="mt-2 text-[var(--secondary-text)]">{description}</p>
      )}
    </div>
  );
} 