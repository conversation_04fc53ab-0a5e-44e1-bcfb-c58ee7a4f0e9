import "./globals.css";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { WebsiteStructuredData } from "@/components/StructuredData";
import LoadingOverlay from "@/components/LoadingOverlay";
import ClientNavigationWrapper from "@/components/ClientNavigationWrapper";
import ConditionalLayout from "@/components/ConditionalLayout";
import { ThemeProvider } from "@/components/theme-provider";
import { SITE_CONFIG } from "@/config/constants";
import Script from "next/script";
import { ToastProvider } from "@/components/ToastProvider";
import ErrorHandler from "@/components/ErrorHandler";
import ChunkErrorBoundary from "@/components/ChunkErrorBoundary";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: SITE_CONFIG.title,
  description: SITE_CONFIG.description,
  keywords:
    "网盘搜索,盘搜搜,盘搜,考研资料,考公资料，短剧影视,游戏资源,电影资源,百度网盘,夸克网盘,迅雷网盘,阿里云盘,广播剧,有声小说",
  // 移除全局robots设置，让页面级别的设置生效
  // robots: "index, follow",
  // 或者设置默认值，但允许页面覆盖
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    title: SITE_CONFIG.title,
    description: SITE_CONFIG.description,
    url: SITE_CONFIG.url,
    siteName: SITE_CONFIG.name,
    type: "website",
  },
  metadataBase: new URL(SITE_CONFIG.url),
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <head>
        <meta name="msvalidate.01" content="70ECAC758022A19ECB41FFEC240B1CCE" />
        <meta name="baidu-site-verification" content="codeva-sQ8a1ygmWu" />
        <meta
          name="shenma-site-verification"
          content="f0cc219cc0a8b9e4a82735aaf84d2a5a_1749909079"
        />
        <meta name="sogou_site_verification" content="FcrE7SXOUt" />
        <meta
          name="bytedance-verification-code"
          content="9jxsF7JormKiDnBW9qzG"
        />
        <meta
          httpEquiv="Cache-Control"
          content="no-cache, no-store, must-revalidate"
        />
        <meta httpEquiv="Pragma" content="no-cache" />
        <meta httpEquiv="Expires" content="0" />
        <WebsiteStructuredData
          siteUrl={SITE_CONFIG.url}
          siteName={SITE_CONFIG.name}
        />
        {/* Google Analytics */}
        <Script
          async
          src="https://www.googletagmanager.com/gtag/js?id=G-YCSE2G3288"
        />
        <Script id="google-analytics">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-YCSE2G3288');
          `}
        </Script>

        {/* 百度统计 */}
        <Script id="baidu-analytics">
          {`
            var _hmt = _hmt || [];
            (function() {
              var hm = document.createElement("script");
              hm.src = "https://hm.baidu.com/hm.js?fd3a1b3ad442bde8548894bb5babb351";
              var s = document.getElementsByTagName("script")[0]; 
              s.parentNode.insertBefore(hm, s);
            })();
          `}
        </Script>

        {/* 自动收录 */}
        <Script id="byte-push">
          {`
            (function(){
              var el = document.createElement("script");
              el.src = "https://lf1-cdn-tos.bytegoofy.com/goofy/ttzz/push.js?2fdee77af49225b581a760592480255e0da3031c125e7429fc5275c9527fda6e4a4d40de0dd5fa9b5c2c10f69a3b501dc430e831103b45ce33654fb9f95b006c";
              el.id = "ttzz";
              var s = document.getElementsByTagName("script")[0];
              s.parentNode.insertBefore(el, s);
            })(window)
          `}
        </Script>
      </head>
      <body
        className={`${inter.className} bg-[var(--background)] text-[var(--foreground)] min-h-screen flex flex-col`}
      >
        <ChunkErrorBoundary>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <ToastProvider>
              <ErrorHandler />
              <LoadingOverlay />
              <ClientNavigationWrapper />
              <ConditionalLayout>{children}</ConditionalLayout>
            </ToastProvider>
          </ThemeProvider>
        </ChunkErrorBoundary>
      </body>
    </html>
  );
}
