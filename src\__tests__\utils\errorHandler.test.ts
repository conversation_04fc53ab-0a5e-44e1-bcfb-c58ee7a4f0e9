/**
 * 错误处理器测试
 */

import { ChunkLoadErrorHandler, initChunkLoadErrorHandler } from '@/utils/errorHandler';

// Mock window.location.reload
const mockReload = jest.fn();
Object.defineProperty(window, 'location', {
  value: {
    reload: mockReload,
  },
  writable: true,
});

// Mock console methods
const mockConsoleWarn = jest.spyOn(console, 'warn').mockImplementation();
const mockConsoleLog = jest.spyOn(console, 'log').mockImplementation();

// Mock setTimeout
jest.useFakeTimers();

describe('ChunkLoadErrorHandler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    ChunkLoadErrorHandler.resetReloadAttempts();
    // Clear any existing notifications
    const existingNotification = document.getElementById('chunk-load-error-notification');
    if (existingNotification) {
      existingNotification.remove();
    }
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
    jest.useFakeTimers();
  });

  afterAll(() => {
    mockConsoleWarn.mockRestore();
    mockConsoleLog.mockRestore();
    jest.useRealTimers();
  });

  describe('handle', () => {
    it('should handle ChunkLoadError and attempt reload', () => {
      const chunkError = new Error('Loading chunk 123 failed');
      chunkError.name = 'ChunkLoadError';

      ChunkLoadErrorHandler.handle(chunkError);

      expect(mockConsoleWarn).toHaveBeenCalledWith(
        '检测到ChunkLoadError:',
        'Loading chunk 123 failed'
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        '尝试自动刷新页面 (1/3)'
      );

      // Fast-forward timer
      jest.advanceTimersByTime(1000);
      expect(mockReload).toHaveBeenCalled();
    });

    it('should not handle non-chunk errors', () => {
      const normalError = new Error('Normal error');

      ChunkLoadErrorHandler.handle(normalError);

      expect(mockConsoleWarn).not.toHaveBeenCalled();
      expect(mockReload).not.toHaveBeenCalled();
    });

    it('should show notification after max reload attempts', () => {
      const chunkError = new Error('Loading chunk 123 failed');
      chunkError.name = 'ChunkLoadError';

      // Trigger max reload attempts
      for (let i = 0; i < 3; i++) {
        ChunkLoadErrorHandler.handle(chunkError);
        jest.advanceTimersByTime(1000);
      }

      // Fourth attempt should show notification instead of reloading
      ChunkLoadErrorHandler.handle(chunkError);

      const notification = document.getElementById('chunk-load-error-notification');
      expect(notification).toBeTruthy();
      expect(notification?.textContent).toContain('页面资源加载失败');
    });

    it('should not create duplicate notifications', () => {
      const chunkError = new Error('Loading chunk 123 failed');
      chunkError.name = 'ChunkLoadError';

      // Trigger notification creation multiple times
      for (let i = 0; i < 5; i++) {
        ChunkLoadErrorHandler.handle(chunkError);
      }

      const notifications = document.querySelectorAll('#chunk-load-error-notification');
      expect(notifications.length).toBe(1);
    });

    it('should handle autoReload=false', () => {
      const chunkError = new Error('Loading chunk 123 failed');
      chunkError.name = 'ChunkLoadError';

      ChunkLoadErrorHandler.handle(chunkError, false);

      const notification = document.getElementById('chunk-load-error-notification');
      expect(notification).toBeTruthy();
      expect(mockReload).not.toHaveBeenCalled();
    });
  });

  describe('notification interactions', () => {
    beforeEach(() => {
      const chunkError = new Error('Loading chunk 123 failed');
      chunkError.name = 'ChunkLoadError';
      ChunkLoadErrorHandler.handle(chunkError, false);
    });

    it('should reload page when reload button is clicked', () => {
      const reloadButton = document.getElementById('reload-page-btn');
      expect(reloadButton).toBeTruthy();

      reloadButton?.click();
      expect(mockReload).toHaveBeenCalled();
    });

    it('should close notification when close button is clicked', () => {
      const closeButton = document.getElementById('close-notification-btn');
      const notification = document.getElementById('chunk-load-error-notification');
      
      expect(closeButton).toBeTruthy();
      expect(notification).toBeTruthy();

      closeButton?.click();
      
      const notificationAfterClose = document.getElementById('chunk-load-error-notification');
      expect(notificationAfterClose).toBeNull();
    });

    it('should auto-remove notification after 10 seconds', () => {
      const notification = document.getElementById('chunk-load-error-notification');
      expect(notification).toBeTruthy();

      jest.advanceTimersByTime(10000);

      const notificationAfterTimeout = document.getElementById('chunk-load-error-notification');
      expect(notificationAfterTimeout).toBeNull();
    });
  });

  describe('resetReloadAttempts', () => {
    it('should reset reload attempts counter', () => {
      const chunkError = new Error('Loading chunk 123 failed');
      chunkError.name = 'ChunkLoadError';

      // Make some reload attempts
      ChunkLoadErrorHandler.handle(chunkError);
      ChunkLoadErrorHandler.handle(chunkError);

      // Reset and try again
      ChunkLoadErrorHandler.resetReloadAttempts();
      ChunkLoadErrorHandler.handle(chunkError);

      expect(mockConsoleLog).toHaveBeenLastCalledWith('尝试自动刷新页面 (1/3)');
    });
  });
});

describe('initChunkLoadErrorHandler', () => {
  it('should add event listeners for unhandled errors', () => {
    const addEventListenerSpy = jest.spyOn(window, 'addEventListener');
    
    initChunkLoadErrorHandler();

    expect(addEventListenerSpy).toHaveBeenCalledWith(
      'unhandledrejection',
      expect.any(Function)
    );
    expect(addEventListenerSpy).toHaveBeenCalledWith(
      'error',
      expect.any(Function)
    );

    addEventListenerSpy.mockRestore();
  });
});
