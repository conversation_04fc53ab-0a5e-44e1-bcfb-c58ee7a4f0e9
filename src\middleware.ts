import { NextResponse } from "next/server";

export function middleware() {
  // 获取响应对象
  const response = NextResponse.next();

  // 添加CORS头
  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set(
    "Access-Control-Allow-Methods",
    "GET, POST, PUT, DELETE, OPTIONS"
  );
  response.headers.set(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization"
  );

  return response;
}

export const config = {
  matcher: ["/api/:path*", "/sitemap-resources/:path*"],
};
