"use client";

import Image from "next/image";
import { useState, useEffect } from "react";

interface PublisherInfoProps {
  author: string;
  authorAvatar: string;
}

const PublisherInfo = ({ author, authorAvatar }: PublisherInfoProps) => {
  const defaultAvatar = "/images/default-avatar.png";
  const [imgSrc, setImgSrc] = useState(authorAvatar || defaultAvatar);

  useEffect(() => {
    setImgSrc(authorAvatar || defaultAvatar);
  }, [authorAvatar]);

  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md flex flex-col items-center border border-gray-200 dark:border-gray-700">
      <Image
        src={imgSrc}
        alt={author || "发布者头像"}
        width={64}
        height={64}
        className="rounded-full"
        onError={() => setImgSrc(defaultAvatar)}
      />
      <span className="mt-4 font-medium">发布者: {author || "匿名用户"}</span>
    </div>
  );
};

export default PublisherInfo;
