"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { getMyHelpAnswers } from "@/services/profileService";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/Button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { PageContainer } from "@/components/layout/PageContainer";
import { AuthGuard } from "@/components/AuthGuard";
import { 
  ArrowLeft, 
  MessageSquare, 
  AlertCircle, 
  RefreshCw,
  Calendar,
  Award,
  CheckCircle,
  ThumbsUp,
  Star
} from "lucide-react";

interface HelpAnswer {
  id: number;
  content: string;
  created_at: string;
  updated_at: string;
  is_accepted: boolean;
  likes_count: number;
  help_request: {
    id: number;
    title: string;
    status: string;
  };
}

export default function MyHelpAnswersPage() {
  const router = useRouter();
  
  const [answers, setAnswers] = useState<HelpAnswer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const pageSize = 10;

  useEffect(() => {
    loadHelpAnswers(currentPage);
  }, [currentPage]);

  const loadHelpAnswers = async (page: number) => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await getMyHelpAnswers(page, pageSize);
      
      if (result.success && result.data) {
        setAnswers(result.data.items);
        setTotalPages(result.data.total_pages);
        setTotal(result.data.total);
      } else {
        setError(result.message || "获取回答列表失败");
      }
    } catch (err) {
      setError("网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const truncateContent = (content: string, maxLength: number = 200) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + "...";
  };

  if (loading && answers.length === 0) {
    return (
      <AuthGuard>
        <PageContainer>
          <div className="min-h-[60vh] flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-muted-foreground">加载中...</p>
            </div>
          </div>
        </PageContainer>
      </AuthGuard>
    );
  }

  return (
    <AuthGuard>
      <PageContainer>
        <div className="py-8">
          {/* 页面标题和导航 */}
          <div className="mb-8">
            <Button
              variant="outline"
              onClick={() => router.back()}
              className="mb-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回
            </Button>
            
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-display font-bold flex items-center">
                  <MessageSquare className="h-8 w-8 mr-3 text-blue-600" />
                  我的回答
                </h1>
                <p className="text-muted-foreground mt-2">
                  查看您提供的所有回答
                  {total > 0 && (
                    <span className="ml-2">（共 {total} 条）</span>
                  )}
                </p>
              </div>
              
              <Button onClick={() => router.push("/help-requests")}>
                浏览求助
              </Button>
            </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>{error}</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => loadHelpAnswers(currentPage)}
                  className="ml-4"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  重试
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {/* 统计信息 */}
          {total > 0 && (
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">总回答数</p>
                      <p className="text-2xl font-bold">{total}</p>
                    </div>
                    <MessageSquare className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">被采纳回答</p>
                      <p className="text-2xl font-bold text-green-600">
                        {answers.filter(a => a.is_accepted).length}
                      </p>
                    </div>
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">采纳率</p>
                      <p className="text-2xl font-bold text-orange-600">
                        {total > 0 ? Math.round((answers.filter(a => a.is_accepted).length / total) * 100) : 0}%
                      </p>
                    </div>
                    <Award className="h-8 w-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* 回答列表 */}
          {answers.length === 0 && !loading ? (
            <Card>
              <CardContent className="py-12">
                <div className="text-center">
                  <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-semibold mb-2">暂无回答记录</h3>
                  <p className="text-muted-foreground mb-6">
                    您还没有回答过任何求助请求
                  </p>
                  <Button onClick={() => router.push("/help-requests")}>
                    去回答问题
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {answers.map((answer) => (
                <Card key={answer.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 
                            className="text-lg font-semibold hover:text-blue-600 cursor-pointer"
                            onClick={() => router.push(`/help-requests/${answer.help_request.id}`)}
                          >
                            {answer.help_request.title}
                          </h3>
                          
                          {answer.is_accepted && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                              <Star className="h-3 w-3 mr-1" />
                              已采纳
                            </span>
                          )}
                        </div>
                        
                        <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 mb-3">
                          <p className="text-sm text-muted-foreground mb-1">我的回答：</p>
                          <p className="text-sm">
                            {truncateContent(answer.content)}
                          </p>
                        </div>
                        
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {formatDate(answer.created_at)}
                          </div>
                          
                          {answer.likes_count > 0 && (
                            <div className="flex items-center">
                              <ThumbsUp className="h-4 w-4 mr-1" />
                              {answer.likes_count} 个赞
                            </div>
                          )}
                          
                          {answer.updated_at !== answer.created_at && (
                            <span className="text-xs">
                              已编辑
                            </span>
                          )}
                        </div>
                      </div>
                      
                      <div className="ml-4 flex flex-col space-y-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/help-requests/${answer.help_request.id}`)}
                        >
                          查看详情
                        </Button>
                        
                        {!answer.is_accepted && answer.help_request.status === "open" && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => router.push(`/help-requests/${answer.help_request.id}/answer/${answer.id}/edit`)}
                          >
                            编辑回答
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
              
              {/* 分页 */}
              {totalPages > 1 && (
                <div className="flex justify-center mt-8">
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1 || loading}
                    >
                      上一页
                    </Button>
                    
                    <span className="flex items-center px-4 py-2 text-sm">
                      第 {currentPage} 页，共 {totalPages} 页
                    </span>
                    
                    <Button
                      variant="outline"
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages || loading}
                    >
                      下一页
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </PageContainer>
    </AuthGuard>
  );
}
