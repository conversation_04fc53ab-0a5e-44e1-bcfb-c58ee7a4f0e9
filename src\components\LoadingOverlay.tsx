'use client';

import { useLoadingStore } from '@/store/loadingStore';

export default function LoadingOverlay() {
    const { isLoading } = useLoadingStore();

    if (!isLoading) return null;

    return (
        <div className="fixed top-4 right-4 z-60">
            <div className="bg-white p-3 rounded-lg shadow-lg flex items-center gap-2">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
                <span className="text-sm text-gray-600">处理中...</span>
            </div>
        </div>
    );
} 