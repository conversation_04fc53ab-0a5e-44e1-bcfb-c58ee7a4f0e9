"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { PointsHistory } from "@/components/profile/PointsHistory";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/Button";
import { PageContainer } from "@/components/layout/PageContainer";
import { AuthGuard } from "@/components/AuthGuard";
import { ArrowLeft, Award, TrendingUp, Info } from "lucide-react";

export default function PointsHistoryPage() {
  const router = useRouter();

  return (
    <AuthGuard>
      <PageContainer>
        <div className="py-8">
          {/* 页面标题和导航 */}
          <div className="mb-8">
            <Button
              variant="outline"
              onClick={() => router.back()}
              className="mb-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回
            </Button>
            
            <h1 className="text-3xl font-display font-bold flex items-center">
              <Award className="h-8 w-8 mr-3 text-blue-600" />
              积分历史
            </h1>
            <p className="text-muted-foreground mt-2">
              查看您的积分获得和消费记录
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* 左侧：积分说明 */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <Info className="h-5 w-5 mr-2" />
                    积分说明
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-medium text-green-600 dark:text-green-400 mb-2">
                      获得积分
                    </h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• 注册账户：+10分</li>
                      <li>• 验证邮箱：+5分</li>
                      <li>• 发布求助：+2分</li>
                      <li>• 回答问题：+5分</li>
                      <li>• 回答被采纳：+20分</li>
                      <li>• 每日签到：+1分</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-red-600 dark:text-red-400 mb-2">
                      消费积分
                    </h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• 悬赏求助：-10~100分</li>
                      <li>• 置顶求助：-50分</li>
                      <li>• 加急处理：-20分</li>
                    </ul>
                  </div>

                  <div className="pt-4 border-t">
                    <h4 className="font-medium text-blue-600 dark:text-blue-400 mb-2">
                      等级系统
                    </h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• 新手：0-99分</li>
                      <li>• 活跃用户：100-499分</li>
                      <li>• 资深用户：500-999分</li>
                      <li>• 专家用户：1000+分</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              {/* 积分统计 */}
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <TrendingUp className="h-5 w-5 mr-2" />
                    本月统计
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">获得积分</span>
                    <span className="font-semibold text-green-600 dark:text-green-400">+0</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">消费积分</span>
                    <span className="font-semibold text-red-600 dark:text-red-400">-0</span>
                  </div>
                  <div className="flex justify-between items-center pt-2 border-t">
                    <span className="text-sm font-medium">净增长</span>
                    <span className="font-semibold">0</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 右侧：积分历史记录 */}
            <div className="lg:col-span-3">
              <PointsHistory />
            </div>
          </div>
        </div>
      </PageContainer>
    </AuthGuard>
  );
}
