"use client";

import { memo } from "react";

interface ResourceCardDescriptionProps {
  description: string;
}

/**
 * 资源卡片描述组件
 * 用于展示资源描述信息
 */
const ResourceCardDescription = memo(function ResourceCardDescription({
  description,
}: ResourceCardDescriptionProps) {
  if (!description || description.toLowerCase() === "other") {
    return null;
  }

  return <p className="text-secondary-text mb-4">{description}</p>;
});

export default ResourceCardDescription;
