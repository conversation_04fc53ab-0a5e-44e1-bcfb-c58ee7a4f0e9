"use client";

import { useState, useEffect, useCallback } from "react";
import {
  Bars3Icon,
  MagnifyingGlassIcon,
  Cog6ToothIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import { useToast } from "@/components/ToastProvider";
import ConfigTree from "./ConfigTree";
import ConfigSearch from "./ConfigSearch";
import ConfigEditor from "./ConfigEditor";
import {
  ConfigTreeNode,
  ConfigTreeResponse,
  ConfigSearchResult,
  ConfigSearchResponse,
} from "@/types/config";
import { getConfigTree, searchConfig } from "@/services/configService";

type ViewMode = "tree" | "search";

export default function ConfigManagement() {
  const { showToast } = useToast();

  // 状态管理
  const [viewMode, setViewMode] = useState<ViewMode>("tree");
  const [loading, setLoading] = useState(true);
  const [searchLoading, setSearchLoading] = useState(false);
  const [showSensitive, setShowSensitive] = useState(false);

  // 数据状态
  const [configTree, setConfigTree] = useState<Record<string, ConfigTreeNode>>(
    {}
  );
  const [searchResults, setSearchResults] = useState<ConfigSearchResult[]>([]);
  const [totalNodes, setTotalNodes] = useState(0);
  const [maxDepth, setMaxDepth] = useState(0);

  // 选中和编辑状态
  const [editingNode, setEditingNode] = useState<ConfigTreeNode | null>(null);
  const [selectedPath, setSelectedPath] = useState<string>("");

  // 加载配置树
  const loadConfigTree = useCallback(async () => {
    setLoading(true);
    try {
      const response: ConfigTreeResponse = await getConfigTree(showSensitive);
      setConfigTree(response.tree);
      setTotalNodes(response.total_nodes);
      setMaxDepth(response.max_depth);
    } catch (error) {
      console.error("加载配置树失败:", error);
      showToast("加载配置树失败", "error");
    } finally {
      setLoading(false);
    }
  }, [showSensitive, showToast]);

  // 初始加载
  useEffect(() => {
    loadConfigTree();
  }, [loadConfigTree]);

  // 切换敏感信息显示
  const handleToggleSensitive = useCallback(() => {
    setShowSensitive(!showSensitive);
  }, [showSensitive]);

  // 搜索配置
  const handleSearch = useCallback(
    async (query: string) => {
      setSearchLoading(true);
      try {
        const response: ConfigSearchResponse = await searchConfig(
          query,
          showSensitive
        );
        setSearchResults(response.data.results);
        setViewMode("search");
      } catch {
        showToast("搜索配置失败", "error");
      } finally {
        setSearchLoading(false);
      }
    },
    [showSensitive, showToast]
  );

  // 节点选择处理
  const handleNodeSelect = useCallback((node: ConfigTreeNode) => {
    setSelectedPath(node.path);
  }, []);

  // 搜索结果选择处理
  const handleSearchResultSelect = useCallback((result: ConfigSearchResult) => {
    setSelectedPath(result.path);
  }, []);

  // 编辑处理
  const handleEdit = useCallback(
    (node: ConfigTreeNode | ConfigSearchResult) => {
      let editNode: ConfigTreeNode;

      if ("is_leaf" in node) {
        // 已经是ConfigTreeNode
        editNode = node;
      } else {
        // 是ConfigSearchResult，需要转换
        editNode = {
          key: node.key,
          display_name: node.key,
          path: node.path,
          type: node.type,
          value: node.value,
          comment: node.comment,
          is_leaf: true,
          sensitive: node.sensitive,
          required: false,
          validation_rules: {},
          effect_type: "immediate",
        };
      }

      setEditingNode(editNode);
    },
    []
  );

  // 保存配置后的处理
  const handleConfigSave = useCallback(
    (path: string, value: any) => {
      // 重新加载配置树以获取最新数据
      loadConfigTree();

      // 如果当前在搜索模式，更新搜索结果
      if (viewMode === "search" && searchResults.length > 0) {
        setSearchResults((prev) =>
          prev.map((result) =>
            result.path === path ? { ...result, value } : result
          )
        );
      }
    },
    [loadConfigTree, viewMode, searchResults]
  );

  // 关闭编辑器
  const handleCloseEditor = useCallback(() => {
    setEditingNode(null);
  }, []);

  return (
    <div className="config-management h-full flex flex-col admin-content-container">
      {/* 头部工具栏 */}
      <div className="flex-shrink-0 flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Cog6ToothIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              配置管理
            </h1>
          </div>

          {/* 统计信息 */}
          <div className="hidden md:flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
            <span>总配置项: {totalNodes}</span>
            <span>最大层级: {maxDepth}</span>
            {viewMode === "search" && (
              <span>搜索结果: {searchResults.length}</span>
            )}
          </div>
        </div>

        {/* 视图切换 */}
        <div className="flex items-center space-x-2">
          <button
            type="button"
            onClick={() => setViewMode("tree")}
            className={`flex items-center space-x-2 px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
              viewMode === "tree"
                ? "bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400"
                : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"
            }`}
          >
            <Bars3Icon className="w-4 h-4" />
            <span>树形视图</span>
          </button>

          <button
            type="button"
            onClick={() => setViewMode("search")}
            className={`flex items-center space-x-2 px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
              viewMode === "search"
                ? "bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400"
                : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"
            }`}
          >
            <MagnifyingGlassIcon className="w-4 h-4" />
            <span>搜索视图</span>
          </button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧面板 */}
        <div
          className={`${editingNode ? "w-1/2" : "w-full"} ${
            editingNode ? "border-r border-gray-200 dark:border-gray-700" : ""
          } flex flex-col transition-all duration-300`}
        >
          {loading ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600 dark:text-gray-400">
                  加载配置中...
                </p>
              </div>
            </div>
          ) : viewMode === "tree" ? (
            <div className="flex-1 overflow-auto">
              <ConfigTree
                tree={configTree}
                onNodeSelect={handleNodeSelect}
                onNodeEdit={handleEdit}
                selectedPath={selectedPath}
                showSensitive={showSensitive}
                onToggleSensitive={handleToggleSensitive}
                className="h-full"
              />
            </div>
          ) : (
            <div className="flex-1 overflow-auto">
              <ConfigSearch
                onSearch={handleSearch}
                results={searchResults}
                loading={searchLoading}
                onResultSelect={handleSearchResultSelect}
                onResultEdit={handleEdit}
                showSensitive={showSensitive}
                onToggleSensitive={handleToggleSensitive}
                className="h-full"
              />
            </div>
          )}
        </div>

        {/* 右侧编辑面板 */}
        {editingNode && (
          <div className="w-1/2 flex flex-col">
            <div className="flex-1 overflow-auto">
              <ConfigEditor
                node={editingNode}
                onClose={handleCloseEditor}
                onSave={handleConfigSave}
                className="h-full"
              />
            </div>
          </div>
        )}
      </div>

      {/* 移动端编辑器覆盖层 */}
      {editingNode && (
        <div className="md:hidden fixed inset-0 z-50 bg-white dark:bg-gray-900">
          <ConfigEditor
            node={editingNode}
            onClose={handleCloseEditor}
            onSave={handleConfigSave}
            className="h-full"
          />
        </div>
      )}

      {/* 错误状态 */}
      {!loading &&
        Object.keys(configTree).length === 0 &&
        viewMode === "tree" && (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                无法加载配置数据
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                请检查后端服务是否正常运行
              </p>
              <button
                type="button"
                onClick={loadConfigTree}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                重新加载
              </button>
            </div>
          </div>
        )}
    </div>
  );
}
