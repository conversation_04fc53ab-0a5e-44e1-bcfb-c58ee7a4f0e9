# 友情链接配置说明

## 概述

本文档说明了友情链接功能的配置和使用方法。

## 修改内容

### 1. 菜单栏调整
- **移除**：从主导航菜单中移除了"友情链接"选项
- **位置**：`src/components/Navigation.tsx`
- **影响**：桌面端和移动端菜单都已移除该选项

### 2. 底部导航调整
- **修改**：将底部的"爱搜导航"链接更改为"友情链接"
- **位置**：`src/app/layout.tsx`
- **新链接**：指向 `/links` 页面（内部链接）
- **原链接**：`https://www.esoua.cn`（外部链接）

### 3. 图标支持增强
- **新功能**：友情链接配置现在支持URL图标
- **位置**：`src/components/FriendLinkCard.tsx`
- **实现方式**：
  - 自动检测图标是否为外部URL（以 `http://` 或 `https://` 开头）
  - 外部URL图标使用 `<img>` 标签
  - 本地图标使用 Next.js `<Image>` 组件

## 配置文件

友情链接配置位于 `src/config/constants.ts` 中的 `FRIEND_LINKS` 对象。

### 配置结构

```typescript
export const FRIEND_LINKS = {
  // 网盘服务
  cloudServices: [
    {
      name: "网站名称",
      description: "网站描述",
      url: "https://example.com",
      icon: "/images/local-icon.png" // 本地图标
    }
  ],
  
  // 导航网站
  navigationSites: [
    {
      name: "网站名称",
      description: "网站描述", 
      url: "https://example.com",
      icon: "https://example.com/favicon.ico" // URL图标
    }
  ],
  
  // 工具网站
  toolSites: [
    // 配置结构同上
  ]
};
```

### 图标配置说明

1. **本地图标**：
   - 路径以 `/` 开头，如 `/images/icon.png`
   - 图标文件需放在 `public` 目录下
   - 使用 Next.js Image 组件优化加载

2. **URL图标**：
   - 完整的HTTP/HTTPS URL，如 `https://example.com/favicon.ico`
   - 使用原生 `<img>` 标签加载
   - 支持任何可访问的图片URL

### 示例配置

```typescript
// 使用本地图标
{
  name: "百度网盘",
  description: "百度公司推出的个人云存储服务",
  url: "https://pan.baidu.com/",
  icon: "/images/baidupan.png"
}

// 使用URL图标
{
  name: "GitHub", 
  description: "全球最大的代码托管平台",
  url: "https://github.com/",
  icon: "https://github.com/favicon.ico"
}
```

## 页面访问

- **友情链接页面**：`/links`
- **底部导航**：点击"友情链接"直接跳转到友情链接页面
- **分类展示**：页面按网盘服务、导航网站、工具网站分类展示

## 注意事项

1. **图标加载失败**：组件包含错误处理，图标加载失败时会自动隐藏
2. **外部链接**：所有友情链接都在新标签页中打开
3. **响应式设计**：页面支持各种屏幕尺寸的响应式布局
4. **主题支持**：完全支持明暗主题切换

## 添加新的友情链接

1. 编辑 `src/config/constants.ts` 文件
2. 在相应的分类数组中添加新的链接对象
3. 保存文件，开发服务器会自动重新加载
4. 访问 `/links` 页面查看效果
