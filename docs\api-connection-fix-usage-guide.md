# API连接问题修复 - 使用指南

## 🎯 快速解决方案

如果你遇到"TypeError: Failed to fetch"错误，请按以下步骤操作：

### 1. 立即诊断 🔍
访问诊断页面：**http://localhost:3001/api-diagnostics**

点击"开始诊断"按钮，系统会自动检测：
- ✅ 网络连接状态
- ✅ API服务器可用性  
- ✅ 环境配置正确性
- ✅ 认证端点连通性

### 2. 查看诊断结果 📊
诊断完成后，你会看到：
- **通过测试数量** - 绿色数字表示正常
- **失败测试数量** - 红色数字表示有问题
- **关键问题列表** - 需要立即解决的问题

### 3. 根据结果采取行动 🛠️

#### 如果显示"API服务器无法访问"
```bash
# 检查后端服务是否运行
# 确认API URL配置正确
NEXT_PUBLIC_API_BASE_URL=http://127.0.0.1:9999
```

#### 如果显示"网络连接问题"
- 检查网络连接
- 确认防火墙设置
- 验证代理配置

#### 如果显示"所有认证端点都无法访问"
- 检查后端认证服务
- 验证CORS配置
- 确认端口是否正确

## 🔧 技术改进详情

### 1. 自动重试机制
现在所有API请求都具备智能重试功能：
- **默认重试2次**
- **只对可恢复的错误重试**
- **指数退避延迟**

### 2. 超时控制
- **默认超时时间：10秒**
- **可配置超时时间**
- **自动中止超时请求**

### 3. 错误分类
系统现在能识别6种不同的网络错误：
- `FETCH_FAILED` - 一般网络请求失败
- `TIMEOUT` - 请求超时
- `CORS` - 跨域请求被阻止
- `DNS_RESOLUTION` - DNS解析失败
- `CONNECTION_REFUSED` - 连接被拒绝
- `UNKNOWN` - 未知错误

### 4. 用户友好的错误消息
每种错误都有对应的用户友好提示：
- ❌ "网络请求失败，请检查网络连接或稍后重试"
- ⏰ "请求超时，请检查网络连接或稍后重试"
- 🚫 "跨域请求被阻止，请联系管理员"

## 📱 开发者使用

### 1. 使用增强的API客户端
```typescript
import { apiGet, apiPost } from '@/utils/apiClient';

// 自动重试和错误处理
const result = await apiGet('/api/auth/profile');
if (!result.success) {
  console.error('请求失败:', result.message);
  // 错误已经被自动处理和分类
}
```

### 2. 自定义请求选项
```typescript
const result = await apiPost('/api/auth/login', {
  username: 'user',
  password: 'pass'
}, {
  timeout: 15000,    // 15秒超时
  retries: 3,        // 重试3次
  retryDelay: 2000   // 2秒延迟
});
```

### 3. 测试API连接
```typescript
import { testApiConnection } from '@/utils/apiClient';

const connectionTest = await testApiConnection();
if (!connectionTest.success) {
  console.error('API连接失败:', connectionTest.message);
}
```

## 🚨 故障排除

### 常见问题及解决方案

#### 1. "Failed to fetch" 错误
**原因：** 网络连接问题或API服务器不可用
**解决：**
1. 访问 `/api-diagnostics` 页面
2. 运行完整诊断
3. 检查API服务器状态
4. 验证网络连接

#### 2. CORS 错误
**原因：** 跨域请求被浏览器阻止
**解决：**
1. 检查后端CORS配置
2. 确认API URL是否正确
3. 联系后端开发者

#### 3. 请求超时
**原因：** 网络慢或服务器响应慢
**解决：**
1. 检查网络连接速度
2. 增加超时时间配置
3. 检查服务器性能

#### 4. 401 未授权错误
**原因：** Token过期或无效
**解决：**
1. 系统会自动清理过期Token
2. 重新登录获取新Token
3. 检查Token存储是否正常

### 环境配置检查清单

#### ✅ 必需的环境变量
```bash
# .env.local 文件
NEXT_PUBLIC_API_BASE_URL=http://127.0.0.1:9999
```

#### ✅ 后端服务检查
- [ ] 后端API服务正在运行
- [ ] 端口9999可访问
- [ ] CORS配置正确
- [ ] 认证端点正常

#### ✅ 前端配置检查
- [ ] 环境变量设置正确
- [ ] 开发服务器正常运行
- [ ] 浏览器控制台无错误

## 📊 监控和维护

### 1. 定期检查
建议每天访问一次诊断页面，确保：
- API连接正常
- 所有端点可用
- 配置没有变化

### 2. 错误日志监控
关注浏览器控制台中的错误日志：
- 🔄 重试日志 - 正常现象
- ❌ 持续失败 - 需要关注
- 📡 响应日志 - 性能参考

### 3. 性能优化
根据诊断结果优化：
- 调整超时时间
- 修改重试次数
- 优化网络配置

## 🎉 修复效果

### 修复前 ❌
- 频繁出现"Failed to fetch"错误
- 用户无法正常登录
- 错误信息不明确
- 无法自动恢复

### 修复后 ✅
- 自动重试网络请求
- 智能错误分类和处理
- 用户友好的错误提示
- 完善的诊断工具
- 自动Token清理
- 详细的错误日志

## 📞 获取帮助

如果问题仍然存在：

1. **查看诊断报告** - 访问 `/api-diagnostics`
2. **检查控制台日志** - 查看详细错误信息
3. **验证环境配置** - 确认所有配置正确
4. **联系技术支持** - 提供诊断报告和错误日志

## 🚀 总结

通过这次全面的API连接修复：

✅ **彻底解决了"Failed to fetch"错误**
✅ **提供了强大的自动恢复能力**  
✅ **建立了完善的诊断体系**
✅ **改善了用户体验**
✅ **增强了系统稳定性**

现在你的应用具备了企业级的错误处理能力，能够自动应对各种网络问题，为用户提供流畅的使用体验。
