"use client";

import { Suspense } from "react";
import AdminLayout from "@/components/admin/AdminLayout";
import ConfigManagement from "@/components/admin/config/ConfigManagement";
import { AdminGuard } from "@/components/AuthGuard";

// 加载组件
function ConfigLoadingFallback() {
  return (
    <div className="h-full flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600 dark:text-gray-400">
          正在加载配置管理界面...
        </p>
      </div>
    </div>
  );
}

/**
 * 配置管理页面
 * 提供系统配置的可视化管理界面
 */
export default function ConfigPage() {
  return (
    <AdminGuard>
      <AdminLayout>
        <Suspense fallback={<ConfigLoadingFallback />}>
          <ConfigManagement />
        </Suspense>
      </AdminLayout>
    </AdminGuard>
  );
}
