import { NextRequest, NextResponse } from "next/server";
import fs from "fs/promises";
import path from "path";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const filepath = searchParams.get("filepath");

    if (!filepath) {
      return NextResponse.json(
        { error: "缺少必要的 filepath 参数" },
        { status: 400 }
      );
    }

    try {
      // 获取项目根目录
      const rootDir = process.cwd();

      // 安全解码文件路径
      let decodedFilepath;
      try {
        decodedFilepath = decodeURIComponent(filepath);
      } catch (e) {
        console.error("URL解码错误:", e);
        return NextResponse.json(
          {
            error:
              "URL解码错误: " + (e instanceof Error ? e.message : String(e)),
          },
          { status: 400 }
        );
      }

      // 拼接完整路径，确保正确处理中文路径
      const fullPath = path.join(rootDir, decodedFilepath);

      // 限制只能访问 data 目录下的文件，防止安全问题
      if (!fullPath.includes(path.join(rootDir, "src", "data"))) {
        return NextResponse.json({ error: "无权访问此文件" }, { status: 403 });
      }

      // 检查文件是否存在
      try {
        await fs.access(fullPath);
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (e) {
        return NextResponse.json(
          {
            error: "文件不存在: " + fullPath,
            originalPath: filepath,
            decodedPath: decodedFilepath,
          },
          { status: 404 }
        );
      }

      // 读取文件内容
      const content = await fs.readFile(fullPath, "utf-8");

      // 返回文件内容
      return NextResponse.json({ content });
    } catch (error) {
      console.error("读取 Markdown 文件时出错:", error);
      return NextResponse.json(
        {
          error:
            "服务器内部错误: " +
            (error instanceof Error ? error.message : String(error)),
          originalPath: filepath,
        },
        { status: 500 }
      );
    }
  } catch (outerError) {
    console.error("API处理过程中出现未捕获的错误:", outerError);
    return NextResponse.json(
      {
        error:
          "未知错误: " +
          (outerError instanceof Error
            ? outerError.message
            : String(outerError)),
      },
      { status: 500 }
    );
  }
}
