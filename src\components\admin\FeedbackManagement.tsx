"use client";

import { useState, useEffect, useCallback } from "react";
import { useToast } from "@/components/ToastProvider";
import {
  getAdminFeedbackList,
  markFeedbackAsVerified,
  deleteFeedback,
  AdminFeedback,
  AdminFeedbackListResponse,
} from "@/services/adminFeedbackService";
import DataTable, { Column } from "@/components/admin/DataTable";
import SearchFilter, { FilterField } from "@/components/admin/SearchFilter";
import {
  CheckCircleIcon,
  EyeIcon,
  TrashIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";

export default function FeedbackManagement() {
  const { showToast } = useToast();
  const [feedback, setFeedback] = useState<AdminFeedback[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [invalidTypeFilter, setInvalidTypeFilter] = useState("");
  const [panTypeFilter, setPanTypeFilter] = useState("");
  const [isVerifiedFilter, setIsVerifiedFilter] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalFeedback, setTotalFeedback] = useState(0);
  // 当前搜索关键词状态（用于实际搜索）
  const [currentSearchTerm, setCurrentSearchTerm] = useState("");

  const loadFeedback = useCallback(async () => {
    setLoading(true);
    try {
      const result: AdminFeedbackListResponse = await getAdminFeedbackList(
        currentPage,
        20,
        invalidTypeFilter ? parseInt(invalidTypeFilter) : undefined,
        panTypeFilter ? parseInt(panTypeFilter) : undefined,
        isVerifiedFilter ? isVerifiedFilter === "true" : undefined,
        currentSearchTerm || undefined,
        startDate || undefined,
        endDate || undefined
      );

      if (result.success) {
        setFeedback(result.data?.feedbacks || []);
        setTotalFeedback(result.data?.pagination?.total || 0);
      } else {
        showToast(result.message || "获取反馈列表失败", "error");
        setFeedback([]);
        setTotalFeedback(0);
      }
    } catch {
      showToast("获取反馈列表失败", "error");
      setFeedback([]);
      setTotalFeedback(0);
    } finally {
      setLoading(false);
    }
  }, [
    currentPage,
    currentSearchTerm,
    invalidTypeFilter,
    panTypeFilter,
    isVerifiedFilter,
    startDate,
    endDate,
    showToast,
  ]);

  // 初始加载和筛选条件变化时重新加载（不包括搜索词）
  useEffect(() => {
    loadFeedback();
  }, [loadFeedback]);

  const handleSearch = () => {
    setCurrentSearchTerm(searchTerm);
    setCurrentPage(1);
  };

  const handleMarkAsVerified = async (feedbackId: number) => {
    try {
      const result = await markFeedbackAsVerified(feedbackId);
      if (result.success) {
        showToast(result.message, "success");
        loadFeedback();
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast("标记反馈失败", "error");
    }
  };

  const handleDeleteFeedback = async (feedbackId: number) => {
    if (!confirm("确定要删除这条反馈吗？此操作不可恢复。")) {
      return;
    }

    try {
      const result = await deleteFeedback(feedbackId);
      if (result.success) {
        showToast(result.message, "success");
        loadFeedback();
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast("删除反馈失败", "error");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("zh-CN");
  };

  const getPanTypeName = (panType: number) => {
    const panTypeMap: { [key: number]: string } = {
      1: "百度网盘",
      2: "夸克网盘",
      3: "阿里云盘",
      4: "迅雷网盘",
    };
    return panTypeMap[panType] || "其他";
  };

  const getInvalidTypeName = (invalidType: number) => {
    const invalidTypeMap: { [key: number]: string } = {
      1: "链接错误",
      2: "资源不存在",
      3: "需要密码",
      4: "其他问题",
    };
    return invalidTypeMap[invalidType] || "未知";
  };

  const filters: FilterField[] = [
    {
      key: "invalidType",
      label: "失效类型",
      type: "select",
      value: invalidTypeFilter,
      onChange: setInvalidTypeFilter,
      options: [
        { label: "链接错误", value: "1" },
        { label: "资源不存在", value: "2" },
        { label: "需要密码", value: "3" },
        { label: "其他问题", value: "4" },
      ],
    },
    {
      key: "panType",
      label: "网盘类型",
      type: "select",
      value: panTypeFilter,
      onChange: setPanTypeFilter,
      options: [
        { label: "百度网盘", value: "1" },
        { label: "夸克网盘", value: "2" },
        { label: "阿里云盘", value: "3" },
        { label: "迅雷网盘", value: "4" },
      ],
    },
    {
      key: "isVerified",
      label: "验证状态",
      type: "select",
      value: isVerifiedFilter,
      onChange: setIsVerifiedFilter,
      options: [
        { label: "已验证", value: "true" },
        { label: "未验证", value: "false" },
      ],
    },
    {
      key: "startDate",
      label: "开始日期",
      type: "date",
      value: startDate,
      onChange: setStartDate,
    },
    {
      key: "endDate",
      label: "结束日期",
      type: "date",
      value: endDate,
      onChange: setEndDate,
    },
  ];

  const columns: Column<AdminFeedback>[] = [
    {
      key: "resource_title",
      title: "资源信息",
      render: (_, record) => (
        <div className="min-w-0">
          <div className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate max-w-xs">
            {record.resource_title || "未知资源"}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            ID: {record.resource_id}
          </div>
        </div>
      ),
    },
    {
      key: "pan_type",
      title: "网盘类型",
      render: (panType) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {getPanTypeName(panType)}
        </span>
      ),
    },
    {
      key: "invalid_type",
      title: "失效类型",
      render: (invalidType) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
          {getInvalidTypeName(invalidType)}
        </span>
      ),
    },
    {
      key: "description",
      title: "问题描述",
      render: (description) => (
        <div className="text-sm text-gray-900 dark:text-gray-100 max-w-xs">
          <div className="truncate" title={description || "无描述"}>
            {description || "无描述"}
          </div>
        </div>
      ),
    },
    {
      key: "contact_info",
      title: "联系方式",
      render: (contactInfo) => (
        <div className="text-sm text-gray-600 dark:text-gray-300 max-w-xs">
          <div className="truncate" title={contactInfo || "未提供"}>
            {contactInfo || "未提供"}
          </div>
        </div>
      ),
    },
    {
      key: "is_verified",
      title: "验证状态",
      render: (isVerified) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            isVerified
              ? "bg-green-100 text-green-800"
              : "bg-yellow-100 text-yellow-800"
          }`}
        >
          {isVerified ? (
            <>
              <CheckCircleIcon className="h-3 w-3 mr-1" />
              已验证
            </>
          ) : (
            "未验证"
          )}
        </span>
      ),
    },
    {
      key: "created_at",
      title: "反馈时间",
      render: (date) => (
        <div className="text-sm text-gray-600 dark:text-gray-300">
          <div>{formatDate(date)}</div>
        </div>
      ),
    },
    {
      key: "actions",
      title: "操作",
      render: (_, record) => (
        <div className="flex items-center space-x-1">
          {!record.is_verified && (
            <button
              type="button"
              onClick={() => handleMarkAsVerified(record.id)}
              className="p-2 text-green-600 hover:text-green-700 hover:bg-green-50/80 rounded-lg transition-all duration-200 backdrop-blur-sm"
              title="标记已验证"
            >
              <CheckCircleIcon className="h-4 w-4" />
            </button>
          )}
          <button
            type="button"
            onClick={() =>
              window.open(`/resources/${record.resource_id}`, "_blank")
            }
            className="p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50/80 rounded-lg transition-all duration-200 backdrop-blur-sm"
            title="查看资源"
          >
            <EyeIcon className="h-4 w-4" />
          </button>
          <button
            type="button"
            onClick={() => handleDeleteFeedback(record.id)}
            className="p-2 text-red-600 hover:text-red-700 hover:bg-red-50/80 rounded-lg transition-all duration-200 backdrop-blur-sm"
            title="删除反馈"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      ),
    },
  ];

  return (
    <div className="h-full flex flex-col admin-content-container">
      {/* 主内容区域 - 修复移动端滚动问题 */}
      <div className="flex-1 flex flex-col overflow-hidden bg-background">
        {/* 搜索和筛选区域 - 移动端优化 */}
        <div className="flex-shrink-0 mb-4">
          <div className="admin-form-mobile">
            <SearchFilter
              searchValue={searchTerm}
              onSearchChange={setSearchTerm}
              onSearch={handleSearch}
              filters={filters}
            />
          </div>
        </div>

        {/* 反馈列表区域 - 修复移动端滚动和内容显示 */}
        <div className="flex-1 overflow-auto">
          <DataTable
            columns={columns}
            data={feedback}
            loading={loading}
            pagination={{
              current: currentPage,
              total: totalFeedback,
              pageSize: 20,
              onChange: setCurrentPage,
            }}
            emptyText="暂无反馈数据"
            className="admin-table-mobile"
          />
        </div>
      </div>
    </div>
  );
}
