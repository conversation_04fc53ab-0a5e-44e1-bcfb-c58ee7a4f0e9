'use client';

import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { dracula } from 'react-syntax-highlighter/dist/esm/styles/prism';
import Image from 'next/image';

interface MarkdownRendererProps {
    content: string;
    className?: string;
}

export default function MarkdownRenderer({ content, className = '' }: MarkdownRendererProps) {
    return (
        <div className={`prose prose-lg max-w-none ${className}`}>
            <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                components={{
                    // @ts-ignore
                    code({ inline, className, children, ...props }) {
                        const match = /language-(\w+)/.exec(className || '');
                        return !inline && match ? (
                            // @ts-ignore
                            <SyntaxHighlighter
                                // @ts-ignore
                                style={dracula}
                                language={match[1]}
                                PreTag="div"
                                {...props}
                            >
                                {String(children).replace(/\n$/, '')}
                            </SyntaxHighlighter>
                        ) : (
                            <code className={className} {...props}>
                                {children}
                            </code>
                        );
                    },
                    // @ts-ignore
                    img({ src, alt }) {
                        // 使用 next/image 处理图片渲染
                        if (typeof src === 'string' &&
                            (src.endsWith('.gif') || src.endsWith('.jpg') ||
                                src.endsWith('.png') || src.endsWith('.jpeg'))) {
                            return (
                                <span className="block my-4">
                                    <Image
                                        src={src}
                                        alt={alt || ''}
                                        width={800}
                                        height={600}
                                        className="max-w-full h-auto rounded-lg"
                                        style={{ objectFit: 'contain' }}
                                    />
                                </span>
                            );
                        }
                        return <span>[图片加载失败]</span>;
                    },
                    // @ts-ignore
                    h1({ children }) {
                        return <h1 className="text-3xl font-bold mb-4 text-gray-900">{children}</h1>;
                    },
                    // @ts-ignore
                    h2({ children }) {
                        return <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">{children}</h2>;
                    },
                    // @ts-ignore
                    h3({ children }) {
                        return <h3 className="text-xl font-bold mt-6 mb-3 text-gray-800">{children}</h3>;
                    },
                    // @ts-ignore
                    p({ children }) {
                        // 检查children是否包含div元素
                        // 如果包含React元素且类型为div，则用span替代p
                        const containsDiv = React.Children.toArray(children).some(
                            (child) =>
                                React.isValidElement(child) &&
                                child.type === 'div'
                        );

                        return containsDiv ? (
                            <div className="mb-4 text-gray-700 leading-relaxed">{children}</div>
                        ) : (
                            <p className="mb-4 text-gray-700 leading-relaxed">{children}</p>
                        );
                    },
                    // @ts-ignore
                    ul({ children }) {
                        return <ul className="mb-6 pl-6 list-disc">{children}</ul>;
                    },
                    // @ts-ignore
                    ol({ children }) {
                        return <ol className="mb-6 pl-6 list-decimal">{children}</ol>;
                    },
                    // @ts-ignore
                    a({ href, children }) {
                        return (
                            <a href={href} className="text-blue-600 hover:underline">
                                {children}
                            </a>
                        );
                    }
                }}
            >
                {content}
            </ReactMarkdown>
        </div>
    );
} 