"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import tutorials from "@/data/tutorials.json";

interface Tutorial {
  id: string;
  title: string;
  slug: string;
  date: string;
  filepath: string;
}

interface TutorialWithContent extends Tutorial {
  preview: string;
}

export default function Tutorials() {
  const [searchTerm, setSearchTerm] = useState("");
  const [tutorialsWithPreviews, setTutorialsWithPreviews] = useState<
    TutorialWithContent[]
  >([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTutorialPreviews = async () => {
      try {
        const previews = await Promise.all(
          tutorials.map(async (tutorial: Tutorial) => {
            try {
              // 直接 fetch public 目录下的 md 文件
              const url = `/${tutorial.filepath}`;
              const response = await fetch(url);
              if (response.ok) {
                const content = await response.text();
                // 创建预览，去除Markdown标记并截取前150个字符
                const preview = content
                  .replace(/[#*`\n]/g, " ")
                  .replace(/\s+/g, " ")
                  .trim()
                  .slice(0, 150);

                return {
                  ...tutorial,
                  preview: preview + "...",
                };
              } else {
                return {
                  ...tutorial,
                  preview: "无法加载预览...",
                };
              }
            } catch (error) {
              console.error(`加载教程预览时出错 (${tutorial.title}):`, error);
              return {
                ...tutorial,
                preview: "加载预览时出错...",
              };
            }
          })
        );

        setTutorialsWithPreviews(previews);
        setLoading(false);
      } catch (error) {
        console.error("加载教程预览时出错:", error);
        setLoading(false);
      }
    };

    fetchTutorialPreviews();
  }, []);

  const filteredTutorials = tutorialsWithPreviews.filter(
    (tutorial: TutorialWithContent) =>
      tutorial.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="max-w-4xl mx-auto pt-8">
      <div className="text-center mb-4">
        <h1 className="text-3xl font-bold text-gray-800">教程中心</h1>
      </div>

      <div className="mb-8">
        <input
          type="text"
          placeholder="搜索教程..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full py-2 px-4 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {loading ? (
        <div className="text-center py-10">
          <p className="text-gray-500 text-lg">加载中...</p>
        </div>
      ) : (
        <div className="space-y-6">
          {filteredTutorials.length > 0 ? (
            filteredTutorials.map((tutorial: TutorialWithContent) => (
              <div
                key={tutorial.id}
                className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
              >
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-gray-800 mb-2">
                    <Link
                      href={`/tutorials/${tutorial.slug}`}
                      className="hover:text-blue-600 transition-colors"
                    >
                      {tutorial.title}
                    </Link>
                  </h2>
                  <p className="text-gray-500 text-sm mb-4">
                    发布于: {tutorial.date}
                  </p>
                  <p className="text-gray-600 line-clamp-3">
                    {tutorial.preview}
                  </p>
                  <div className="mt-4">
                    <Link
                      href={`/tutorials/${tutorial.slug}`}
                      className="text-blue-600 hover:underline font-medium"
                    >
                      阅读更多 &rarr;
                    </Link>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-10">
              <p className="text-gray-500 text-lg">没有找到匹配的教程。</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
