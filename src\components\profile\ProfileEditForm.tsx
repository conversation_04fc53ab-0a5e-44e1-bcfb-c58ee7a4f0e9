"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  UserProfile,
  updateMyProfile,
  changeNickname,
  changeEmail,
  changePassword,
} from "@/services/profileService";
import { validateNickname } from "@/utils/errorHandler";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { AlertCircle, CheckCircle, Save, Key, Mail, User } from "lucide-react";

// 表单验证模式
const nicknameSchema = z.object({
  nickname: z
    .string()
    .min(2, "昵称长度不能少于2个字符")
    .max(20, "昵称长度不能超过20个字符")
    .regex(/^[\u4e00-\u9fa5a-zA-Z0-9_]+$/, "昵称只能包含中文、英文、数字和下划线"),
  reason: z.string().optional(),
});

const emailSchema = z.object({
  new_email: z.string().email("请输入有效的邮箱地址"),
  password: z.string().min(1, "请输入当前密码"),
});

const passwordSchema = z.object({
  old_password: z.string().min(1, "请输入当前密码"),
  new_password: z
    .string()
    .min(8, "新密码长度不能少于8个字符")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, "密码必须包含大小写字母和数字"),
  confirm_password: z.string().min(1, "请确认新密码"),
}).refine((data) => data.new_password === data.confirm_password, {
  message: "两次输入的密码不一致",
  path: ["confirm_password"],
});

type NicknameFormData = z.infer<typeof nicknameSchema>;
type EmailFormData = z.infer<typeof emailSchema>;
type PasswordFormData = z.infer<typeof passwordSchema>;

interface ProfileEditFormProps {
  profile: UserProfile;
  onUpdateSuccess?: (updatedProfile: UserProfile) => void;
  onCancel?: () => void;
}

export function ProfileEditForm({
  profile,
  onUpdateSuccess,
  onCancel,
}: ProfileEditFormProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("nickname");

  // 昵称表单
  const nicknameForm = useForm<NicknameFormData>({
    resolver: zodResolver(nicknameSchema),
    defaultValues: {
      nickname: profile.nickname || "",
      reason: "",
    },
  });

  // 邮箱表单
  const emailForm = useForm<EmailFormData>({
    resolver: zodResolver(emailSchema),
    defaultValues: {
      new_email: "",
      password: "",
    },
  });

  // 密码表单
  const passwordForm = useForm<PasswordFormData>({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      old_password: "",
      new_password: "",
      confirm_password: "",
    },
  });

  const clearMessages = () => {
    setError(null);
    setSuccess(null);
  };

  const handleNicknameSubmit = async (data: NicknameFormData) => {
    try {
      setLoading(true);
      clearMessages();

      const result = await changeNickname(data);

      if (result.success) {
        setSuccess(result.message || "昵称修改成功");
        nicknameForm.reset({ nickname: data.nickname, reason: "" });
        
        // 通知父组件更新成功
        if (onUpdateSuccess && result.data) {
          onUpdateSuccess({ ...profile, nickname: data.nickname });
        }
      } else {
        setError(result.message || "昵称修改失败");
      }
    } catch (err) {
      setError("网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  const handleEmailSubmit = async (data: EmailFormData) => {
    try {
      setLoading(true);
      clearMessages();

      const result = await changeEmail(data);

      if (result.success) {
        setSuccess(result.message || "邮箱修改请求已发送，请查收验证邮件");
        emailForm.reset();
      } else {
        setError(result.message || "邮箱修改失败");
      }
    } catch (err) {
      setError("网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordSubmit = async (data: PasswordFormData) => {
    try {
      setLoading(true);
      clearMessages();

      const result = await changePassword({
        old_password: data.old_password,
        new_password: data.new_password,
      });

      if (result.success) {
        setSuccess(result.message || "密码修改成功");
        passwordForm.reset();
      } else {
        setError(result.message || "密码修改失败");
      }
    } catch (err) {
      setError("网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>编辑个人信息</CardTitle>
      </CardHeader>
      
      <CardContent>
        {/* 错误和成功提示 */}
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mb-4 border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              {success}
            </AlertDescription>
          </Alert>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="nickname">
              <User className="h-4 w-4 mr-2" />
              昵称
            </TabsTrigger>
            <TabsTrigger value="email">
              <Mail className="h-4 w-4 mr-2" />
              邮箱
            </TabsTrigger>
            <TabsTrigger value="password">
              <Key className="h-4 w-4 mr-2" />
              密码
            </TabsTrigger>
          </TabsList>

          {/* 昵称修改 */}
          <TabsContent value="nickname" className="space-y-4">
            <Form {...nicknameForm}>
              <form onSubmit={nicknameForm.handleSubmit(handleNicknameSubmit)} className="space-y-4">
                <FormField
                  control={nicknameForm.control}
                  name="nickname"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>昵称</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入昵称" {...field} />
                      </FormControl>
                      <FormDescription>
                        昵称长度2-20个字符，只能包含中文、英文、数字和下划线
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={nicknameForm.control}
                  name="reason"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>修改原因（可选）</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="请简要说明修改昵称的原因"
                          className="resize-none"
                          rows={3}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex space-x-2">
                  <Button type="submit" disabled={loading}>
                    <Save className="h-4 w-4 mr-2" />
                    {loading ? "保存中..." : "保存昵称"}
                  </Button>
                  {onCancel && (
                    <Button type="button" variant="outline" onClick={onCancel}>
                      取消
                    </Button>
                  )}
                </div>
              </form>
            </Form>
          </TabsContent>

          {/* 邮箱修改 */}
          <TabsContent value="email" className="space-y-4">
            <div className="text-sm text-muted-foreground mb-4">
              当前邮箱：{profile.email}
            </div>
            
            <Form {...emailForm}>
              <form onSubmit={emailForm.handleSubmit(handleEmailSubmit)} className="space-y-4">
                <FormField
                  control={emailForm.control}
                  name="new_email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>新邮箱</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="请输入新邮箱地址" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={emailForm.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>当前密码</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="请输入当前密码" {...field} />
                      </FormControl>
                      <FormDescription>
                        为了安全，修改邮箱需要验证当前密码
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex space-x-2">
                  <Button type="submit" disabled={loading}>
                    <Mail className="h-4 w-4 mr-2" />
                    {loading ? "发送中..." : "发送验证邮件"}
                  </Button>
                  {onCancel && (
                    <Button type="button" variant="outline" onClick={onCancel}>
                      取消
                    </Button>
                  )}
                </div>
              </form>
            </Form>
          </TabsContent>

          {/* 密码修改 */}
          <TabsContent value="password" className="space-y-4">
            <Form {...passwordForm}>
              <form onSubmit={passwordForm.handleSubmit(handlePasswordSubmit)} className="space-y-4">
                <FormField
                  control={passwordForm.control}
                  name="old_password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>当前密码</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="请输入当前密码" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={passwordForm.control}
                  name="new_password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>新密码</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="请输入新密码" {...field} />
                      </FormControl>
                      <FormDescription>
                        密码长度至少8个字符，必须包含大小写字母和数字
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={passwordForm.control}
                  name="confirm_password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>确认新密码</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="请再次输入新密码" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex space-x-2">
                  <Button type="submit" disabled={loading}>
                    <Key className="h-4 w-4 mr-2" />
                    {loading ? "修改中..." : "修改密码"}
                  </Button>
                  {onCancel && (
                    <Button type="button" variant="outline" onClick={onCancel}>
                      取消
                    </Button>
                  )}
                </div>
              </form>
            </Form>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
