'use client';

import React, { memo, useCallback } from 'react';
import {
  DocumentDuplicateIcon,
  ArrowTopRightOnSquareIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from "@heroicons/react/24/outline";

interface ResourceActionsProps {
  linkStatus: {
    valid: boolean;
    message: string;
    checking: boolean;
  };
  isLinkLoading: boolean;
  onLinkAction: (action: "copy" | "open") => void;
  onShowFeedback: () => void;
}

/**
 * 资源操作按钮组件
 * 包含复制链接、打开链接、报告失效等操作
 */
const ResourceActions = memo(function ResourceActions({
  linkStatus,
  isLinkLoading,
  onLinkAction,
  onShowFeedback,
}: ResourceActionsProps) {
  const handleCopyClick = useCallback(() => {
    onLinkAction("copy");
  }, [onLinkAction]);

  const handleOpenClick = useCallback(() => {
    onLinkAction("open");
  }, [onLinkAction]);

  const isDisabled = !linkStatus.valid || isLinkLoading || linkStatus.checking;

  return (
    <div className="flex flex-col sm:flex-row gap-3 mb-6">
      {/* 进入网盘按钮 */}
      <button
        onClick={handleOpenClick}
        disabled={isDisabled}
        className={`flex-1 flex items-center justify-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
          isDisabled
            ? "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed"
            : "bg-[var(--button-background)] hover:bg-[var(--button-hover)] text-white shadow-md hover:shadow-lg transform hover:scale-[1.02]"
        }`}
      >
        {isLinkLoading || linkStatus.checking ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            <span>处理中...</span>
          </>
        ) : (
          <>
            <ArrowTopRightOnSquareIcon className="h-5 w-5" />
            <span>进入网盘</span>
          </>
        )}
      </button>

      {/* 复制链接按钮 */}
      <button
        onClick={handleCopyClick}
        disabled={isDisabled}
        className={`flex-1 flex items-center justify-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
          isDisabled
            ? "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed"
            : "bg-[var(--secondary-button-background)] hover:bg-[var(--secondary-button-hover)] text-[var(--secondary-button-text)] shadow-md hover:shadow-lg transform hover:scale-[1.02]"
        }`}
      >
        <DocumentDuplicateIcon className="h-5 w-5" />
        <span>复制链接</span>
      </button>

      {/* 资源失效反馈按钮 */}
      <button
        onClick={onShowFeedback}
        className="px-4 py-2 text-xs bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 rounded-lg hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors border border-red-200 dark:border-red-800"
      >
        <ExclamationTriangleIcon className="h-4 w-4 inline mr-1" />
        资源失效反馈
      </button>

      {/* 状态指示器 */}
      <div className="flex items-center gap-2 px-3 py-2 rounded-lg bg-gray-50 dark:bg-gray-700">
        {linkStatus.checking ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            <span className="text-sm text-gray-600 dark:text-gray-300">检查中...</span>
          </>
        ) : linkStatus.valid ? (
          <>
            <CheckCircleIcon className="h-4 w-4 text-green-500" />
            <span className="text-sm text-green-600 dark:text-green-400">资源有效</span>
          </>
        ) : (
          <>
            <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />
            <span className="text-sm text-red-600 dark:text-red-400">
              {linkStatus.message || "资源无效"}
            </span>
          </>
        )}
      </div>
    </div>
  );
});

export default ResourceActions;
