import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const { links } = await request.json();
    
    if (!links || !Array.isArray(links) || links.length === 0) {
      return NextResponse.json(
        { error: '请提供有效的链接' },
        { status: 400 }
      );
    }
    
    // 模拟处理延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 模拟链接状态检查结果
    const results = links.map(link => ({
      link,
      status: Math.random() > 0.3 ? '有效' : '失效',
      lastChecked: new Date().toISOString(),
    }));
    
    return NextResponse.json({ 
      success: true, 
      results
    });
  } catch (error) {
    console.error('链接查询处理错误:', error);
    return NextResponse.json(
      { error: '服务器处理请求时出错' },
      { status: 500 }
    );
  }
} 