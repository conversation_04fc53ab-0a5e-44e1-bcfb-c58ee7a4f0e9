"use client";

import { memo } from "react";
import CloudLinkButton from "./CloudLinkButton";

interface CloudLinksSectionProps {
  type: string;
  baiduLink?: string;
  quarkLink?: string;
  aliyunLink?: string;
  thunderLink?: string;
  updatedLinks: {
    baiduLink: string;
    quarkLink: string;
    thunderLink: string;
    aliyunLink: string;
  };
  loadingLinks: {
    baidu: boolean;
    quark: boolean;
    thunder: boolean;
    aliyun: boolean;
  };
  resourceStatus: {
    baidu: { error: boolean; message: string };
    quark: { error: boolean; message: string };
    thunder: { error: boolean; message: string };
    aliyun: { error: boolean; message: string };
  };
  linkUpdated: {
    baidu: boolean;
    quark: boolean;
    thunder: boolean;
    aliyun: boolean;
  };
  copied: {
    baiduLink: boolean;
    quarkLink: boolean;
    thunderLink: boolean;
    aliyunLink: boolean;
  };
  isDisabled: boolean;
  onOpenLink: (
    event: React.MouseEvent<HTMLAnchorElement>,
    type: "baidu" | "quark" | "thunder" | "aliyun"
  ) => void;
  onCopyLink: (
    type: "baiduLink" | "quarkLink" | "thunderLink" | "aliyunLink"
  ) => void;
}

/**
 * 网盘链接区域组件
 * 用于展示所有网盘链接按钮
 */
const CloudLinksSection = memo(function CloudLinksSection({
  type,
  baiduLink,
  quarkLink,
  aliyunLink,
  thunderLink,
  updatedLinks,
  loadingLinks,
  resourceStatus,
  linkUpdated,
  copied,
  isDisabled,
  onOpenLink,
  onCopyLink,
}: CloudLinksSectionProps) {
  // 使用当前激活的链接（原始链接或更新后的链接）
  const activeBaiduLink = updatedLinks.baiduLink || baiduLink;
  const activeQuarkLink = updatedLinks.quarkLink || quarkLink;
  const activeThunderLink = updatedLinks.thunderLink || thunderLink;
  const activeAliyunLink = updatedLinks.aliyunLink || aliyunLink;

  return (
    <div className="space-y-3">
      {/* 百度网盘链接，仅type为百度网盘时展示 */}
      {type === "百度网盘" && (
        <CloudLinkButton
          type="baidu"
          link={activeBaiduLink || ""}
          isLoading={loadingLinks.baidu}
          isDisabled={isDisabled}
          hasError={resourceStatus.baidu.error}
          isUpdated={linkUpdated.baidu}
          isCopied={copied.baiduLink}
          onOpenLink={(e) => onOpenLink(e, "baidu")}
          onCopyLink={() => onCopyLink("baiduLink")}
        />
      )}

      {/* 夸克网盘链接，仅type为夸克网盘时展示 */}
      {type === "夸克网盘" && (
        <CloudLinkButton
          type="quark"
          link={activeQuarkLink || ""}
          isLoading={loadingLinks.quark}
          isDisabled={isDisabled}
          hasError={resourceStatus.quark.error}
          isUpdated={linkUpdated.quark}
          isCopied={copied.quarkLink}
          onOpenLink={(e) => onOpenLink(e, "quark")}
          onCopyLink={() => onCopyLink("quarkLink")}
        />
      )}

      {/* 阿里云盘链接 */}
      {type === "阿里云盘" && (
        <CloudLinkButton
          type="aliyun"
          link={activeAliyunLink || ""}
          isLoading={loadingLinks.aliyun}
          isDisabled={isDisabled}
          hasError={resourceStatus.aliyun.error}
          isUpdated={linkUpdated.aliyun}
          isCopied={copied.aliyunLink}
          onOpenLink={(e) => onOpenLink(e, "aliyun")}
          onCopyLink={() => onCopyLink("aliyunLink")}
        />
      )}

      {/* 迅雷网盘链接 */}
      {type === "迅雷网盘" && (
        <CloudLinkButton
          type="thunder"
          link={activeThunderLink || ""}
          isLoading={loadingLinks.thunder}
          isDisabled={isDisabled}
          hasError={resourceStatus.thunder.error}
          isUpdated={linkUpdated.thunder}
          isCopied={copied.thunderLink}
          onOpenLink={(e) => onOpenLink(e, "thunder")}
          onCopyLink={() => onCopyLink("thunderLink")}
        />
      )}
    </div>
  );
});

export default CloudLinksSection;
