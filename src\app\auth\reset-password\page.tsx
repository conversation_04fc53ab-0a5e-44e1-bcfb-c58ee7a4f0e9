"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/hooks/useAuth";
import { resetPassword } from "@/services/authService";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { PageContainer } from "@/components/layout/PageContainer";
import { Eye, EyeOff, Key, AlertCircle, CheckCircle, ArrowLeft } from "lucide-react";

export default function ResetPasswordPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated } = useAuth();
  
  const [formData, setFormData] = useState({
    new_password: "",
    confirm_password: "",
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [token, setToken] = useState<string | null>(null);

  // 获取重置令牌
  useEffect(() => {
    const tokenParam = searchParams.get("token");
    if (!tokenParam) {
      setError("重置链接无效或已过期");
    } else {
      setToken(tokenParam);
    }
  }, [searchParams]);

  // 如果已登录，重定向到首页
  useEffect(() => {
    if (isAuthenticated) {
      router.replace("/");
    }
  }, [isAuthenticated, router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    
    // 清除错误信息
    if (error) setError(null);
  };

  const validateForm = () => {
    if (!formData.new_password) {
      setError("请输入新密码");
      return false;
    }

    if (formData.new_password.length < 8) {
      setError("密码长度不能少于8个字符");
      return false;
    }

    // 检查密码强度
    const hasUpperCase = /[A-Z]/.test(formData.new_password);
    const hasLowerCase = /[a-z]/.test(formData.new_password);
    const hasNumbers = /\d/.test(formData.new_password);
    
    if (!hasUpperCase || !hasLowerCase || !hasNumbers) {
      setError("密码必须包含大小写字母和数字");
      return false;
    }

    if (formData.new_password !== formData.confirm_password) {
      setError("两次输入的密码不一致");
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!token) {
      setError("重置链接无效或已过期");
      return;
    }

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await resetPassword({
        token,
        new_password: formData.new_password,
        confirm_password: formData.confirm_password,
      });
      
      if (result.success) {
        setSuccess(result.message || "密码重置成功");
        
        // 3秒后跳转到登录页面
        setTimeout(() => {
          router.push("/auth/login?message=password_reset_success");
        }, 3000);
      } else {
        setError(result.message || "密码重置失败");
      }
    } catch (err) {
      setError("网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  // 如果已登录，显示加载状态
  if (isAuthenticated) {
    return (
      <PageContainer>
        <div className="min-h-[60vh] flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-muted-foreground">正在跳转...</p>
          </div>
        </div>
      </PageContainer>
    );
  }

  // 如果没有token，显示错误页面
  if (!token && !loading) {
    return (
      <PageContainer>
        <div className="min-h-[80vh] flex items-center justify-center py-12">
          <div className="w-full max-w-md">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                  <h2 className="text-xl font-semibold mb-2">链接无效</h2>
                  <p className="text-muted-foreground mb-6">
                    重置密码链接无效或已过期。请重新申请密码重置。
                  </p>
                  <div className="space-y-2">
                    <Button asChild className="w-full">
                      <Link href="/auth/forgot-password">
                        重新申请密码重置
                      </Link>
                    </Button>
                    <Button variant="outline" asChild className="w-full">
                      <Link href="/auth/login">
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        返回登录
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <div className="min-h-[80vh] flex items-center justify-center py-12">
        <div className="w-full max-w-md">
          <Card>
            <CardHeader className="text-center">
              <CardTitle className="text-2xl font-display">重置密码</CardTitle>
              <CardDescription>
                请输入您的新密码
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              {/* 错误提示 */}
              {error && (
                <Alert variant="destructive" className="mb-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {/* 成功提示 */}
              {success && (
                <Alert variant="success" className="mb-4">
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    <div>
                      <p>{success}</p>
                      <p className="text-xs mt-1 opacity-80">
                        3秒后自动跳转到登录页面...
                      </p>
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="new_password">新密码</Label>
                  <div className="relative">
                    <Input
                      id="new_password"
                      name="new_password"
                      type={showPassword ? "text" : "password"}
                      placeholder="请输入新密码（至少8个字符）"
                      value={formData.new_password}
                      onChange={handleInputChange}
                      disabled={loading || success !== null}
                      required
                      className="pr-10"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                      disabled={loading || success !== null}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    密码必须包含大小写字母和数字，长度至少8个字符
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirm_password">确认新密码</Label>
                  <div className="relative">
                    <Input
                      id="confirm_password"
                      name="confirm_password"
                      type={showConfirmPassword ? "text" : "password"}
                      placeholder="请再次输入新密码"
                      value={formData.confirm_password}
                      onChange={handleInputChange}
                      disabled={loading || success !== null}
                      required
                      className="pr-10"
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                      disabled={loading || success !== null}
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                </div>

                <Button
                  type="submit"
                  className="w-full"
                  disabled={loading || success !== null}
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      重置中...
                    </>
                  ) : (
                    <>
                      <Key className="h-4 w-4 mr-2" />
                      重置密码
                    </>
                  )}
                </Button>
              </form>

              <div className="mt-6 text-center">
                <Link
                  href="/auth/login"
                  className="inline-flex items-center text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 font-medium transition-colors"
                >
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  返回登录
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </PageContainer>
  );
}
