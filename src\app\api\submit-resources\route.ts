import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const { resources } = await request.json();
    
    if (!resources || !Array.isArray(resources) || resources.length === 0) {
      return NextResponse.json(
        { error: '请提供有效的资源链接' },
        { status: 400 }
      );
    }
    
    // 这里可以添加资源验证和存储逻辑
    // 例如，将资源保存到数据库
    
    // 模拟处理延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return NextResponse.json({ 
      success: true, 
      message: '资源提交成功',
      count: resources.length
    });
  } catch (error) {
    console.error('资源提交处理错误:', error);
    return NextResponse.json(
      { error: '服务器处理请求时出错' },
      { status: 500 }
    );
  }
} 