import { NextResponse } from "next/server";
import { getTotalResourceCountFromBackend } from "@/lib/resource";

export async function GET() {
  try {
    const total = await getTotalResourceCountFromBackend();
    // 站点地图索引生成器期望的字段是 `total`
    return NextResponse.json({ total });
  } catch (error) {
    console.error("在 /api/resource-count 路由中发生未知错误:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}
