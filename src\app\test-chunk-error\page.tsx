'use client';

import { useState } from 'react';

/**
 * 测试ChunkLoadError处理的页面
 */
export default function TestChunkErrorPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string>('');

  // 模拟ChunkLoadError
  const simulateChunkError = () => {
    setLoading(true);
    setResult('');

    // 创建一个模拟的ChunkLoadError
    const chunkError = new Error('Loading chunk 123 failed');
    chunkError.name = 'ChunkLoadError';

    setTimeout(() => {
      setLoading(false);
      // 抛出ChunkLoadError来测试错误边界
      throw chunkError;
    }, 1000);
  };

  // 测试动态导入
  const testDynamicImport = async () => {
    setLoading(true);
    setResult('');

    try {
      // 尝试动态导入一个不存在的模块
      await import('./non-existent-module');
      setResult('导入成功（不应该看到这个）');
    } catch (error) {
      console.error('动态导入失败:', error);
      setResult(`动态导入失败: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  // 测试登录功能
  const testLogin = async () => {
    setLoading(true);
    setResult('');

    try {
      // 动态导入登录服务
      const { login } = await import('@/services/authService');
      
      // 使用测试凭据
      const result = await login({
        username: '<EMAIL>',
        password: 'testpassword',
        remember_me: false
      });

      setResult(`登录测试结果: ${result.success ? '成功' : '失败'} - ${result.message}`);
    } catch (error) {
      console.error('登录测试失败:', error);
      setResult(`登录测试失败: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
      <div className="max-w-2xl mx-auto px-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            ChunkLoadError 测试页面
          </h1>

          <div className="space-y-4">
            <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
              <h2 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
                ⚠️ 测试说明
              </h2>
              <p className="text-yellow-700 dark:text-yellow-300 text-sm">
                这个页面用于测试ChunkLoadError的处理机制。点击下面的按钮来模拟不同的错误场景。
              </p>
            </div>

            <div className="grid gap-4">
              <button
                onClick={simulateChunkError}
                disabled={loading}
                className="w-full bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white font-medium py-3 px-4 rounded-md transition-colors"
              >
                {loading ? '处理中...' : '模拟 ChunkLoadError'}
              </button>

              <button
                onClick={testDynamicImport}
                disabled={loading}
                className="w-full bg-orange-600 hover:bg-orange-700 disabled:bg-orange-400 text-white font-medium py-3 px-4 rounded-md transition-colors"
              >
                {loading ? '处理中...' : '测试动态导入失败'}
              </button>

              <button
                onClick={testLogin}
                disabled={loading}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-3 px-4 rounded-md transition-colors"
              >
                {loading ? '处理中...' : '测试登录服务导入'}
              </button>
            </div>

            {result && (
              <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-md">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  测试结果:
                </h3>
                <pre className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                  {result}
                </pre>
              </div>
            )}

            <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
              <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">
                💡 修复措施
              </h3>
              <ul className="text-blue-700 dark:text-blue-300 text-sm space-y-1">
                <li>• 添加了ChunkErrorBoundary错误边界组件</li>
                <li>• 优化了webpack配置以减少chunk分割</li>
                <li>• 登录成功后强制刷新页面避免chunk问题</li>
                <li>• 移除了可能导致循环依赖的动态导入</li>
                <li>• 添加了全局错误处理器</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
