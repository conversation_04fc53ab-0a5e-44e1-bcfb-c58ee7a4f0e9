/**
 * 求助筛选器组件
 * 提供状态、网盘类型、资源类型等筛选功能
 */

import { useState } from "react";
import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import {
  HelpRequestFilters as FiltersType,
  PAN_TYPE_MAP,
  RESOURCE_TYPES,
} from "@/types/help-request";

interface HelpRequestFiltersProps {
  filters: FiltersType;
  onFiltersChange: (filters: FiltersType) => void;
  onSearch?: () => void;
  showSearch?: boolean;
  className?: string;
}

export default function HelpRequestFilters({
  filters,
  onFiltersChange,
  onSearch,
  showSearch = false,
  className = "",
}: HelpRequestFiltersProps) {
  const [searchQuery, setSearchQuery] = useState(filters.search || "");

  const handleFilterChange = (key: keyof FiltersType, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const handleSearch = () => {
    // 将搜索关键词添加到筛选器中
    onFiltersChange({
      ...filters,
      search: searchQuery,
    });

    if (onSearch) {
      onSearch();
    }
  };

  const handleReset = () => {
    onFiltersChange({
      status: "all",
      search: undefined,
    });
    setSearchQuery("");
  };

  return (
    <div className={`bg-card-background rounded-lg p-4 ${className}`}>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
        {/* 状态筛选 */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            状态
          </label>
          <select
            value={filters.status || "all"}
            onChange={(e) => handleFilterChange("status", e.target.value)}
            className="w-full px-3 py-2 border border-border-color rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-blue-500"
            title="选择状态筛选条件"
          >
            <option value="all">全部状态</option>
            <option value="open">求助中</option>
            <option value="solved">已解决</option>
            <option value="closed">已关闭</option>
          </select>
        </div>

        {/* 网盘类型筛选 */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            网盘类型
          </label>
          <select
            value={filters.pan_type || ""}
            onChange={(e) =>
              handleFilterChange(
                "pan_type",
                e.target.value ? parseInt(e.target.value) : undefined
              )
            }
            className="w-full px-3 py-2 border border-border-color rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-blue-500"
            title="选择网盘类型筛选条件"
          >
            <option value="">全部网盘</option>
            {Object.entries(PAN_TYPE_MAP).map(([key, value]) => (
              <option key={key} value={key}>
                {value}
              </option>
            ))}
          </select>
        </div>

        {/* 资源类型筛选 */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            资源类型
          </label>
          <select
            value={filters.resource_type || ""}
            onChange={(e) =>
              handleFilterChange("resource_type", e.target.value || undefined)
            }
            className="w-full px-3 py-2 border border-border-color rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-blue-500"
            title="选择资源类型筛选条件"
          >
            <option value="">全部类型</option>
            {RESOURCE_TYPES.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* 重置按钮 */}
      <div className="flex justify-end mb-4">
        <button
          type="button"
          onClick={handleReset}
          className="px-4 py-2 border border-border-color text-foreground rounded-md hover:bg-hover-background transition-colors"
        >
          重置筛选
        </button>
      </div>

      {/* 搜索框 */}
      {showSearch && (
        <div className="flex gap-2">
          <div className="flex-1 relative">
            <input
              type="text"
              placeholder="搜索求助标题或描述..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === "Enter" && handleSearch()}
              className="w-full px-3 py-2 pl-10 border border-border-color rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <MagnifyingGlassIcon className="h-5 w-5 text-secondary-text absolute left-3 top-1/2 transform -translate-y-1/2" />
          </div>
          <button
            type="button"
            onClick={handleSearch}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            搜索
          </button>
        </div>
      )}
    </div>
  );
}

// 简化版筛选器，只包含基本筛选项
export function SimpleHelpRequestFilters({
  filters,
  onFiltersChange,
  className = "",
}: {
  filters: FiltersType;
  onFiltersChange: (filters: FiltersType) => void;
  className?: string;
}) {
  const handleFilterChange = (key: keyof FiltersType, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  return (
    <div className={`flex flex-wrap gap-3 ${className}`}>
      {/* 状态筛选 */}
      <select
        value={filters.status || "all"}
        onChange={(e) => handleFilterChange("status", e.target.value)}
        className="px-3 py-2 border border-border-color rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
        title="选择状态筛选条件"
      >
        <option value="all">全部状态</option>
        <option value="open">求助中</option>
        <option value="solved">已解决</option>
        <option value="closed">已关闭</option>
      </select>
    </div>
  );
}
