"use client";

import { useState, useCallback } from "react";
import {
  ChevronRightIcon,
  ChevronDownIcon,
  EyeIcon,
  EyeSlashIcon,
  PencilIcon,
  InformationCircleIcon,
} from "@heroicons/react/24/outline";
import { ConfigTreeNode } from "@/types/config";
import {
  formatConfigValue,
  getEffectTypeText,
  getEffectTypeColor,
} from "@/services/configService";

interface ConfigTreeProps {
  tree: Record<string, ConfigTreeNode>;
  onNodeSelect?: (node: ConfigTreeNode) => void;
  onNodeEdit?: (node: ConfigTreeNode) => void;
  selectedPath?: string;
  showSensitive?: boolean;
  onToggleSensitive?: () => void;
  className?: string;
}

interface ConfigTreeNodeProps {
  node: ConfigTreeNode;
  level: number;
  onNodeSelect?: (node: ConfigTreeNode) => void;
  onNodeEdit?: (node: ConfigTreeNode) => void;
  selectedPath?: string;
  showSensitive?: boolean;
}

function ConfigTreeNodeComponent({
  node,
  level,
  onNodeSelect,
  onNodeEdit,
  selectedPath,
  showSensitive = false,
}: ConfigTreeNodeProps) {
  const [isExpanded, setIsExpanded] = useState(level < 2); // 默认展开前两层
  const isSelected = selectedPath === node.path;
  const hasChildren = node.children && Object.keys(node.children).length > 0;

  const handleToggle = useCallback(() => {
    if (hasChildren) {
      setIsExpanded(!isExpanded);
    }
  }, [hasChildren, isExpanded]);

  const handleSelect = useCallback(() => {
    onNodeSelect?.(node);
  }, [node, onNodeSelect]);

  const handleEdit = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      onNodeEdit?.(node);
    },
    [node, onNodeEdit]
  );

  // 获取层级缩进类名，最大支持10层
  const getLevelClass = (level: number) => {
    const maxLevel = Math.min(level, 10);
    return `config-tree-level-${maxLevel}`;
  };

  return (
    <div className="config-tree-node">
      {/* 节点内容 */}
      <div
        className={`flex items-center py-2 px-3 cursor-pointer transition-colors rounded-lg mx-2 ${getLevelClass(
          level
        )} ${
          isSelected
            ? "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700"
            : "hover:bg-gray-50 dark:hover:bg-gray-800/50"
        }`}
        onClick={handleSelect}
      >
        {/* 展开/折叠图标 */}
        <div className="flex-shrink-0 w-5 h-5 mr-2">
          {hasChildren ? (
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                handleToggle();
              }}
              className="w-full h-full flex items-center justify-center text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              {isExpanded ? (
                <ChevronDownIcon className="w-4 h-4" />
              ) : (
                <ChevronRightIcon className="w-4 h-4" />
              )}
            </button>
          ) : (
            <div className="w-4 h-4" />
          )}
        </div>

        {/* 节点信息 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center min-w-0">
              {/* 节点名称 */}
              <span className="font-medium text-gray-900 dark:text-gray-100 truncate">
                {node.display_name}
              </span>

              {/* 敏感信息标识 */}
              {node.sensitive && (
                <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                  敏感
                </span>
              )}

              {/* 必填标识 */}
              {node.required && <span className="ml-1 text-red-500">*</span>}
            </div>

            {/* 操作按钮 */}
            <div className="flex items-center space-x-1 ml-2">
              {node.is_leaf && (
                <button
                  type="button"
                  onClick={handleEdit}
                  className="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                  title="编辑配置"
                >
                  <PencilIcon className="w-4 h-4" />
                </button>
              )}
            </div>
          </div>

          {/* 节点值和类型 */}
          {node.is_leaf && (
            <div className="mt-1 text-sm">
              <div className="flex items-center space-x-2">
                <span className="text-gray-600 dark:text-gray-200">
                  {formatConfigValue(node.value, node.type)}
                </span>
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-100">
                  {node.type}
                </span>
                <span
                  className={`text-xs ${getEffectTypeColor(node.effect_type)}`}
                >
                  {getEffectTypeText(node.effect_type)}
                </span>
              </div>
            </div>
          )}

          {/* 注释 */}
          {node.comment && (
            <div className="mt-1 text-xs text-gray-500 dark:text-gray-400 italic">
              {node.comment}
            </div>
          )}

          {/* 路径 */}
          <div className="mt-1 text-xs text-gray-400 dark:text-gray-500 font-mono">
            {node.path}
          </div>
        </div>
      </div>

      {/* 子节点 */}
      {hasChildren && isExpanded && (
        <div className="config-tree-children">
          {Object.entries(node.children!).map(([key, childNode]) => (
            <ConfigTreeNodeComponent
              key={key}
              node={childNode}
              level={level + 1}
              onNodeSelect={onNodeSelect}
              onNodeEdit={onNodeEdit}
              selectedPath={selectedPath}
              showSensitive={showSensitive}
            />
          ))}
        </div>
      )}
    </div>
  );
}

export default function ConfigTree({
  tree,
  onNodeSelect,
  onNodeEdit,
  selectedPath,
  showSensitive = false,
  onToggleSensitive,
  className = "",
}: ConfigTreeProps) {
  if (!tree || Object.keys(tree).length === 0) {
    return (
      <div className={`config-tree ${className}`}>
        <div className="flex items-center justify-center py-8 text-gray-500 dark:text-gray-400">
          <InformationCircleIcon className="w-5 h-5 mr-2" />
          暂无配置数据
        </div>
      </div>
    );
  }

  return (
    <div
      className={`config-tree bg-card-background rounded-lg border border-border-color ${className}`}
    >
      {/* 工具栏 */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          配置树
        </h3>

        {onToggleSensitive && (
          <button
            type="button"
            onClick={onToggleSensitive}
            className="flex items-center space-x-2 px-3 py-1.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            {showSensitive ? (
              <>
                <EyeSlashIcon className="w-4 h-4" />
                <span>隐藏敏感信息</span>
              </>
            ) : (
              <>
                <EyeIcon className="w-4 h-4" />
                <span>显示敏感信息</span>
              </>
            )}
          </button>
        )}
      </div>

      {/* 树形结构 */}
      <div className="config-tree-content overflow-auto max-h-[calc(100vh-200px)]">
        {Object.entries(tree).map(([key, node]) => (
          <ConfigTreeNodeComponent
            key={key}
            node={node}
            level={0}
            onNodeSelect={onNodeSelect}
            onNodeEdit={onNodeEdit}
            selectedPath={selectedPath}
            showSensitive={showSensitive}
          />
        ))}
      </div>
    </div>
  );
}
