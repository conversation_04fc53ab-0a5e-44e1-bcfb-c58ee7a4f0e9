import type { NextConfig } from "next";

const API_PROXY_TARGET =
  process.env.NODE_ENV === "development"
    ? "http://127.0.0.1:9999"
    : process.env.API_PROXY_TARGET || "http://127.0.0.1:9999";

const nextConfig: NextConfig = {
  reactStrictMode: true,
  /* config options here */
  typescript: {
    // !! 警告：在生产环境中不建议禁用类型检查
    // 由于部署环境问题暂时禁用
    ignoreBuildErrors: false,
  },
  eslint: {
    // 这个选项只会在 `next build` 期间禁用 ESLint，并不会影响您在 VS Code 编辑器中的实时 linting。
    ignoreDuringBuilds: false,
  },
  // 启用图像优化
  images: {
    formats: ["image/avif", "image/webp"],
    remotePatterns: [
      {
        protocol: "https",
        hostname: "img.aliyundrive.com",
        port: "",
        pathname: "/avatar/**",
      },
      {
        protocol: "https",
        hostname: "wxalbum-10001658.image.myqcloud.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "q.qlogo.cn",
        port: "",
        pathname: "/**",
      },
      {
        // 从旧的 next.config.ts 中合并而来
        protocol: "https",
        hostname: "images.unsplash.com",
      },
      {
        protocol: "https",
        hostname: "image.baidu.com",
      },
      {
        protocol: "http",
        hostname: "image.quark.cn",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "image.quark.cn",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "himg.bdimg.com",
      },
      // 友情链接图标支持
      {
        protocol: "https",
        hostname: "github.com",
        pathname: "/favicon.ico",
      },
      {
        protocol: "https",
        hostname: "www.esoua.cn",
        pathname: "/favicon.ico",
      },
      // 无瑕导航图片支持
      {
        protocol: "https",
        hostname: "www.wuxdh.cn",
        pathname: "/wp-content/uploads/**",
      },

      // Boomcatcher图片支持
      {
        protocol: "https",
        hostname: "www.boomcatcher.com",
        pathname: "/wp-content/uploads/**",
      },
      // 通用favicon支持
      {
        protocol: "https",
        hostname: "*.com",
        pathname: "/favicon.ico",
      },
      {
        protocol: "https",
        hostname: "*.cn",
        pathname: "/favicon.ico",
      },
    ],
  },
  // 启用压缩
  compress: true,
  // 添加安全标头
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "X-DNS-Prefetch-Control",
            value: "on",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "Referrer-Policy",
            value: "origin-when-cross-origin",
          },
        ],
      },
    ];
  },
  // 配置API代理
  async rewrites() {
    return [
      {
        source: "/api/:path*",
        destination: `${API_PROXY_TARGET}/api/:path*`,
      },
    ];
  },
};

export default nextConfig;
