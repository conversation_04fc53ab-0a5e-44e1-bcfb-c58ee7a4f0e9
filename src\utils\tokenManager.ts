/**
 * Token管理器 - 处理自动刷新和过期检查
 */

class TokenManager {
  private refreshPromise: Promise<boolean> | null = null;
  private refreshTimer: NodeJS.Timeout | null = null;

  /**
   * 检查Token是否即将过期（5分钟内）
   */
  isTokenExpiringSoon(): boolean {
    if (typeof window === "undefined") {
      return false;
    }

    const expires = localStorage.getItem("auth_expires");
    if (!expires) return false;

    const expiresDate = new Date(expires);
    const now = new Date();
    const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000);

    return expiresDate <= fiveMinutesFromNow;
  }

  /**
   * 检查Token是否已过期
   */
  isTokenExpired(): boolean {
    if (typeof window === "undefined") {
      return false;
    }

    const expires = localStorage.getItem("auth_expires");
    if (!expires) return false;

    const expiresDate = new Date(expires);
    const now = new Date();

    return expiresDate <= now;
  }

  /**
   * 自动刷新Token（防止重复刷新）
   */
  async autoRefreshToken(): Promise<boolean> {
    // 如果已经有刷新请求在进行中，等待其完成
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    // 检查是否需要刷新
    if (!this.isTokenExpiringSoon()) {
      return true;
    }

    console.log("🔄 Token即将过期，开始自动刷新...");

    this.refreshPromise = this.performRefresh();

    try {
      const result = await this.refreshPromise;
      return result;
    } finally {
      this.refreshPromise = null;
    }
  }

  /**
   * 执行Token刷新
   */
  private async performRefresh(): Promise<boolean> {
    try {
      // 延迟导入以避免循环依赖
      const { refreshToken } = await import("../services/authService");
      const result = await refreshToken();

      if (result.success) {
        console.log("✅ Token刷新成功");
        this.scheduleNextRefresh();
        return true;
      } else {
        console.error("❌ Token刷新失败:", result.message);
        this.clearRefreshTimer();
        // 刷新失败时，可以触发重新登录
        this.handleRefreshFailure();
        return false;
      }
    } catch (error) {
      console.error("❌ Token刷新异常:", error);
      this.clearRefreshTimer();
      this.handleRefreshFailure();
      return false;
    }
  }

  /**
   * 安排下次刷新
   */
  private scheduleNextRefresh(): void {
    this.clearRefreshTimer();

    const expires = localStorage.getItem("auth_expires");
    if (!expires) return;

    const expiresDate = new Date(expires);
    const now = new Date();

    // 在过期前10分钟刷新
    const refreshTime = new Date(expiresDate.getTime() - 10 * 60 * 1000);
    const delay = Math.max(0, refreshTime.getTime() - now.getTime());

    console.log(`⏰ 下次Token刷新时间: ${refreshTime.toLocaleString()}`);

    this.refreshTimer = setTimeout(() => {
      this.autoRefreshToken();
    }, delay);
  }

  /**
   * 清除刷新定时器
   */
  private clearRefreshTimer(): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  /**
   * 处理刷新失败
   */
  private handleRefreshFailure(): void {
    // 可以在这里触发全局事件，通知应用需要重新登录
    if (typeof window !== "undefined") {
      window.dispatchEvent(new CustomEvent("auth:refresh-failed"));
    }
  }

  /**
   * 启动Token管理器
   */
  start(): void {
    if (typeof window === "undefined") {
      return;
    }

    // 检查当前Token状态
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return;
    }

    if (this.isTokenExpired()) {
      console.log("🔄 Token已过期，尝试刷新...");
      this.autoRefreshToken();
    } else if (this.isTokenExpiringSoon()) {
      console.log("🔄 Token即将过期，尝试刷新...");
      this.autoRefreshToken();
    } else {
      // 安排下次刷新
      this.scheduleNextRefresh();
    }
  }

  /**
   * 停止Token管理器
   */
  stop(): void {
    this.clearRefreshTimer();
    this.refreshPromise = null;
  }

  /**
   * 重置Token管理器（登录后调用）
   */
  reset(): void {
    this.stop();
    this.start();
  }
}

// 创建全局实例
export const tokenManager = new TokenManager();

// 在浏览器环境中自动启动
if (typeof window !== "undefined") {
  // 页面加载时启动
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", () => {
      tokenManager.start();
    });
  } else {
    tokenManager.start();
  }

  // 页面可见性变化时检查Token
  document.addEventListener("visibilitychange", () => {
    if (!document.hidden) {
      tokenManager.start();
    }
  });
}
