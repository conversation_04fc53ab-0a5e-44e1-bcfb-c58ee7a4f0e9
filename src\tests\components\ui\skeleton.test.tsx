import React from "react";
import { render, screen } from "@testing-library/react";
import { describe, it, expect } from "vitest";
import { Skeleton } from "@/components/ui/skeleton";

describe("Skeleton 组件", () => {
  it("应该正确渲染 Skeleton", () => {
    render(<Skeleton data-testid="skeleton" />);

    const skeleton = screen.getByTestId("skeleton");
    expect(skeleton).toBeInTheDocument();
    expect(skeleton).toHaveClass("animate-pulse", "rounded-md", "bg-muted");
  });

  it("应该支持自定义 className", () => {
    render(
      <Skeleton className="h-4 w-full custom-skeleton" data-testid="skeleton" />
    );

    const skeleton = screen.getByTestId("skeleton");
    expect(skeleton).toHaveClass("h-4", "w-full", "custom-skeleton");
    expect(skeleton).toHaveClass("animate-pulse", "rounded-md", "bg-muted");
  });

  it("应该支持不同的尺寸", () => {
    render(
      <div>
        <Skeleton className="h-4 w-full" data-testid="line-skeleton" />
        <Skeleton
          className="h-12 w-12 rounded-full"
          data-testid="circle-skeleton"
        />
        <Skeleton className="h-32 w-full" data-testid="block-skeleton" />
      </div>
    );

    const lineSkeleton = screen.getByTestId("line-skeleton");
    const circleSkeleton = screen.getByTestId("circle-skeleton");
    const blockSkeleton = screen.getByTestId("block-skeleton");

    expect(lineSkeleton).toHaveClass("h-4", "w-full");
    expect(circleSkeleton).toHaveClass("h-12", "w-12", "rounded-full");
    expect(blockSkeleton).toHaveClass("h-32", "w-full");
  });

  it("应该支持其他 HTML 属性", () => {
    render(
      <Skeleton
        data-testid="skeleton"
        role="status"
        aria-label="加载中"
        style={{ width: "200px" }}
      />
    );

    const skeleton = screen.getByTestId("skeleton");
    expect(skeleton).toHaveAttribute("role", "status");
    expect(skeleton).toHaveAttribute("aria-label", "加载中");
    expect(skeleton).toHaveStyle({ width: "200px" });
  });

  it("应该正确处理动画类", () => {
    render(<Skeleton data-testid="skeleton" />);

    const skeleton = screen.getByTestId("skeleton");
    expect(skeleton).toHaveClass("animate-pulse");
  });

  it("应该支持覆盖默认样式", () => {
    render(
      <Skeleton
        className="bg-gray-200 rounded-lg animate-bounce"
        data-testid="skeleton"
      />
    );

    const skeleton = screen.getByTestId("skeleton");
    expect(skeleton).toHaveClass("bg-gray-200", "rounded-lg", "animate-bounce");
    // 默认的 bg-muted 和 rounded-md 应该被覆盖
    expect(skeleton).toHaveClass("animate-pulse"); // 这个应该仍然存在
  });

  it("应该作为加载状态指示器工作", () => {
    const { rerender } = render(
      <div>
        <Skeleton className="h-4 w-full mb-2" data-testid="title-skeleton" />
        <Skeleton className="h-4 w-3/4 mb-2" data-testid="subtitle-skeleton" />
        <Skeleton className="h-20 w-full" data-testid="content-skeleton" />
      </div>
    );

    expect(screen.getByTestId("title-skeleton")).toBeInTheDocument();
    expect(screen.getByTestId("subtitle-skeleton")).toBeInTheDocument();
    expect(screen.getByTestId("content-skeleton")).toBeInTheDocument();

    // 模拟加载完成后替换为实际内容
    rerender(
      <div>
        <h1>实际标题</h1>
        <p>实际副标题</p>
        <div>实际内容</div>
      </div>
    );

    expect(screen.getByText("实际标题")).toBeInTheDocument();
    expect(screen.getByText("实际副标题")).toBeInTheDocument();
    expect(screen.getByText("实际内容")).toBeInTheDocument();
  });
});
