{"files.autoSave": "after<PERSON>elay", "eslint.codeActionsOnSave.rules": null, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}