'use client';

import { useState, useEffect } from 'react';
import { ArchiveBoxIcon, RocketLaunchIcon } from '@heroicons/react/24/outline';

export default function FooterStats() {
    const [stats, setStats] = useState({
        total: 0,
        yesterday: 0
    });
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // 获取缓存资源统计
        fetch('/api/resource_stats')
            .then(res => res.json())
            .then(data => {
                if (data.status === 'success') {
                    setStats({
                        total: data.total,
                        yesterday: data.yesterday
                    });
                }
            })
            .catch(error => {
                console.error('获取资源统计失败:', error);
            })
            .finally(() => {
                setLoading(false);
            });
    }, []);

    if (loading) return null;

    return (
        <div className="flex flex-wrap justify-center sm:justify-start gap-3 text-xs">
            <div className="flex items-center bg-[color:var(--background)] px-2 py-1 rounded-full shadow-sm">
                <ArchiveBoxIcon className="h-3.5 w-3.5 text-[color:var(--footer-link)] mr-1.5" />
                <span className="text-[color:var(--secondary-text)]">资源总数:</span>
                <span className="font-medium ml-1 text-[color:var(--foreground)]">{stats.total.toLocaleString()}</span>
            </div>
            <div className="flex items-center bg-[color:var(--background)] px-2 py-1 rounded-full shadow-sm">
                <RocketLaunchIcon className="h-3.5 w-3.5 text-[color:var(--footer-link)] mr-1.5" />
                <span className="text-[color:var(--secondary-text)]">昨日新增:</span>
                <span className="font-medium ml-1 text-[color:var(--foreground)]">{stats.yesterday.toLocaleString()}</span>
            </div>
        </div>
    );
} 