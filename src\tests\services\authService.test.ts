/**
 * 认证服务测试用例
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  login, 
  logout, 
  register, 
  refreshToken, 
  isAuthenticated,
  isTokenExpiringSoon,
  getCurrentUser,
  setCachedUserInfo
} from '../../services/authService';

// Mock fetch
global.fetch = vi.fn();

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock tokenManager
vi.mock('../../utils/tokenManager', () => ({
  tokenManager: {
    reset: vi.fn(),
    stop: vi.fn(),
  },
}));

describe('AuthService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('login', () => {
    it('应该成功登录并保存认证信息', async () => {
      const mockResponse = {
        access_token: 'test-access-token',
        refresh_token: 'test-refresh-token',
        expires_in: 3600,
        token_type: 'bearer',
        user: {
          id: 1,
          username: 'testuser',
          email: '<EMAIL>',
          role: { id: '1', name: 'user' },
        },
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await login({
        username: 'testuser',
        password: 'password123',
      });

      expect(result.success).toBe(true);
      expect(result.message).toBe('登录成功');
      expect(result.data?.token).toBe('test-access-token');
      expect(result.data?.user).toEqual(mockResponse.user);

      // 验证localStorage调用
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'auth_token',
        'test-access-token'
      );
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'auth_refresh_token',
        'test-refresh-token'
      );
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'auth_expires',
        expect.any(String)
      );
    });

    it('应该处理登录失败', async () => {
      const mockError = {
        detail: '用户名或密码错误',
      };

      (fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => mockError,
      });

      const result = await login({
        username: 'testuser',
        password: 'wrongpassword',
      });

      expect(result.success).toBe(false);
      expect(result.message).toContain('用户名或密码错误');
    });

    it('应该处理网络错误', async () => {
      (fetch as any).mockRejectedValueOnce(new Error('Network error'));

      const result = await login({
        username: 'testuser',
        password: 'password123',
      });

      expect(result.success).toBe(false);
      expect(result.message).toContain('网络错误');
    });
  });

  describe('logout', () => {
    it('应该成功登出并清除本地存储', async () => {
      localStorageMock.getItem.mockReturnValue('test-token');
      
      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      });

      const result = await logout();

      expect(result.success).toBe(true);
      expect(result.message).toBe('登出成功');

      // 验证清除localStorage
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_token');
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_refresh_token');
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_expires');
    });

    it('应该在没有token时直接清除本地存储', async () => {
      localStorageMock.getItem.mockReturnValue(null);

      const result = await logout();

      expect(result.success).toBe(true);
      expect(result.message).toBe('已清除本地登录状态');
      expect(fetch).not.toHaveBeenCalled();
    });
  });

  describe('register', () => {
    it('应该成功注册', async () => {
      const mockResponse = {
        message: '注册成功，请查收验证邮件',
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await register({
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        confirm_password: 'password123',
        agree_terms: true,
      });

      expect(result.success).toBe(true);
      expect(result.message).toBe('注册成功，请查收验证邮件');
    });

    it('应该处理注册失败', async () => {
      const mockError = {
        detail: '邮箱已被注册',
      };

      (fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => mockError,
      });

      const result = await register({
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        confirm_password: 'password123',
        agree_terms: true,
      });

      expect(result.success).toBe(false);
      expect(result.message).toContain('邮箱已被注册');
    });
  });

  describe('refreshToken', () => {
    it('应该成功刷新token', async () => {
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'auth_refresh_token') return 'test-refresh-token';
        return null;
      });

      const mockResponse = {
        access_token: 'new-access-token',
        expires_in: 3600,
        token_type: 'bearer',
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await refreshToken();

      expect(result.success).toBe(true);
      expect(result.message).toBe('令牌刷新成功');
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'auth_token',
        'new-access-token'
      );
    });

    it('应该在没有refresh token时返回失败', async () => {
      localStorageMock.getItem.mockReturnValue(null);

      const result = await refreshToken();

      expect(result.success).toBe(false);
      expect(result.message).toBe('没有刷新令牌，请重新登录');
    });
  });

  describe('isAuthenticated', () => {
    it('应该在有有效token时返回true', () => {
      const futureDate = new Date(Date.now() + 3600000).toISOString();
      
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'auth_token') return 'test-token';
        if (key === 'auth_expires') return futureDate;
        return null;
      });

      expect(isAuthenticated()).toBe(true);
    });

    it('应该在token过期时返回false', () => {
      const pastDate = new Date(Date.now() - 3600000).toISOString();
      
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'auth_token') return 'test-token';
        if (key === 'auth_expires') return pastDate;
        return null;
      });

      expect(isAuthenticated()).toBe(false);
    });

    it('应该在没有token时返回false', () => {
      localStorageMock.getItem.mockReturnValue(null);

      expect(isAuthenticated()).toBe(false);
    });
  });

  describe('isTokenExpiringSoon', () => {
    it('应该在token即将过期时返回true', () => {
      const soonDate = new Date(Date.now() + 2 * 60 * 1000).toISOString(); // 2分钟后
      
      localStorageMock.getItem.mockReturnValue(soonDate);

      expect(isTokenExpiringSoon()).toBe(true);
    });

    it('应该在token还有很长时间才过期时返回false', () => {
      const futureDate = new Date(Date.now() + 30 * 60 * 1000).toISOString(); // 30分钟后
      
      localStorageMock.getItem.mockReturnValue(futureDate);

      expect(isTokenExpiringSoon()).toBe(false);
    });
  });
});
