/**
 * 个人信息管理服务测试用例
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  getMyProfile,
  updateMyProfile,
  changeEmail,
  verifyEmailChange,
  uploadAvatar,
  changePassword,
  changeNickname,
  getPointsHistory,
  getMyHelpRequests,
  getMyHelpAnswers,
  getProfileStatistics,
  getActivitySummary
} from '../../services/profileService';

// Mock fetch
global.fetch = vi.fn();

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('ProfileService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue('test-token');
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('getMyProfile', () => {
    it('应该成功获取个人信息', async () => {
      const mockProfile = {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        nickname: 'Test User',
        avatar: 'https://example.com/avatar.jpg',
        status: 'active',
        email_verified: true,
        role: { id: '1', name: 'user' },
        points: 100,
        title: '新手',
        created_at: '2023-01-01T00:00:00Z',
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockProfile,
      });

      const result = await getMyProfile();

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockProfile);
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/profile/me'),
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            Authorization: 'Bearer test-token',
          }),
        })
      );
    });

    it('应该在未登录时返回错误', async () => {
      localStorageMock.getItem.mockReturnValue(null);

      const result = await getMyProfile();

      expect(result.success).toBe(false);
      expect(result.message).toBe('未登录，请先登录');
      expect(fetch).not.toHaveBeenCalled();
    });

    it('应该处理API错误', async () => {
      (fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({ detail: '用户不存在' }),
      });

      const result = await getMyProfile();

      expect(result.success).toBe(false);
      expect(result.message).toContain('用户不存在');
    });
  });

  describe('updateMyProfile', () => {
    it('应该成功更新个人信息', async () => {
      const updateData = { nickname: 'New Nickname' };
      const mockResponse = { message: '更新成功' };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await updateMyProfile(updateData);

      expect(result.success).toBe(true);
      expect(result.message).toBe('更新个人信息成功');
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/profile/me'),
        expect.objectContaining({
          method: 'PUT',
          body: JSON.stringify(updateData),
        })
      );
    });
  });

  describe('changeEmail', () => {
    it('应该成功发送邮箱修改请求', async () => {
      const emailData = {
        new_email: '<EMAIL>',
        password: 'password123',
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ message: '验证邮件已发送' }),
      });

      const result = await changeEmail(emailData);

      expect(result.success).toBe(true);
      expect(result.message).toContain('验证邮件');
    });

    it('应该处理邮箱已存在的错误', async () => {
      const emailData = {
        new_email: '<EMAIL>',
        password: 'password123',
      };

      (fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => ({ detail: 'email already exists' }),
      });

      const result = await changeEmail(emailData);

      expect(result.success).toBe(false);
      expect(result.message).toContain('邮箱已被其他用户使用');
    });
  });

  describe('uploadAvatar', () => {
    it('应该成功上传头像', async () => {
      const file = new File(['test'], 'avatar.jpg', { type: 'image/jpeg' });
      Object.defineProperty(file, 'size', { value: 1024 * 1024 }); // 1MB

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          message: '头像上传成功',
          avatar_url: 'https://example.com/new-avatar.jpg'
        }),
      });

      const result = await uploadAvatar({ file });

      expect(result.success).toBe(true);
      expect(result.message).toBe('头像上传成功');
    });

    it('应该验证文件大小', async () => {
      const file = new File(['test'], 'avatar.jpg', { type: 'image/jpeg' });
      Object.defineProperty(file, 'size', { value: 3 * 1024 * 1024 }); // 3MB

      const result = await uploadAvatar({ file });

      expect(result.success).toBe(false);
      expect(result.message).toContain('文件过大');
    });

    it('应该验证文件类型', async () => {
      const file = new File(['test'], 'avatar.txt', { type: 'text/plain' });
      Object.defineProperty(file, 'size', { value: 1024 });

      const result = await uploadAvatar({ file });

      expect(result.success).toBe(false);
      expect(result.message).toContain('格式不支持');
    });
  });

  describe('changeNickname', () => {
    it('应该成功修改昵称', async () => {
      const nicknameData = {
        nickname: 'NewNickname',
        reason: '测试修改',
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ message: '昵称修改成功' }),
      });

      const result = await changeNickname(nicknameData);

      expect(result.success).toBe(true);
      expect(result.message).toBe('昵称修改成功');
    });

    it('应该验证昵称长度', async () => {
      const nicknameData = {
        nickname: 'a', // 太短
      };

      const result = await changeNickname(nicknameData);

      expect(result.success).toBe(false);
      expect(result.message).toContain('长度不能少于2个字符');
    });

    it('应该验证昵称字符', async () => {
      const nicknameData = {
        nickname: 'test@nickname', // 包含非法字符
      };

      const result = await changeNickname(nicknameData);

      expect(result.success).toBe(false);
      expect(result.message).toContain('只能包含');
    });
  });

  describe('changePassword', () => {
    it('应该成功修改密码', async () => {
      const passwordData = {
        old_password: 'oldpassword',
        new_password: 'newpassword123',
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ message: '密码修改成功' }),
      });

      const result = await changePassword(passwordData);

      expect(result.success).toBe(true);
      expect(result.message).toBe('密码修改成功');
    });

    it('应该处理旧密码错误', async () => {
      const passwordData = {
        old_password: 'wrongpassword',
        new_password: 'newpassword123',
      };

      (fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => ({ detail: 'old_password_incorrect' }),
      });

      const result = await changePassword(passwordData);

      expect(result.success).toBe(false);
      expect(result.message).toContain('原密码错误');
    });
  });

  describe('getPointsHistory', () => {
    it('应该成功获取积分历史', async () => {
      const mockHistory = {
        data: {
          items: [
            {
              id: 1,
              points: 10,
              transaction_type: 'earn',
              description: '回答问题',
              created_at: '2023-01-01T00:00:00Z',
            },
          ],
          total: 1,
          page: 1,
          size: 20,
          total_pages: 1,
        },
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockHistory,
      });

      const result = await getPointsHistory(1, 20);

      expect(result.success).toBe(true);
      expect(result.data.items).toHaveLength(1);
      expect(result.data.items[0].points).toBe(10);
    });
  });

  describe('getProfileStatistics', () => {
    it('应该成功获取统计信息', async () => {
      const mockStats = {
        data: {
          total_help_requests: 5,
          total_help_answers: 10,
          accepted_answers: 3,
          total_points: 150,
          current_title: '活跃用户',
        },
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      });

      const result = await getProfileStatistics();

      expect(result.success).toBe(true);
      expect(result.data.total_help_requests).toBe(5);
      expect(result.data.total_help_answers).toBe(10);
    });
  });

  describe('网络错误处理', () => {
    it('应该处理网络错误', async () => {
      (fetch as any).mockRejectedValueOnce(new Error('Network error'));

      const result = await getMyProfile();

      expect(result.success).toBe(false);
      expect(result.message).toContain('网络错误');
    });
  });
});
