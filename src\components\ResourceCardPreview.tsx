"use client";

import { memo } from "react";

interface ResourceCardPreviewProps {
  textContent?: string;
}

/**
 * 资源卡片预览组件
 * 用于展示资源预览内容
 */
const ResourceCardPreview = memo(function ResourceCardPreview({
  textContent,
}: ResourceCardPreviewProps) {
  if (!textContent) {
    return null;
  }

  return (
    <div className="mb-4 p-3 bg-gray-50 rounded-md border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
      <h4 className="text-sm font-medium mb-2 text-gray-700 dark:text-white">
        资源预览
      </h4>
      <p className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-line line-clamp-4">
        {textContent}
      </p>
    </div>
  );
});

export default ResourceCardPreview;
